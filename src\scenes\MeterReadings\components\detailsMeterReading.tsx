import * as React from "react";
import "../../scenes.less";
import { Form } from "@ant-design/compatible";
import "@ant-design/compatible/assets/index.css";
import { Button, Input, Modal } from "antd";
import FormItem from "antd/lib/form/FormItem";
import moment, { Moment } from "moment";
import ComponentLayout from "../../../components/Layout/ComponentLayout";
import { L } from "../../../lib/abpUtility";
import { DateTimeFormat } from "../../ViewSettingsConsts";
import { ModalFormComponentProps } from "../../modalFormComponentProps";

export interface IDetailsMeterReadingProps
	extends Omit<ModalFormComponentProps, "onCreate" | "roles"> {}

class DetailsMeterReading extends React.Component<IDetailsMeterReadingProps> {
	normalizeAll = (date: Moment) => {
		const normalizedData: string = moment(date).format(DateTimeFormat.fullDate);
		return normalizedData;
	};

	state = {
		confirmDirty: false,
	};

	render() {
		const { getFieldDecorator } = this.props.form;
		const { visible, onCancel } = this.props;
		return (
			<Modal
				visible={visible}
				okText={L("OK")}
				onOk={onCancel}
				onCancel={onCancel}
				title={L("Details")}
				footer={[
					<Button key="back" type="primary" onClick={onCancel}>
						{L("OK")}
					</Button>,
				]}
			>
				<FormItem
					label={L("Reason For Event")}
					{...ComponentLayout.formItemLayout}
				>
					{getFieldDecorator("reasonForEvent")(
						<Input className={"disabled-input"} disabled />,
					)}
				</FormItem>
				<FormItem
					label={L("Meter Reading")}
					{...ComponentLayout.formItemLayout}
				>
					{getFieldDecorator("value")(
						<Input className={"disabled-input"} disabled />,
					)}
				</FormItem>
				<FormItem label={L("Fuel Type")} {...ComponentLayout.formItemLayout}>
					{getFieldDecorator("fuelType")(
						<Input className={"disabled-input"} disabled />,
					)}
				</FormItem>
				<FormItem label={L("Scrubber")} {...ComponentLayout.formItemLayout}>
					{getFieldDecorator("scrubber", {
						initialValue: false, // domyślna wartość false
						normalize: (value) => (value ? "Yes" : "No"), // zmiana wartości na Yes lub No
					})(<Input className={"disabled-input"} disabled />)}
				</FormItem>
				<FormItem
					label={L("Creation Time")}
					{...ComponentLayout.formItemLayout}
				>
					{getFieldDecorator("creationTime", {
						normalize(value) {
							return moment(value).format(DateTimeFormat.fullDate);
						},
					})(<Input className={"disabled-input"} disabled />)}
				</FormItem>
				<FormItem label={L("Meter Name")} {...ComponentLayout.formItemLayout}>
					{getFieldDecorator("meterName")(
						<Input className={"disabled-input"} disabled />,
					)}
				</FormItem>
				<FormItem label={L("Ship Name")} {...ComponentLayout.formItemLayout}>
					{getFieldDecorator("shipName")(
						<Input className={"disabled-input"} disabled />,
					)}
				</FormItem>
				<FormItem label={L("IMO")} {...ComponentLayout.formItemLayout}>
					{getFieldDecorator("imoNumber")(
						<Input className={"disabled-input"} disabled />,
					)}
				</FormItem>
				<FormItem label={L("Meter Type")} {...ComponentLayout.formItemLayout}>
					{getFieldDecorator("meterType")(
						<Input className={"disabled-input"} disabled />,
					)}
				</FormItem>
				<FormItem
					label={L("Unit Of Measure")}
					{...ComponentLayout.formItemLayout}
				>
					{getFieldDecorator("unitOfMeasure")(
						<Input className={"disabled-input"} disabled />,
					)}
				</FormItem>
				<FormItem label={L("Digits Count")} {...ComponentLayout.formItemLayout}>
					{getFieldDecorator("digitsCount")(
						<Input className={"disabled-input"} disabled />,
					)}
				</FormItem>
				<FormItem
					label={L("Decimal Point Position")}
					{...ComponentLayout.formItemLayout}
				>
					{getFieldDecorator("decimalPointPosition")(
						<Input className={"disabled-input"} disabled />,
					)}
				</FormItem>
				<FormItem
					label={L("Flow Direction")}
					{...ComponentLayout.formItemLayout}
				>
					{getFieldDecorator("flowDirection")(
						<Input className={"disabled-input"} disabled />,
					)}
				</FormItem>
				<FormItem
					label={L("Measurement Type")}
					{...ComponentLayout.formItemLayout}
				>
					{getFieldDecorator("measurementType")(
						<Input className={"disabled-input"} disabled />,
					)}
				</FormItem>
			</Modal>
		);
	}
}

export default Form.create<IDetailsMeterReadingProps>()(DetailsMeterReading);
