import { action, observable } from "mobx";

import { EntityDto } from "../services/dto/entityDto";
import type { PagedResultDto } from "../services/dto/pagedResultDto";
import type { PagedShipResultRequestDto } from "../services/ship/dto/PagedShipResultRequestDto";
import type { CreateOrUpdateShipInput } from "../services/ship/dto/createOrUpdateShipInput";
import { GetShipOutput } from "../services/ship/dto/getShipOutput";
import type { UpdateShipInput } from "../services/ship/dto/updateShipInput";
import shipService from "../services/ship/shipService";

class ShipStore {
	@observable ships!: PagedResultDto<GetShipOutput>;
	@observable editShip!: CreateOrUpdateShipInput;
	@observable allShips!: GetShipOutput[];
	@observable allShipNames!: GetShipOutput[];
	@observable scrubber!: { [key: string]: string };
	@observable filters: string[] = [];

	@action
	async create(createShipInput: CreateOrUpdateShipInput) {
		const result = await shipService.create(createShipInput);
		this.ships.items.push(result);
	}

	@action
	async update(updateShipInput: UpdateShipInput) {
		const result = await shipService.update(updateShipInput);
		this.ships.items = this.ships.items.map((x: GetShipOutput) => {
			if (x.id === updateShipInput.id) x = result;
			return x;
		});
	}

	@action
	async delete(entityDto: EntityDto) {
		await shipService.delete(entityDto);
		this.ships.items = this.ships.items.filter(
			(x: GetShipOutput) => x.id !== entityDto.id,
		);
	}

	@action
	async get(entityDto: EntityDto) {
		const result = await shipService.get(entityDto);
		this.editShip = result;
	}

	@action
	async createShip() {
		this.editShip = {
			id: 0,
			shipName: "",
			mmsi: "",
			cargoUnit: "",
			type: "",
			sdwt: 0,
			gt: 0,
			imoNumber: "",
			shipEmail: "",
			captainName: "",
			chiefEngineersName: "",
			engineRoomUnmannedHoursFrom: [],
			engineRoomUnmannedHoursTo: [],
			scrubber: "",
			settings: {
				arrivalAndAnchorage: false,
				readMetersOnFAOP: false,
				enteringECA: false,
				noonReportAt6: false,
				noonReportAt12: false,
				noonReportAt18: false,
			},
		};
	}

	@action
	async getAll(pagedFilterAndSortedRequest: PagedShipResultRequestDto) {
		const result = await shipService.getAll(pagedFilterAndSortedRequest);
		this.ships = result;
	}

	/**
	 * @deprecated
	 * Use getShipNames method instead
	 */
	@action
	async getAllWithIdAndName() {
		const result = await shipService.getAllWithIdAndName();
		this.allShips = result?.items;
	}

	/**
	 * Access response through allShipNames prop
	 */
	@action
	async getShipNames() {
		const result = await shipService.getShipNames();
		this.allShipNames = result;
	}

	@action
	async getFilters(
		pagedFilterAndSortedRequest: { keyword: string; searchColumn: string },
		property: string,
	) {
		const result = await shipService.getFilters(
			pagedFilterAndSortedRequest,
			property,
		);
		this.filters = result;
	}
}

export default ShipStore;
