import { Button, Table } from "antd";
import { inject, observer } from "mobx-react";
import moment from "moment";
import React from "react";
import { L } from "../../../lib/abpUtility";
import fileService from "../../../services/azure/fileService";
import { EntityDto } from "../../../services/dto/entityDto";
import { OcrDto } from "../../../services/ocr/dTo/ocrDto";
import OcrStore from "../../../stores/ocrStore";
import Stores from "../../../stores/storeIdentifier";
import { renderDate } from "../../renderUtils";
import { TableRowSelection } from "antd/lib/table/interface";

// const { Option } = Select;

interface IBeforeOcrTableProps {
	ocrStore: OcrStore;
	ocr: any;
	analyzeFile: (ocrStore: any) => Promise<void>;
	denyFile: (ocrStore: any) => Promise<void>;
}

@inject(Stores.OcrStore)
@observer
class BeforeOcrTable extends React.Component<IBeforeOcrTableProps> {
	ocrStore: OcrStore = this.props.ocrStore;
	fileUploadInputRef: any;
	selectedFilesForUpload: any = {
		names: [] as string[],
		scrs: [] as string[],
	};

	state = {
		isModalVisible: false,
		selectedFiles: [],
		selectedRows: {
			items: [],
		},
		status: 0,
	};

	constructor(props: any) {
		super(props);
		this.fileUploadInputRef = React.createRef();
		this.state = {
			isModalVisible: false,
			selectedFiles: [],
			selectedRows: {
				items: [],
			},
			status: 0,
		};
	}

	showModal = () => {
		this.setState({ isModalVisible: true });
	};

	handleOk = () => {
		this.setState({ isModalVisible: false });
	};

	handleCancel = () => {
		this.setState({ isModalVisible: false });
	};

	handleFileChange = (event: any) => {
		this.setState({ selectedFiles: event.target.files });
	};

	triggerUpload = () => {
		this.fileUploadInputRef.current.click();
	};

	handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
		e.persist();
		const selectedFiles = this.fileUploadInputRef.current.files;
		if (!selectedFiles || selectedFiles.length === 0) {
			return;
		}

		this.selectedFilesForUpload.names = [];
		this.selectedFilesForUpload.srcs = [];

		const tempOcrUrls: Pick<OcrDto, "fileName" | "fileUrl">[] = [];

		for (let i = 0; i < selectedFiles.length; i++) {
			const currentFile = selectedFiles[i];
			const result = await fileService.uploadFileOCR(currentFile);

			tempOcrUrls.push({
				fileName: currentFile.name,
				fileUrl: result,
			});
		}

		this.setState({ isModalVisible: false });
		await this.props.ocrStore.createMany(tempOcrUrls);
		this.resetFileInput();
		await this.props.ocrStore.getAllByStatus({
			status: this.state.status,
		});
	};
	resetFileInput = () => {
		if (this.fileUploadInputRef?.current) {
			this.fileUploadInputRef.current.value = "";
		}
	};

	public render() {
		const columns = [
			{
				title: L("CreationTime"),
				dataIndex: "creationTime",
				key: "creationTime",
				width: 150,
				sorter: (a: OcrDto, b: OcrDto) =>
					moment(a.creationTime).diff(moment(b.creationTime)),
				render: (text: string, rekord: OcrDto, index: number) =>
					renderDate(text),
			},
			{
				title: L("LastModificationTime"),
				dataIndex: "lastModificationTime",
				key: "lastModificationTime",
				width: 150,
				sorter: (a: OcrDto, b: OcrDto) =>
					moment(a.lastModificationTime).diff(moment(b.lastModificationTime)),
				render: (text: string, rekord: OcrDto, index: number) =>
					renderDate(text),
			},
			{
				title: L("FileName"),
				dataIndex: "fileName",
				key: "fileName",
				width: 150,
			},
			{
				title: L("Status"),
				dataIndex: "status",
				key: "status",
				width: 150,
			},
		];

		const { selectedRows } = this.state;
		const rowSelection: TableRowSelection<OcrDto> = {
			onChange: (selectedRowKeys, selectedRows: OcrDto[]) => {
				const selectedRowsItems: EntityDto[] = selectedRows.map((x) => {
					return {
						id: x.id,
					};
				});
				this.setState({
					selectedRows: {
						items: selectedRowsItems,
					},
				});
			},
		};

		const hasSelected = selectedRows.items.length > 0;

		// let documentTypes = [] ? [] : [];

		return (
			<>
				<div className="ocr-header__buttons">
					<Button type="primary" onClick={this.triggerUpload} disabled={false}>
						{L("Add File")}
					</Button>
					<Button
						type="primary"
						onClick={() => this.props.denyFile(selectedRows.items)}
						disabled={!hasSelected}
					>
						{L("Deny File")}
					</Button>
					<Button
						type="primary"
						onClick={() => this.props.analyzeFile(selectedRows.items)}
						disabled={!hasSelected}
					>
						{L("Analyze File")}
					</Button>
				</div>
				<Table
					bordered={true}
					rowKey={(record: OcrDto) => record.id.toString()}
					columns={columns}
					dataSource={this.props.ocr ? this.props.ocr : []}
					rowSelection={rowSelection}
				/>

				<input
					type="file"
					title={L("Add File")}
					accept=".pdf"
					ref={this.fileUploadInputRef}
					multiple
					onChange={async (e) => await this.handleFileUpload(e)}
					style={{ display: "none" }}
				/>
			</>
		);
	}
}

export default BeforeOcrTable;
