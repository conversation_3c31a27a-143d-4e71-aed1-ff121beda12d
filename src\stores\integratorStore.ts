import { action, observable } from "mobx";
import type { PagedFilterAndSortedRequest } from "../services/dto/pagedFilterAndSortedRequest";
import type { PagedResultDto } from "../services/dto/pagedResultDto";
import { GetIntegratorOutput } from "../services/integrator/dto/getIntegratorOutput";
import integratorService from "../services/integrator/integratorService";

class IntegratorStore {
	@observable integrators!: PagedResultDto<GetIntegratorOutput>;

	@action
	async getAll(pagedFilterAndSortedRequest: PagedFilterAndSortedRequest) {
		const result = await integratorService.getAll(pagedFilterAndSortedRequest);
		this.integrators = result;
	}
}

export default IntegratorStore;
