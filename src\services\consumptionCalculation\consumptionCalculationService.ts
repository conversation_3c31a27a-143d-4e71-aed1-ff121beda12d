import Endpoint from "../endpoints";
import http from "../httpService";
import {
	CalculationResultAutoInput,
	ConsumptionCalculationDtos,
	ConsumptionConfigurationDto,
} from "./dto/calculationResultAutoInput";

class ConsumptionCalculationService {
	public async calculateResults(
		input: CalculationResultAutoInput,
	): Promise<ConsumptionCalculationDtos> {
		const result = await http.post(
			Endpoint.ConsumptionCalculation.CalculateResults,
			input,
		);
		return result.data.result;
	}

	public async saveAutoCalculationResults(
		calculationResults: ConsumptionCalculationDtos,
	): Promise<ConsumptionCalculationDtos> {
		const result = await http.post(
			Endpoint.ConsumptionCalculation.SaveAutoCalculationResults,
			calculationResults,
		);
		return result.data.result;
	}

	public async getConfig(): Promise<ConsumptionConfigurationDto> {
		const result = await http.get(Endpoint.ConsumptionCalculation.GetConfig);
		return result.data.result;
	}

	public async setGroupingPeriod(
		groupingPeriodInMinutes: number,
	): Promise<ConsumptionConfigurationDto> {
		const result = await http.post(
			Endpoint.ConsumptionCalculation.SetGroupingPeriod,
			groupingPeriodInMinutes,
			{
				headers: { "Content-type": "application/json" },
			},
		);
		return result.data.result;
	}
}

export default new ConsumptionCalculationService();
