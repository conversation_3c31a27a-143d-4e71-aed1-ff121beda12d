import { EntityDto } from "../dto/entityDto";
import { PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoints";
import http from "../httpService";
import { CreateOrUpdateNotificationsInput } from "./dto/CreateOrUpdateNotificationsInput";
import { PagedFilterAndSortedRequestNotifications } from "./dto/PagedFilterAndSortedRequestNotifications";
import { ShipTrace } from "./dto/ShipTrace";
import { UpdateCargoInput } from "./dto/UpdateCargoInput";
import { UpdateNotificationsInput } from "./dto/UpdateNotificationsInput";

class NotificationsService {
	public async getNotificationsSummary(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequestNotifications,
		shipId: number,
		fromLastDays?: number,
	) {
		const result = await http.get(
			Endpoint.Notifications.GetNotificationsSummary,
			{ params: { ...pagedFilterAndSortedRequest, shipId, fromLastDays } },
		);
		return result.data.result;
	}

	public async getTitles(shipId?: number, fromLastDays?: number) {
		const result = await http.get(Endpoint.Notifications.GetTitles, {
			params: { shipId, fromLastDays },
		});
		return result.data.result;
	}

	public async create(createEventsInput: CreateOrUpdateNotificationsInput) {
		const result = await http.post(
			Endpoint.Notifications.Create,
			createEventsInput,
		);
		return result.data.result;
	}

	public async update(updateNotifications: UpdateNotificationsInput) {
		const result = await http.put(
			Endpoint.Notifications.Update,
			updateNotifications,
		);
		return result.data.result;
	}

	public async updateCargo(updateCargo: UpdateCargoInput) {
		const result = await http.put(
			Endpoint.Notifications.UpdateCargo,
			updateCargo,
		);
		return result.data.result;
	}

	public async delete(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.Notifications.Delete, {
			params: entityDto,
		});
		return result.data;
	}

	public async get(
		entityDto: EntityDto,
	): Promise<CreateOrUpdateNotificationsInput> {
		const result = await http.get(Endpoint.Notifications.Get, {
			params: entityDto,
		});
		return result.data.result;
	}

	public async getAll(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequestNotifications,
	): Promise<PagedResultDto<CreateOrUpdateNotificationsInput>> {
		const result = await http.get(Endpoint.Notifications.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}

	public async getShipTrace(eventGuid: string): Promise<ShipTrace[]> {
		const result = await http.get(Endpoint.Notifications.ShipTrace, {
			params: { eventGuid },
		});
		return result.data.result;
	}

	public async getFilters(
		filters: { keyword: string; searchColumn: string },
		property: string,
		shipId: number,
		fromLastDays?: number,
	): Promise<string[]> {
		const result = await http.get(Endpoint.Notifications.Filters, {
			params: { ...filters, propertyName: property, shipId, fromLastDays },
		});
		return result.data.result;
	}
}

export default new NotificationsService();
