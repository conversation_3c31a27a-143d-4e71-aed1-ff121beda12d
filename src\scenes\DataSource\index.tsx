import { SearchOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Card, Col, Input, Row, Table } from "antd";
import { ColumnProps, TablePaginationConfig } from "antd/lib/table";
import { inject, observer } from "mobx-react";
import * as React from "react";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import { L } from "../../lib/abpUtility";
import { CreateOrUpdateDataSourceInput } from "../../services/dataSource/dto/CreateOrUpdateDataSourceInput";
import { GetDataSourceOutput } from "../../services/dataSource/dto/GetDataSourceOutput";
import { EntityDto } from "../../services/dto/entityDto";
import { ListResultDto } from "../../services/dto/pagedResultDto";
import DataSourceStore from "../../stores/dataSourceStore";
import Stores from "../../stores/storeIdentifier";
import {
	getTablePaginationOptions,
	renderDate,
	renderSearchIcon,
} from "../renderUtils";
import { TableRowSelection } from "antd/lib/table/interface";

export interface IDataSourceProps {
	dataSourceStore: DataSourceStore;
}

export interface IDataSourceState {
	maxResultCount: number;
	skipCount: number;
	sorting: string;
	searchTextInsideTable: Array<{ index: string; searchText: string }>;
	searchTextTable: string;
	searchedColumnTable: string;
	selectedRows: ListResultDto<EntityDto>;
	searchColumn: string;
	loading: boolean;
}

@inject(Stores.DataSourceStore)
@observer
class DataSource extends AppComponentBase<IDataSourceProps, IDataSourceState> {
	state = {
		maxResultCount: 10,
		skipCount: 0,
		sorting: "",
		searchTextInsideTable: [{ index: "", searchText: "" }],
		searchTextTable: "",
		searchedColumnTable: "",
		selectedRows: {
			items: [],
		},
		searchColumn: "",
		loading: false,
	};

	async componentDidMount() {
		await this.getAll();
	}

	async getAll() {
		this.setState({ loading: true });
		await this.props.dataSourceStore.getAll({
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: this.state.sorting,
			searchColumn: this.state.searchColumn,
		});
		this.setState({ loading: false });
	}

	getColumnsSearchProps = (
		dataIndex: keyof GetDataSourceOutput,
	): ColumnProps<GetDataSourceOutput> => ({
		filterDropdown: ({
			setSelectedKeys,
			selectedKeys,
			confirm,
			clearFilters,
		}) => {
			return (
				<div style={{ padding: 8 }}>
					<Input
						autoFocus
						placeholder={`Search ${dataIndex}`}
						value={selectedKeys ? selectedKeys[0] : ""}
						onChange={(e) => {
							if (setSelectedKeys)
								setSelectedKeys(e.target.value ? [e.target.value] : []);
						}}
						onPressEnter={() =>
							this.handleSearchTable(selectedKeys || [], dataIndex, confirm)
						}
						style={{ width: 188, marginBottom: 8, display: "block" }}
					/>
					<Button
						type="primary"
						onClick={() =>
							this.handleSearchTable(selectedKeys || [], dataIndex, confirm)
						}
						icon={<SearchOutlined />}
						size="small"
						style={{ width: 90, marginRight: 8 }}
					>
						Search
					</Button>
					<Button
						onClick={() => this.handleReset(dataIndex, clearFilters)}
						size="small"
						style={{ width: 90 }}
					>
						Reset
					</Button>
				</div>
			);
		},
		filterIcon: renderSearchIcon,
		onFilter: (value, record) => {
			if (record[dataIndex] != null && record[dataIndex] !== "") {
				return record[dataIndex]
					.toString()
					.toLowerCase()
					.includes(value.toString().toLowerCase());
			}
			return false;
		},
	});

	handleTableChange = (pagination: TablePaginationConfig) => {
		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
			},
			async () => await this.getAll(),
		);
	};

	handleSearchTable = (
		selectedKeys: React.Key[],
		dataIndex: string,
		confirm?: () => void,
	) => {
		const allSearches = this.state.searchTextInsideTable;
		const filtred = allSearches.filter((x) => x.index !== dataIndex);

		filtred.push({ index: dataIndex, searchText: selectedKeys[0].toString() });
		if (confirm) confirm();
		this.setState({
			searchTextTable: selectedKeys[0].toString(),
			searchedColumnTable: dataIndex,
			searchTextInsideTable: filtred,
		});
	};

	handleReset = (dataIndex: string, clearFilters?: () => void) => {
		const allSearches = this.state.searchTextInsideTable;
		const filtred = allSearches.filter((x) => x.index !== dataIndex);
		if (clearFilters) clearFilters();
		this.setState({
			searchTextTable: "",
			searchTextInsideTable: filtred,
		});
	};

	public render() {
		const { dataSources } = this.props.dataSourceStore;
		const paginationOptions = getTablePaginationOptions(
			dataSources?.totalCount,
		);
		const columns = [
			{
				title: L("Timestamp"),
				dataIndex: "timestamp",
				key: "timestamp",
				width: 150,
				render: (text: string) => renderDate(text),
				sorter: (a: GetDataSourceOutput, b: GetDataSourceOutput) =>
					new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime(),
				...this.getColumnsSearchProps("timestamp"),
			},
			{
				title: L("Provider Id"),
				dataIndex: "providerId",
				key: "providerId",
				width: 150,
				sorter: (a: GetDataSourceOutput, b: GetDataSourceOutput) =>
					a.providerId - b.providerId,
				...this.getColumnsSearchProps("providerId"),
			},
			{
				title: L("Provider Type"),
				dataIndex: "providerType",
				key: "providerType",
				width: 150,
				sorter: (a: GetDataSourceOutput, b: GetDataSourceOutput) =>
					a.providerType?.length - b.providerType?.length,
			},
			{
				title: L("Payload"),
				dataIndex: "payload",
				key: "payload",
				width: 1000,
			},
		];

		const rowSelection: TableRowSelection<GetDataSourceOutput> = {
			fixed: true,
			columnWidth: 60,
			onChange: (_, selectedRows: GetDataSourceOutput[]) => {
				const selectedRowsItems: EntityDto[] = selectedRows.map((x) => {
					return {
						id: x.id,
					};
				});
				this.setState({
					selectedRows: {
						items: selectedRowsItems,
					},
				});
			},
		};

		return (
			<Card>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey={(record: CreateOrUpdateDataSourceInput) =>
								record.id.toString()
							}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={this.state.loading}
							dataSource={dataSources === undefined ? [] : dataSources.items}
							onChange={this.handleTableChange}
							rowSelection={rowSelection}
							scroll={{ x: true, y: 400 }}
						/>
					</Col>
				</Row>
				<Chat />
			</Card>
		);
	}
}

export default DataSource;
