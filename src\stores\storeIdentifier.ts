export default class Stores {
	static AuthenticationStore = "authenticationStore";
	static RoleStore = "roleStore";
	static OcrStore = "ocrStore";
	static TenantStore = "tenantStore";
	static UserStore = "userStore";
	static SessionStore = "sessionStore";
	static AccountStore = "accountStore";
	static MeterReadingStore = "meterReadingStore";
	static MeterConfigurationStore = "meterConfigurationStore";
	static MeterReadingSetStore = "meterReadingSetStore";
	static ShipStore = "shipStore";
	static ShipInfoStore = "shipInfoStore";
	static ShipRentalStore = "shipRentalStore";
	static CargoStore = "cargoStore";
	static PassagesStore = "passagesStore";
	static DataSourceStore = "dataSourceStore";
	static DataProviderStore = "dataProviderStore";
	static EventsStore = "eventsStore";
	static NotificationsStore = "notificationsStore";
	static AnswersForEventsStore = "answersForEventsStore";
	static MeterStore = "meterStore";
	static VoyageStore = "voyageStore";
	static DeviceStore = "deviceStore";
	static MailRecipientStore = "mailRecipientStore";
	static FileStore = "fileStore";
	static LicenseStore = "licenseStore";
	static ConsumptionConsumerStore = "consumptionConsumerStore";
	static ConsumptionResultStore = "consumptionResultStore";
	static DraftStore = "draftStore";
	static CustomerStore = "customerStore";
	static IntegrationStore = "integrationStore";
	static IntegratorStore = "integratorStore";
	static FuelTypeStore = "fuelTypeStore";
	static ConsumptionCalculationStore = "consumptionCalculationStore";
	static BunkeringNotesStore = "bunkeringNotesStore";
	static AisStore = "aisStore";
	static MapStore = "mapStore";
	static PortStore = "portStore";
}
