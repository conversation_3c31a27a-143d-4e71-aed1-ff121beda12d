import { Select, TablePaginationConfig, Tag } from "antd";
import moment from "moment";
import * as React from "react";
import { L } from "../lib/abpUtility";
import { GetShipOutput } from "../services/ship/dto/getShipOutput";
import {
	convertServerUtcToLocalMoment,
	getTimezone,
} from "../services/timeService";
import { DateTimeFormat } from "./ViewSettingsConsts";
import {
	FileSearchOutlined,
	FilterFilled,
	FilterOutlined,
	SearchOutlined,
} from "@ant-design/icons";

export const renderDate = (
	dateString: string | number,
	includeTime = false,
): JSX.Element => {
	if (!dateString) return <div />;
	if (includeTime)
		return <div>{moment.utc(dateString).format(DateTimeFormat.fullDate)}</div>;
	return <div>{moment.utc(dateString).format(DateTimeFormat.onlyDate)}</div>;
};

const postfixes = ["h", "m", "s"];

export const renderTimeSpan = (text: string) => {
	if (!text) return "-";
	const hasDays = text.split(".").length > 1;
	let timeSpan = text;
	let time = "";
	if (hasDays) {
		time += `${Number.parseInt(timeSpan.split(".")[0])}d `;
		timeSpan = timeSpan.split(".")[1];
	}
	const times = timeSpan.split(":");

	for (let i = 0; i < times.length; i++) {
		if (Number.parseInt(times[i]) === 0) continue;
		time += `${Number.parseInt(times[i])}${postfixes[i]} `;
	}

	return time;
};

export const renderDateFromUtcStringAndTimezone = (
	utcServerString: string,
	timezone: string = getTimezone(),
) => {
	const timezonedString = convertServerUtcToLocalMoment(
		utcServerString,
		timezone,
	);
	return timezonedString ? (
		<div>{moment(timezonedString).format(DateTimeFormat.fullDate)}</div>
	) : (
		<div />
	);
};

export const normalizeCamelCaseText = (input: string): string => {
	const normalizedText = L(input);
	if (/[a-z][A-Z]/.test(input)) {
		let result = normalizedText
			? normalizedText
					.split(/(?=[A-Z])/)
					.join(" ")
					.toLowerCase()
			: "";
		result = result ? result.replaceAt(0, result[0].toLocaleUpperCase()) : "";
		return result;
	}
	return normalizedText;
};

String.prototype.replaceAt = function (index: number, replacement: string) {
	return (
		this.substr(0, index) +
		replacement +
		this.substr(index + replacement.length)
	);
};

export const formatString = (value: string, ...args: string[]): string => {
	let argCount = args.length;

	let formattedString = value;

	while (argCount--) {
		formattedString = value.replace(
			new RegExp(`\\{${argCount}\\}`, "gm"),
			args[argCount],
		);
	}
	return formattedString;
};

export const renderCheckboxValue = (value: boolean): JSX.Element => {
	return value ? (
		<Tag color="#2db7f5">{L("Yes")}</Tag>
	) : (
		<Tag color="red">{L("No")}</Tag>
	);
};

export const renderValidationValue = (value: boolean): JSX.Element => {
	return value === true ? (
		<p color="black" style={{ margin: 0 }}>
			{L("Yes")}
		</p>
	) : (
		<p color="black" style={{ margin: 0 }}>
			{L("No")}
		</p>
	);
};

export const renderResultEdited = (
	value: string,
	manualValue: string,
): JSX.Element => {
	return Number.parseFloat(value) !== Number.parseFloat(manualValue) ? (
		<p color="black">{L("Yes")}</p>
	) : (
		<p color="black">{L("No")}</p>
	);
};

export const renderCheckboxCustomValue = (
	value: boolean,
	positiveTagValue: string,
	negativeTagValue: string,
) => {
	return value === true ? (
		<Tag color="#2db7f5">{L(positiveTagValue)}</Tag>
	) : (
		<Tag color="red">{L(negativeTagValue)}</Tag>
	);
};

export const renderUnixDate = (dateNumber: number): JSX.Element => {
	return dateNumber ? (
		<div>{moment(dateNumber).format(DateTimeFormat.fullDate)}</div>
	) : (
		<div />
	);
};

declare global {
	interface String {
		replaceAt(index: number, replacement: string): string;
	}
}

export const getShipLabel = (ship: GetShipOutput): string => {
	return `${ship.shipName} - ${ship.callSign}`;
};

export const orderByHours = (a: string, b: string) => {
	const firstHour = a.length > 4 ? a : `0${a}`;
	const secondHour = b.length > 4 ? b : `0${b}`;
	return firstHour.localeCompare(secondHour);
};

export const getTablePaginationOptions = (
	totalCount: number,
): TablePaginationConfig => {
	const paginationOptions = {
		showSizeChanger: true,
		pageSizeOptions: ["10", "20", "30", "50", "100"],
		locale: { items_per_page: `/ ${L("Page")}` },
		defaultCurrent: 1,
		total: totalCount ?? 10,
		position: ["bottomCenter"],
	} as TablePaginationConfig;
	return paginationOptions;
};

export const createPascalCaseText = (input: string): string => {
	return input
		.replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1))
		.replace(/\s/g, "");
};

export const renderSearchIcon = (filtered: boolean): JSX.Element => {
	return filtered ? (
		<FileSearchOutlined
			style={{
				fontSize: "medium",
				width: "30px",
			}}
		/>
	) : (
		<SearchOutlined
			style={{
				fontSize: "medium",
				width: "30px",
			}}
		/>
	);
};

export const renderFilterIcon = (filtered: boolean): JSX.Element => {
	return filtered ? (
		<FilterFilled
			style={{
				fontSize: "medium",
				width: "30px",
			}}
		/>
	) : (
		<FilterOutlined
			style={{
				fontSize: "medium",
				width: "30px",
			}}
		/>
	);
};

export const renderHoursOptions = () => {
	return Array.from({ length: 24 }, (item, hourIndex) => {
		const currentHour = `${(hourIndex + 12) % 24}:00`;
		return (
			<Select.Option key={`${currentHour}:00`}>{currentHour}</Select.Option>
		);
	});
};

export const renderHoursOptionsEvery15Min = () => {
	const options = [];
	for (let hour = 0; hour < 24; hour++) {
		for (let minute = 0; minute < 60; minute += 15) {
			const formattedHour = `${(hour < 10 ? "0" : "") + hour}:${minute === 0 ? "00" : minute < 10 ? `0${minute}` : minute}`;
			options.push(
				<Select.Option key={`${formattedHour}:00`}>
					{formattedHour}
				</Select.Option>,
			);
		}
	}
	return options;
};
