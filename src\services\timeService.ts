import moment, { Moment } from "moment";

interface ITimeFormats {
	short: string;
	full: string;
	fullWithSlashSeparator: string;
	fullWithDashSeparator: string;
}

export const timeFormats: ITimeFormats = {
	short: "DD.MM.YYYY",
	full: "DD.MM.YYYY HH:mm",
	fullWithSlashSeparator: "MM/DD/YYYY hh:mm",
	fullWithDashSeparator: "MM-DD-YYYY hh:mm",
};

export const getTimezone = (): string => moment.tz.guess();

export const convertServerUtcToLocalMoment = (
	utcServerString: string,
	timezone: string = getTimezone(),
): Moment => {
	const utcTime = moment.utc(utcServerString);
	const localTime = utcTime.clone().tz(timezone);
	return localTime;
};
