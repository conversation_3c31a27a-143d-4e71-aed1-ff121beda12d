import * as React from "react";

import { PlusOutlined, SettingOutlined } from "@ant-design/icons";

import {
	Button,
	Card,
	Col,
	Dropdown,
	Input,
	Menu,
	Modal,
	Row,
	Table,
	Tag,
} from "antd";
import { inject, observer } from "mobx-react";

import AppComponentBase from "../../components/AppComponentBase";
import { L } from "../../lib/abpUtility";
import { CustomerModel } from "../../services/customer/dto/customerModel";
import { EntityDto } from "../../services/dto/entityDto";
import CreateTenantInput from "../../services/tenant/dto/createTenantInput";
import { GetAllTenantOutput } from "../../services/tenant/dto/getAllTenantOutput";
import CustomerStore from "../../stores/customerStore";
import LicenseStore from "../../stores/licenseStore";
import Stores from "../../stores/storeIdentifier";
import TenantStore from "../../stores/tenantStore";
import { ModalType } from "../ModalConsts";
import { getTablePaginationOptions } from "../renderUtils";
import CreateOrUpdateTenant from "./components/createOrUpdateTenant";
import { ColumnProps, TablePaginationConfig } from "antd/lib/table";

export interface TenantCustomerModel extends CreateTenantInput, CustomerModel {}

export interface ITenantProps {
	tenantStore: TenantStore;
	licenseStore: LicenseStore;
	customerStore: CustomerStore;
}

export interface ITenantState {
	modalVisible: boolean;
	maxResultCount: number;
	skipCount: number;
	tenantId: number;
	filter: string;
	licenseModalVisible: boolean;
	addLicenseVisible: boolean;
}

const confirm = Modal.confirm;
const Search = Input.Search;

@inject(Stores.TenantStore)
@inject(Stores.LicenseStore)
@inject(Stores.CustomerStore)
@observer
class Tenant extends AppComponentBase<ITenantProps, ITenantState> {
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	formRef: any;

	state = {
		modalVisible: false,
		maxResultCount: 10,
		skipCount: 0,
		tenantId: 0,
		filter: "",
		licenseModalVisible: false,
		addLicenseVisible: false,
	};

	async componentDidMount() {
		await this.getAll();
	}

	async getAll() {
		await this.props.tenantStore.getAll({
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: this.state.filter,
		});
		this.forceUpdate();
	}

	handleTableChange = (pagination: TablePaginationConfig) => {
		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
			},
			async () => await this.getAll(),
		);
	};

	toggleModal = () => {
		this.setState({
			modalVisible: !this.state.modalVisible,
		});
	};

	async createOrUpdateModalOpen(entityDto: EntityDto) {
		if (entityDto.id === 0) {
			this.props.tenantStore.createTenant();
		} else {
			await this.props.tenantStore.get(entityDto);
		}

		this.setState({ tenantId: entityDto.id });
		this.toggleModal();

		if (entityDto.id !== 0) {
			this.formRef.props.form.setFieldsValue({
				...this.props.tenantStore.tenantModel,
			});
		} else {
			this.formRef.props.form.resetFields();
		}
	}

	delete(input: EntityDto) {
		const self = this;
		confirm({
			title: "Do you Want to delete these items?",
			onOk() {
				self.props.tenantStore.delete(input);
			},
			onCancel() {},
		});
	}

	handleCreate = () => {
		const form = this.formRef.props.form;
		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
		form.validateFields(async (err: any, values: TenantCustomerModel) => {
			const tenantModel: CreateTenantInput = {
				adminEmailAddress: values.adminEmailAddress,
				connectionString: values.connectionString,
				isActive: values.isActive,
				name: values.name,
				password: values.password,
				tenancyName: values.tenancyName,
			};

			if (err) {
				return;
			}
				if (this.state.tenantId === 0) {
					await this.props.tenantStore.create(tenantModel);
				} else {
					await this.props.tenantStore.update({
						id: this.state.tenantId,
						...values,
					});
				}

			await this.getAll();
			this.setState({ modalVisible: false });
			form.resetFields();
		});
	};

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	saveFormRef = (formRef: any) => {
		this.formRef = formRef;
	};

	handleSearch = (value: string) => {
		this.setState({ filter: value }, async () => await this.getAll());
	};

	public render() {
		const { tenants } = { ...this.props.tenantStore };
		const paginationOptions = getTablePaginationOptions(tenants?.totalCount);
		const columns :Array<ColumnProps<GetAllTenantOutput>> = [
			{
				title: L("TenancyName"),
				dataIndex: "tenancyName",
				key: "tenancyName",
				width: 150,
				render: (text: string) => <div>{text}</div>,
			},
			{
				title: L("Name"),
				dataIndex: "name",
				key: "name",
				width: 150,
				render: (text: string) => <div>{text}</div>,
			},
			{
				title: L("IsActive"),
				dataIndex: "isActive",
				key: "isActive",
				width: 150,
				render: (text: boolean) =>
					text === true ? (
						<Tag color="#2db7f5">{L("Yes")}</Tag>
					) : (
						<Tag color="red">{L("No")}</Tag>
					),
			},
			{
				title: L("Customer"),
				dataIndex: "customerName",
				key: "customerName",
				width: 150,
				sorter: (a: GetAllTenantOutput, b: GetAllTenantOutput) =>
					a.customerName.localeCompare(b.customerName, "en", {
						sensitivity: "base",
					}),
			},
			{
				title: L("Actions"),
				width: 150,
				render: (text: string, item) => (
					<div>
						<Dropdown
							trigger={["click"]}
							overlay={
								<Menu>
									<Menu.Item
										onClick={() =>
											this.createOrUpdateModalOpen({ id: item.id })
										}
									>
										{L("Edit")}
									</Menu.Item>
									<Menu.Item onClick={() => this.delete({ id: item.id })}>
										{L("Delete")}
									</Menu.Item>
								</Menu>
							}
							placement="bottomLeft"
						>
							<Button type="primary" icon={<SettingOutlined />}>
								{L("Actions")}
							</Button>
						</Dropdown>
					</div>
				),
			},
		];

		return (
			<Card>
				<Row>
					<Col
						xs={{ span: 10, offset: 0 }}
						sm={{ span: 10, offset: 0 }}
						md={{ span: 10, offset: 0 }}
						lg={{ span: 10, offset: 0 }}
						xl={{ span: 10, offset: 0 }}
						xxl={{ span: 10, offset: 0 }}
					>
						<Search
							placeholder={this.L("Filter")}
							onSearch={this.handleSearch}
						/>
					</Col>
					<Col
						xs={{ span: 13, offset: 0 }}
						sm={{ span: 13, offset: 0 }}
						md={{ span: 13, offset: 0 }}
						lg={{ span: 13, offset: 0 }}
						xl={{ span: 13, offset: 0 }}
						xxl={{ span: 13, offset: 0 }}
						style={{ display: "flex", justifyContent: "right" }}
					>
						<Button
							type="primary"
							shape="circle"
							icon={<PlusOutlined />}
							onClick={() => this.createOrUpdateModalOpen({ id: 0 })}
						/>
					</Col>
				</Row>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey="id"
							bordered={true}
							pagination={paginationOptions}
							columns={columns}
							loading={tenants  === undefined}
							dataSource={tenants === undefined ? [] : tenants.items}
							onChange={this.handleTableChange}
						/>
					</Col>
				</Row>
				<CreateOrUpdateTenant
					wrappedComponentRef={this.saveFormRef}
					visible={this.state.modalVisible}
					onCancel={() =>
						this.setState({
							modalVisible: false,
						})
					}
					modalType={
						this.state.tenantId === 0 ? ModalType.create : ModalType.edit
					}
					onCreate={this.handleCreate}
				/>
			</Card>
		);
	}
}

export default Tenant;
