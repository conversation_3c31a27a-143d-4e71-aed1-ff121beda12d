import { PlusOutlined, SettingOutlined } from "@ant-design/icons";

import {
	<PERSON><PERSON>,
	<PERSON>,
	Col,
	Dropdown,
	Menu,
	Modal,
	Row,
	Table,
	TablePaginationConfig,
} from "antd";
import { inject, observer } from "mobx-react";

import { FormComponentProps } from "@ant-design/compatible/lib/form";

import { ColumnProps } from "antd/lib/table";
import { SorterResult } from "antd/lib/table/interface";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import { L, isGranted } from "../../lib/abpUtility";
import { EntityDto } from "../../services/dto/entityDto";
import { GetAllRoleOutput } from "../../services/role/dto/getAllRoleOutput";
import RoleStore from "../../stores/roleStore";
import Stores from "../../stores/storeIdentifier";
import utils from "../../utils/utils";
import { ModalType } from "../ModalConsts";
import { getTablePaginationOptions } from "../renderUtils";
import CreateOrUpdateRole from "./components/createOrUpdateRole";

export interface IRoleProps extends FormComponentProps {
	roleStore: RoleStore;
}

export interface IRoleState {
	modalVisible: boolean;
	maxResultCount: number;
	skipCount: number;
	roleId: number;
	filter: string;
	sorters: SorterResult<GetAllRoleOutput>[];
	loading: boolean;
	postAction: boolean;
}

const confirm = Modal.confirm;
// const Search = Input.Search;

type PreviousState = {
	sorters: IRoleState["sorters"];
};

@inject(Stores.RoleStore)
@observer
class Role extends AppComponentBase<IRoleProps, IRoleState> {
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	formRef: any;

	state: IRoleState = {
		modalVisible: false,
		maxResultCount: 10,
		skipCount: 0,
		roleId: 0,
		filter: "",
		sorters: [],
		loading: false,
		postAction: false,
	};

	async componentDidMount() {
		const prevState = this.extractPreviousState();

		this.setState({ ...prevState }, async () => {
			await this.getAll();
		});
	}

	extractPreviousState(): PreviousState {
		let state: PreviousState = {
			sorters: [],
		};

		const prevState =
			utils.getSortAndFilterFromStorage<PreviousState>("role-filters");

		if (prevState)
			state = {
				...prevState,
			};

		utils.removeStateFromStorage("role-filters");

		return state;
	}

	componentWillUnmount(): void {
		const settings: PreviousState = {
			sorters: this.state.sorters,
		};

		utils.saveSortAndFilterToStorage("role-filters", settings);
	}

	async getAll() {
		this.setState({ loading: true });
		const sortString = utils.getSorterString(this.state.sorters);
		await this.props.roleStore.getAll({
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: this.state.filter,
			sorting: sortString,
		});
		this.setState({ loading: false });
	}

	handleTableChange = (
		pagination: TablePaginationConfig,
		filters: Partial<Record<keyof GetAllRoleOutput, string[]>>,
		sorter: SorterResult<GetAllRoleOutput> | SorterResult<GetAllRoleOutput>[],
	) => {
		const sorters = utils.getSorters(sorter);
		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
				sorters: sorters,
			},
			async () => await this.getAll(),
		);
	};

	Modal = () => {
		this.setState({
			modalVisible: !this.state.modalVisible,
		});
	};

	groupByPermission = (list: { label: string; value: string }[]) =>
		list.reduce(
			(hash: Record<string, string[]>, obj) => ({
				// biome-ignore lint/performance/noAccumulatingSpread: <explanation>
				...hash,
				[obj.label.split("-")[0]]: (hash[obj.label.split("-")[0]] || []).concat(
					obj.value,
				),
			}),
			{},
		);

	async createOrUpdateModalOpen(entityDto: EntityDto) {
		this.formRef.props.form.resetFields();
		if (entityDto.id === 0) {
			this.props.roleStore.createRole();
			await this.props.roleStore.getAllPermissions();
		} else {
			await this.props.roleStore.getRoleForEdit(entityDto);
			await this.props.roleStore.getAllPermissions();
		}

		this.setState({ roleId: entityDto.id });
		this.Modal();

		const options = this.props.roleStore.roleEdit.grantedPermissionNames.map(
			(item) => {
				const label = item.split(".")[1].replace(/\[|\]/g, "").trim();

				return { label, value: item };
			},
		);

		this.formRef.props.form.setFieldsValue({
			...this.props.roleStore.roleEdit.role,
			grantedPermissions: this.groupByPermission(options),
		});
	}

	delete(input: EntityDto) {
		const self = this;
		confirm({
			title: "Do you Want to delete these items?",
			onOk() {
				self.props.roleStore.delete(input);
			},
			onCancel() {},
		});
	}

	handleCreate = () => {
		const form = this.formRef.props.form;

		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
		form.validateFields(async (err: any, values: any) => {
			if (err) {
				return;
			}
			let localPermissions: string[] = [];

			this.setState({ postAction: true });
			for (const perm in values.grantedPermissions) {
				if (!values.grantedPermissions[perm]) continue;
				localPermissions = localPermissions.concat(
					values.grantedPermissions[perm],
				);
			}

			try {
				if (this.state.roleId === 0) {
					await this.props.roleStore.create({
						...values,
						grantedPermissions: localPermissions,
					});
				} else {
					await this.props.roleStore.update({
						id: this.state.roleId,
						...values,
						grantedPermissions: localPermissions,
					});
				}

				await this.getAll();
				this.setState({ modalVisible: false });
				form.resetFields();
			} catch {
			} finally {
				this.setState({ postAction: false });
			}
		});
	};

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	saveFormRef = (formRef: any) => {
		this.formRef = formRef;
	};

	handleSearch = (value: string) => {
		this.setState({ filter: value }, async () => await this.getAll());
	};

	public render() {
		const { allPermissions, roles } = this.props.roleStore;
		const paginationOptions = getTablePaginationOptions(roles?.totalCount);
		const columns: Array<ColumnProps<GetAllRoleOutput>> = [
			{
				title: L("RoleName"),
				dataIndex: "name",
				key: "name",
				width: 150,
				sorter: true,
				render: (text: string) => <div>{text}</div>,
			},
			{
				title: L("DisplayName"),
				dataIndex: "displayName",
				key: "displayName",
				width: 150,
				sorter: true,
				render: (text: string) => <div>{text}</div>,
			},
			{
				title: L("Actions"),
				width: "10%",
				render: (text: string, item: GetAllRoleOutput) => (
					<div>
						<Dropdown
							trigger={["click"]}
							overlay={
								<Menu>
									{isGranted("Pages.Roles-Edit") && (
										<Menu.Item
											onClick={() =>
												this.createOrUpdateModalOpen({ id: item.id })
											}
										>
											{L("Edit")}
										</Menu.Item>
									)}
									{isGranted("Pages.Roles-Delete") && (
										<Menu.Item onClick={() => this.delete({ id: item.id })}>
											{L("Delete")}
										</Menu.Item>
									)}
								</Menu>
							}
							placement="bottomLeft"
						>
							<Button type="primary" icon={<SettingOutlined />}>
								{L("Actions")}
							</Button>
						</Dropdown>
					</div>
				),
			},
		];

		const hasNoActions =
			!isGranted("Pages.Roles-Edit") && !isGranted("Pages.Roles-Delete");

		if (hasNoActions) {
			columns.pop();
		}

		return (
			<Card>
				<Row>
					{/* <Col
            xs={{ span: 10, offset: 0 }}
            sm={{ span: 10, offset: 0 }}
            md={{ span: 10, offset: 0 }}
            lg={{ span: 10, offset: 0 }}
            xl={{ span: 10, offset: 0 }}
            xxl={{ span: 10, offset: 0 }}
          >
            <Search placeholder={this.L('Filter')} onSearch={this.handleSearch} />
          </Col> */}
					<Col
						xs={{ span: 23, offset: 0 }}
						sm={{ span: 23, offset: 0 }}
						md={{ span: 23, offset: 0 }}
						lg={{ span: 23, offset: 0 }}
						xl={{ span: 23, offset: 0 }}
						xxl={{ span: 23, offset: 0 }}
						style={{ display: "flex", justifyContent: "end" }}
					>
						{isGranted("Pages.Roles-Add") && (
							<Button
								type="primary"
								shape="circle"
								icon={<PlusOutlined />}
								style={{
									display: "flex",
									justifyContent: "center",
									alignItems: "center",
								}}
								onClick={() => this.createOrUpdateModalOpen({ id: 0 })}
							/>
						)}
					</Col>
				</Row>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey="id"
							bordered={true}
							pagination={paginationOptions}
							columns={columns}
							loading={roles === undefined || this.state.loading}
							dataSource={roles === undefined ? [] : roles.items}
							onChange={this.handleTableChange}
						/>
					</Col>
				</Row>

				<CreateOrUpdateRole
					wrappedComponentRef={this.saveFormRef}
					visible={this.state.modalVisible}
					loading={this.state.postAction}
					onCancel={() =>
						this.setState({
							modalVisible: false,
						})
					}
					modalType={
						this.state.roleId === 0 ? ModalType.edit : ModalType.create
					}
					onOk={this.handleCreate}
					permissions={allPermissions}
					roleStore={this.props.roleStore}
				/>
				<Chat />
			</Card>
		);
	}
}

export default Role;
