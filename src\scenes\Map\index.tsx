import { toJS } from "mobx";
import { inject, observer } from "mobx-react";
import * as React from "react";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import { ShipPositionDto } from "../../services/map/dto/ShipPositionDto";
import MapStore from "../../stores/mapStore";
import Stores from "../../stores/storeIdentifier";
import MapWrapper from "./components/mapWrapper";

export interface IMapProps {
	mapStore: MapStore;
}

export interface IMapState {
	ShipLastPositions: ShipPositionDto[];
}

@inject(Stores.MapStore)
@observer
class Map extends AppComponentBase<IMapProps, IMapState> {
	state = {
		ShipLastPositions: [],
	};

	async componentDidMount() {
		await this.props.mapStore.getShipLastPositions();
		this.setState({
			ShipLastPositions: toJS(this.props.mapStore.ShipLastPositions),
		});
	}

	render() {
		return (
			<>
				<MapWrapper ShipLastPositions={this.state.ShipLastPositions} />
				<Chat />
			</>
		);
	}
}

export default Map;
