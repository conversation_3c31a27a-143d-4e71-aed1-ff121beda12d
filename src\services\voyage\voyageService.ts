import { EntityDto } from "../dto/entityDto";
import { ListResultDto, PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoints";
import http from "../httpService";
import { PagedVoyageResultRequestDto } from "./dto/PagedVoyageResultRequestDto";
import { CreateOrUpdateVoyageInput } from "./dto/createOrUpdateVoyageInput";
import { GetAllVoyageOutput } from "./dto/getAllVoyageOutput";
import { GetVoyageOutput } from "./dto/getVoyageOutput";

class VoyageService {
	public async create(createVoyageInput: CreateOrUpdateVoyageInput) {
		const result = await http.post(Endpoint.Voyage.Create, createVoyageInput);
		return result.data.result;
	}

	public async update(updateVoyageInput: CreateOrUpdateVoyageInput) {
		const result = await http.put(Endpoint.Voyage.Update, updateVoyageInput);
		return result.data.result;
	}

	public async delete(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.Voyage.Delete, {
			params: entityDto,
		});
		return result.data;
	}

	public async get(entityDto: EntityDto): Promise<CreateOrUpdateVoyageInput> {
		const result = await http.get(Endpoint.Voyage.Get, { params: entityDto });
		return result.data.result;
	}

	public async getAll(
		pagedFilterAndSortedRequest: PagedVoyageResultRequestDto,
	): Promise<PagedResultDto<GetAllVoyageOutput>> {
		const result = await http.get(Endpoint.Voyage.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}

	public async voyageStatuses() {
		const result = await http.get(Endpoint.Voyage.GetVoyageStatuses);
		return result.data.result;
	}

	public async downloadQrCodeLink(entityDto: EntityDto): Promise<string> {
		const result = await http.post(
			Endpoint.Voyage.DownloadQrCodeLink,
			entityDto,
		);
		return result.data.result;
	}

	public async getAllWithIdAndNumber(): Promise<
		ListResultDto<GetVoyageOutput>
	> {
		const result = await http.get(Endpoint.Voyage.GetAllWithIdAndNumber);
		return result.data.result;
	}

	public async voyageInitializationSources() {
		const result = await http.get(
			Endpoint.Voyage.GetVoyageInitializationSources,
		);
		return result.data.result;
	}
}

export default new VoyageService();
