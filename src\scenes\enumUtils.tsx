import * as React from "react";
import { normalizeCamelCaseText } from "./renderUtils";

export const unitOfMeasureCustomData: Map<string, string> = new Map<
	string,
	string
>([
	["Kilograms (Kg)", "Kilograms (Kg)"],
	["Tonnes (t)", "Tonnes (t)"],
	["Cubic Meters (m3)", "Cubic Meters (m3)"],
	["Litres (l)", "Litres (l)"],
	["RPM", "RPM"],
	["Knots", "Knots"],
	["Kilowatts (kW)", "Kilowatts (kW)"],
	["Nautical Miles (Nm)", "Nautical Miles (Nm)"],
	["Meters per second (M/s)", "Meters per second (M/s)"],
	["Degrees (Deg)", "Degrees (Deg)"],
	["Celsius (°C)", "Celsius (°C)"],
	["Percentage (%)", "Percentage (%)"],
	["Hours", "Hours"],
	["None", "None"],
]);

export const statusCustomData: Map<number, string> = new Map<number, string>([
	[0, "Verified"],
	[1, "Not Verified"],
	[2, "Blocked"],
]);

export const vesselTypesCustomData: Map<string, string> = new Map<
	string,
	string
>([
	["Passenger ship", "Passenger ship"],
	["Ro-Pax ship", "Ro-Pax ship"],
	["Ro-Ro ship", "Ro-Ro ship"],
	["Container ship", "Container ship"],
	["Oil tanker", "Oil tanker"],
	["Chemical tanker", "Chemical tanker"],
	["Gas carrier", "Gas carrier"],
	["Bulk carrier", "Bulk carrier"],
	["Refrigerated cargo ship", "Refrigerated cargo ship"],
	["Combination carrier", "Combination carrier"],
	["LNG carrier", "LNG carrier"],
	["Container/Ro-Ro ship", "Container/Ro-Ro ship"],
	["General cargo ship", "General cargo ship"],
	["Vehicle carrier", "Vehicle carrier"],
	["Other ship types", "Other ship types"],
]);

export const cargoUnitsCustomData: { [key: string]: Map<string, string> } = {
	"Passenger ship": new Map([
		["Number of passengers (PAX)", "Number of passengers (PAX)"],
	]),
	"Ro-Pax ship": new Map([
		["Number of passengers (PAX)", "Number of passengers (PAX)"],
		["Mass of cargo onboard (MT)", "Mass of cargo onboard (MT)"],
		["Occupied lane meters (Lane m)", "Occupied lane meters (Lane m)"],
	]),
	"Ro-Ro ship": new Map([
		["Mass of cargo onboard (MT)", "Mass of cargo onboard (MT)"],
		["Occupied lane meters (Lane m)", "Occupied lane meters (Lane m)"],
	]),
	"Container ship": new Map([
		["Mass of cargo onboard (MT)", "Mass of cargo onboard (MT)"],
		["Number of TEUs (TEU)", "Number of TEUs (TEU)"],
	]),
	"Oil tanker": new Map([
		["Mass of cargo onboard (MT)", "Mass of cargo onboard (MT)"],
	]),
	"Chemical tanker": new Map([
		["Mass of cargo onboard (MT)", "Mass of cargo onboard (MT)"],
	]),
	"Gas carrier": new Map([
		["Mass of cargo onboard (MT)", "Mass of cargo onboard (MT)"],
	]),
	"Bulk carrier": new Map([
		["Mass of cargo onboard (MT)", "Mass of cargo onboard (MT)"],
	]),
	"Refrigerated cargo ship": new Map([
		["Mass of cargo onboard (MT)", "Mass of cargo onboard (MT)"],
	]),
	"Combination carrier": new Map([
		["Mass of cargo onboard (MT)", "Mass of cargo onboard (MT)"],
	]),
	"LNG carrier": new Map([
		[
			"Volume of cargo on discharge (cbm)",
			"Volume of cargo on discharge (cbm)",
		],
	]),
	"Container/Ro-Ro ship": new Map([
		["Volume of cargo on board (cbm)", "Volume of cargo on board (cbm)"],
	]),
	"General cargo ship": new Map([
		[
			"Deadweight for laden voyages (DWT)",
			"Deadweight for laden voyages (DWT)",
		],
	]),
	"Vehicle carrier": new Map([
		["Mass of cargo onboard (MT)", "Mass of cargo onboard (MT)"],
		["Occupied lane meters (Lane m)", "Occupied lane meters (Lane m)"],
	]),
	"Other ship types": new Map([
		["Mass of cargo onboard (MT)", "Mass of cargo onboard (MT)"],
		[
			"Deadweight for laden voyages (DWT)",
			"Deadweight for laden voyages (DWT)",
		],
	]),
};

export const reasonsForEventCustomData: Map<string, string> = new Map<
	string,
	string
>([
	["Arriving Berth", "Arriving Berth"],
	["Leaving Berth", "Leaving Berth"],
	["Arriving Anchorage", "Arriving Anchorage"],
	["Leaving Anchorage", "Leaving Anchorage"],
	["Entering ECA zone", "Entering ECA zone"],
	["Leaving ECA zone", "Leaving ECA zone"],
	["Start of Charter Period", "Start of Charter Period"],
	["End of Charter Period", "End of Charter Period"],
	["Performance Trial - Start", "Performance Trial - Start"],
	["Performance Trial - End", "Performance Trial - End"],
	["Reporting", "Reporting"],
	["Other", "Other"],
]);

export const meterTypeCustomData: Map<string, string> = new Map<string, string>(
	[
		["Main Supply", "Main Supply"],
		["Main Engine", "Main Engine"],
		["Auxiliary Engine", "Auxiliary Engine"],
		["Boiler", "Boiler"],
		["Other", "Other"],
		["All Meter Types", "All Meter Types"],
	],
);

export const meterTypesForShipsCustomData: Map<string, string> = new Map<
	string,
	string
>([
	["Main Supply", "Main Supply"],
	["Main Engine", "Main Engine"],
	["Auxiliary Engine", "Auxiliary Engine"],
	["Boiler", "Boiler"],
	["Other", "Other"],
	["All Meter Types", "All Meter Types"],
]);

export const scrubberCustomData: Map<string, string> = new Map<string, string>([
	[
		"No scrubber fitted/ not operational",
		"No scrubber fitted/ not operational",
	],
	["Open-loop scrubber", "Open-loop scrubber"],
	["Closed-loop scrubber", "Closed-loop scrubber"],
	["Hybrid scrubber", "Hybrid scrubber"],
]);

export const measureTypeCustomData: Map<string, string> = new Map<
	string,
	string
>([
	["Digital", "Digital"],
	["Mechanical", "Mechanical"],
]);

export const flowDirectionCustomData: Map<string, string> = new Map<
	string,
	string
>([
	["Flow In", "Flow In"],
	["Flow Out", "Flow Out"],
]);

export const deviceStatusesCustomData: Map<string, string> = new Map<
	string,
	string
>([
	["Not Activated", "Not Activated"],
	["Activated", "Activated"],
	["Blocked", "Blocked"],
]);

export const fuelTypeCustomData: Map<string, string> = new Map<string, string>([
	["VLSFO (0,5 % S)", "VLSFO (0,5 % S)"],
	["IFO 380 (3,5% S)", "IFO 380 (3,5% S)"],
	["MGO (0,1% S)", "MGO (0,1% S)"],
	["Liquefied Petroleum Gas", "Liquefied Petroleum Gas"],
	["HYDROGEN LH2", "HYDROGEN LH2"],
]);

export const mailLevelCustomData: Map<string, string> = new Map<string, string>(
	[
		["General", "General"],
		["Ship", "Ship"],
		["None", "None"],
	],
);

export const measuringCommodityCustomData: Map<string, string> = new Map<
	string,
	string
>([
	["Fuel", "Fuel"],
	["Water", "Water"],
	["Wind", "Wind"],
]);

export const voyageTypeCustomData: Map<string, string> = new Map<
	string,
	string
>([
	[
		"Voyage between EU/EEA ports. (100% of emissions under EU/ETS)",
		"Voyage between EU/EEA ports. (100% of emissions under EU/ETS) ",
	],
	[
		"Voyage from EU/EEA port (Departure). (50% of emissions under EU/ETS)",
		"Voyage from EU/EEA port (Departure). (50% of emissions under EU/ETS)",
	],
	[
		"Voyage to EU/EEA port (Arrival). (50% of emissions under EU/ETS)",
		"Voyage to EU/EEA port (Arrival). (50% of emissions under EU/ETS)",
	],
	[
		"Within EU/EEA port. (100% of emissions under EU/ETS)",
		"Within EU/EEA port. (100% of emissions under EU/ETS)",
	],
]);

export const validationOptionsCustomData: Map<string, string> = new Map<
	string,
	string
>([
	["All", "AI Discrepancy = All"],
	["Yes", "AI Discrepancy = Yes"],
	["No", "AI Discrepancy = No"],
	["ResultLower", "Result Lower = Yes"],
	["IncorrectDigits", "Incorrect number of digits = Yes"],
]);



export const renderStringForDropDown = (text: string): JSX.Element => {
	return <>{text ? normalizeCamelCaseText(text) : ""}</>;
};

export const renderFlagEnum = (
	value: number,
	array: { [key: number]: string },
): JSX.Element => {
	const values: string[] = [];

	if (array) {
		for (const index in array) {
			if (+index >= 1 && (value & +index) === +index) {
				values.push(array[index]);
			}
		}

		if (values.length === 0) values.push(array[0]);
	}

	return <div>{values.length >= 1 ? values.join(", ") : ""}</div>;
};

export const renderCustomEnum = (
	text: string,
	customData: Map<number, string>,
): JSX.Element => {
	const value = customData.get(Number(text));
	return <div>{customData ? value : ""}</div>;
};

declare global {
	interface Number {
		hasFlag(enumFlag: number): boolean;
		removeFlag(enumFlag: number): number;
	}
}

Number.prototype.hasFlag = function (enumFlag: number): boolean {
	const flags: number = this as number;
	return (flags & enumFlag) === enumFlag;
};

Number.prototype.removeFlag = function (enumFlag: number): number {
	const flags: number = this as number;
	return flags & ~enumFlag;
};
