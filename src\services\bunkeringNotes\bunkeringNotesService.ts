import { EntityDto } from "../dto/entityDto";
import { PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoints";
import http from "../httpService";
import { CreateOrUpdateBunkeringNotesInput } from "./dto/CreateOrUpdateBunkeringNotesInput";
import { GetBunkeringNotesOutput } from "./dto/GetBunkeringNotesOutput";
import { PagedFilterAndSortedRequestBunkeringNotes } from "./dto/PagedFilterAndSortedRequestBunkeringNotes";
import { UpdateBunkeringNotesInput } from "./dto/UpdateBunkeringNotesInput";
class BunkeringNotesService {
	public async create(
		createBunkeringNotesInput: CreateOrUpdateBunkeringNotesInput,
	) {
		const result = await http.post(Endpoint.BunkeringNotes.Create, {
			...createBunkeringNotesInput,
			source: "Web",
		});
		return result.data.result;
	}
	public async update(updateBunkeringNotesInput: UpdateBunkeringNotesInput) {
		const result = await http.put(Endpoint.BunkeringNotes.Update, {
			...updateBunkeringNotesInput,
			source: "Web",
		});
		return result.data.result;
	}
	public async delete(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.BunkeringNotes.Delete, {
			params: entityDto,
		});
		return result.data;
	}
	public async get(
		entityDto: EntityDto,
	): Promise<CreateOrUpdateBunkeringNotesInput> {
		const result = await http.get(Endpoint.BunkeringNotes.Get, {
			params: entityDto,
		});
		return result.data.result;
	}
	public async getAll(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequestBunkeringNotes,
	): Promise<PagedResultDto<GetBunkeringNotesOutput>> {
		const result = await http.get(Endpoint.BunkeringNotes.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}

	public async getFilters(
		filters: { keyword: string; searchColumn: string },
		property: string,
	): Promise<string[]> {
		const result = await http.get(Endpoint.BunkeringNotes.Filters, {
			params: { ...filters, propertyName: property },
		});
		return result.data.result;
	}

}
export default new BunkeringNotesService();
