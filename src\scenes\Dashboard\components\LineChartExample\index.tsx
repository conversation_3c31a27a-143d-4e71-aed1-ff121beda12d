//@ts-nocheck
import { observer } from "mobx-react";
import * as React from "react";
import {
	CartesianGrid,
	Legend,
	Line,
	<PERSON>C<PERSON>,
	<PERSON><PERSON>ip,
	<PERSON>A<PERSON><PERSON>,
	<PERSON>Axis,
} from "recharts";
import AbpComponentBase from "../../../../components/AppComponentBase";
import MeterReadingStore from "../../../../stores/meterReadingStore";

@observer
export class AllReadingsPerDayLineChart extends AbpComponentBase {
	meterReadingStore: MeterReadingStore = new MeterReadingStore();

	async componentDidMount() {
		await this.meterReadingStore.loadStatisticCountPerDays();
	}

	renderLines() {
		const result = [];
		if (this.meterReadingStore.staticCountPerDays !== undefined) {
			const callSignals = [
				...new Set(
					this.meterReadingStore.staticCountPerDays.map((x) =>
						x.key.callSignal ? x.key.callSignal : "Unknown",
					),
				),
			];
			for (let dataIndex = 0; dataIndex < callSignals.length; dataIndex++) {
				const data = callSignals[dataIndex];
				result.push(
					<Line
						type="monotone"
						strokeWidth={3}
						dataKey={data}
						stroke={getColor(dataIndex)}
					/>,
				);
			}
		}

		return result;
	}

	getNormalizeDataForChart() {
		const { staticCountPerDays } = this.meterReadingStore;
		const result: object[] = [];

		let currentObject = {};
		let previousObject = undefined;
		// biome-ignore lint/complexity/noForEach: <explanation>
		staticCountPerDays.forEach((x) => {
			previousObject = result.find((y) => y.date === x.key.date);
			if (previousObject) {
				previousObject[`${x.key.callSignal ?? "Unknown"}`] =
					x.count;
			} else {
				currentObject.date = x.key.date;
				currentObject[`${x.key.callSignal ?? "Unknown"}`] =
					x.count;
				result.push(currentObject);
				currentObject = {};
			}
		});
		result.sort((a, b) => {
			const datePrevious = (b.date as string).split("/");
			const dateCurrent = (a.date as string).split("/");

			const yearIndex = 2;
			if (datePrevious[yearIndex] !== dateCurrent[yearIndex]) {
				return Number(dateCurrent[yearIndex]) - Number(datePrevious[yearIndex]);
			}

			const monthIndex = 1;
			if (datePrevious[monthIndex] !== dateCurrent[monthIndex]) {
				return (
					Number(dateCurrent[monthIndex]) - Number(datePrevious[monthIndex])
				);
			}

			const dayIndex = 0;
			return Number(dateCurrent[dayIndex]) - Number(datePrevious[dayIndex]);
		});
		return result;
	}

	render() {
		return (
			<LineChart
				width={
					document.getElementsByClassName("dashboardBox")[0]?.clientWidth -
						100 || 1150
				}
				height={300}
				data={this.getNormalizeDataForChart()}
				margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
			>
				<XAxis dataKey="date" />
				<YAxis />
				<CartesianGrid strokeDasharray="3 3" />
				<Tooltip />
				<Legend wrapperStyle={{ fontSize: "16px" }} />
				{this.renderLines()}
			</LineChart>
		);
	}
}

export default AllReadingsPerDayLineChart;

const colors = [
	"#59D5E0",
	"#F5DD61",
	"#FAA300",
	"#F4538A",
	"#EA8FEA",
	"#F4D03F",
	"#D35400",
	"#34495E",
];

const getColor = (index: number) => {
	return colors[index % colors.length];
};
