import { EntityDto } from "../dto/entityDto";
import { PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoints";
import http from "../httpService";
import { PagedMailRecipientResultRequestDto } from "./dto/PagedMailRecipientResultRequestDto";
import { CreateOrUpdateMailRecipientInput } from "./dto/createOrUpdateMailRecipientInput";
import { GetAllMailRecipientOutput } from "./dto/getAllMailRecipientOutput";

class MailRecipientService {
	public async create(
		createMailRecipientInput: CreateOrUpdateMailRecipientInput,
	) {
		const result = await http.post(
			Endpoint.MailRecipient.Create,
			createMailRecipientInput,
		);
		return result.data.result;
	}

	public async update(
		updateMailRecipientInput: CreateOrUpdateMailRecipientInput,
	) {
		const result = await http.put(
			Endpoint.MailRecipient.Update,
			updateMailRecipientInput,
		);
		return result.data.result;
	}

	public async delete(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.MailRecipient.Delete, {
			params: entityDto,
		});
		return result.data;
	}

	public async get(
		entityDto: EntityDto,
	): Promise<CreateOrUpdateMailRecipientInput> {
		const result = await http.get(Endpoint.MailRecipient.Get, {
			params: entityDto,
		});
		return result.data.result;
	}

	public async getAll(
		pagedFilterAndSortedRequest: PagedMailRecipientResultRequestDto,
	): Promise<PagedResultDto<GetAllMailRecipientOutput>> {
		const result = await http.get(Endpoint.MailRecipient.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}
}

export default new MailRecipientService();
