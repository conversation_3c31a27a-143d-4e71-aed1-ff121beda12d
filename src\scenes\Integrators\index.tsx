import { Card, Col, Row, Table, TablePaginationConfig } from "antd";
import { inject, observer } from "mobx-react";
import * as React from "react";
import AppComponentBase from "../../components/AppComponentBase";
import { L } from "../../lib/abpUtility";
import { PagedUserResultRequestDto } from "../../services/user/dto/PagedUserResultRequestDto";
import IntegratorStore from "../../stores/integratorStore";
import Stores from "../../stores/storeIdentifier";
import { getTablePaginationOptions } from "../renderUtils";

export interface IIntegratorProps {
	integratorStore: IntegratorStore;
}

export interface IIntegratorState {
	modalVisible: boolean;
	maxResultCount: number;
	skipCount: number;
}

@inject(Stores.IntegratorStore)
@observer
class Integrator extends AppComponentBase<IIntegratorProps, IIntegratorState> {
	formRef: any;

	state: IIntegratorState = {
		modalVisible: false,
		maxResultCount: 10,
		skipCount: 0,
	};

	async componentDidMount() {
		await this.getAll();
	}

	async getAll() {
		await this.props.integratorStore.getAll({} as PagedUserResultRequestDto);
	}

	handleTableChange = (pagination: TablePaginationConfig) => {
		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
			},
			async () => await this.getAll(),
		);
	};

	public render() {
		const { integrators } = this.props.integratorStore ?? [];
		const paginationOptions = getTablePaginationOptions(
			integrators?.totalCount,
		);

		const columns = [
			{
				title: L("IntegratorId"),
				dataIndex: "id",
				key: "id",
			},
			{
				title: L("IntegratorName"),
				dataIndex: "name",
				key: "name",
			},
			{
				title: L("IntegratorUsername"),
				dataIndex: "username",
				key: "username",
			},
		];

		return (
			<Card>
				<Row>
					<Col
						xs={{ span: 4, offset: 0 }}
						sm={{ span: 4, offset: 0 }}
						md={{ span: 4, offset: 0 }}
						lg={{ span: 2, offset: 0 }}
						xl={{ span: 2, offset: 0 }}
						xxl={{ span: 2, offset: 0 }}
					/>
					<Col
						xs={{ span: 14, offset: 0 }}
						sm={{ span: 15, offset: 0 }}
						md={{ span: 15, offset: 0 }}
						lg={{ span: 1, offset: 21 }}
						xl={{ span: 1, offset: 21 }}
						xxl={{ span: 1, offset: 21 }}
					/>
				</Row>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey={(record) => record.id.toString()}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={integrators  === undefined}
							dataSource={integrators === undefined ? [] : integrators.items}
						/>
					</Col>
				</Row>
			</Card>
		);
	}
}

export default Integrator;
