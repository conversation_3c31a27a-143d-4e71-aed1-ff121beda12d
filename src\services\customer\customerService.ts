import { CustomerDto } from "./dto/customerDto";

import { EntityDto } from "../dto/entityDto";
import Endpoint from "../endpoints";
import http from "../httpService";

class CustomerService {
	public async create(customerDto: CustomerDto) {
		const result = await http.post(Endpoint.Customer.Create, customerDto);
		return result.data.result;
	}
	public async update(customer: CustomerDto) {
		const result = await http.put(Endpoint.Customer.Update, customer);
		return result.data.result;
	}

	public async getByTenantId(entityDto: EntityDto): Promise<CustomerDto> {
		const result = await http.get(Endpoint.Customer.GetByTenantId, {
			params: entityDto,
		});
		return result.data.result;
	}

	public async deleteByTenant(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.Customer.DeleteByTenant, {
			params: entityDto,
		});
		return result.data;
	}
}

export default new CustomerService();
