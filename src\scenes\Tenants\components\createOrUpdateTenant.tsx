import { Form } from "@ant-design/compatible";
import * as React from "react";
import "@ant-design/compatible/assets/index.css";
import { FormComponentProps } from "@ant-design/compatible/lib/form";
import { Checkbox, Input, Modal } from "antd";
import ComponentLayout from "../../../components/Layout/ComponentLayout";
import PasswordComponentBase from "../../../components/PasswordComponentBase";
import { L } from "../../../lib/abpUtility";
import { ModalType } from "../../ModalConsts";
import rules from "./createOrUpdateTenant.validation";

const FormItem = Form.Item;

export interface ICreateOrUpdateTenantProps extends FormComponentProps {
	visible: boolean;
	modalType: string;
	onCreate: () => void;
	onCancel: () => void;
}

class CreateOrUpdateTenant extends PasswordComponentBase<ICreateOrUpdateTenantProps> {
	render() {
		const { getFieldDecorator } = this.props.form;
		const { visible, onCancel, onCreate } = this.props;
		return (
			<Modal
				visible={visible}
				onCancel={onCancel}
				onOk={onCreate}
				title={L("Tenants")}
				width={550}
			>
				<Form>
					<FormItem
						label={L("TenancyName")}
						{...ComponentLayout.formItemLayout}
					>
						{this.props.form.getFieldDecorator("tenancyName", {
							rules: rules.tenancyName,
						})(<Input />)}
					</FormItem>
					<FormItem label={L("Name")} {...ComponentLayout.formItemLayout}>
						{getFieldDecorator("name", { rules: rules.name })(<Input />)}
					</FormItem>
					{this.props.modalType === ModalType.create ? (
						<FormItem
							label={L("AdminEmailAddress")}
							{...ComponentLayout.formItemLayout}
						>
							{getFieldDecorator("adminEmailAddress", {
								rules: rules.adminEmailAddress as any,
							})(<Input />)}
						</FormItem>
					) : null}
					{this.props.modalType === ModalType.create ? (
						<FormItem
							label={L("DatabaseConnectionString")}
							{...ComponentLayout.formItemLayout}
						>
							{getFieldDecorator("connectionString")(<Input />)}
						</FormItem>
					) : null}
					<FormItem label={L("IsActive")} {...ComponentLayout.formItemLayout}>
						{getFieldDecorator("isActive", { valuePropName: "checked" })(
							<Checkbox />,
						)}
					</FormItem>
					{this.props.form.getFieldValue("name") !== "Default" && (
						<>
							<FormItem
								label={L("Customer")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("customerName", { rules: rules.name })(
									<Input />,
								)}
							</FormItem>
							<FormItem
								label={L("GS synchronization")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("customerSynch", {
									valuePropName: "checked",
								})(<Checkbox />)}
							</FormItem>
						</>
					)}
					{this.props.modalType === ModalType.create && (
						<>
							<FormItem
								label={L("Password")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("password", { rules: rules.password })(
									<Input.Password />,
								)}
							</FormItem>
							<FormItem
								label={L("ConfirmPassword")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("confirmPassword", {
									rules: rules.confirmPassword(this.compareToFirstPassword),
								})(<Input.Password />)}
							</FormItem>
						</>
					)}
				</Form>
			</Modal>
		);
	}
}

export default Form.create<ICreateOrUpdateTenantProps>()(CreateOrUpdateTenant);
