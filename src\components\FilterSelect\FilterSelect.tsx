import { Select } from "antd";
import { FilterDropdownProps } from "antd/lib/table/interface";
import { L } from "../../lib/abpUtility";
import { useEffect, useState } from "react";

type FilterProps = {
	value: string | undefined;
	title: string;
	loading: boolean;
	options: Array<{ value: string; key: string | number }>;
	confirm: FilterDropdownProps["confirm"];
	handleFilter: (value: string) => void;
};

const { Option } = Select;

const FilterSelect = ({
	value,
	title,
	options,
	confirm,
	handleFilter,
	loading,
}: FilterProps) => {
	const [open, setOpen] = useState<boolean>(false);

	useEffect(() => {
		setOpen(true);
	}, []);

	useEffect(() => {
		if (loading) setOpen(false);
		else setOpen(true);
	}, [loading]);

	const removeEmpty = (option: (typeof options)[0]) => option.key !== "";

	return (
		<Select
			loading={loading}
			showSearch
			open={open}
			placeholder={`Select ${L(title)}`}
			allowClear
			value={value}
			style={{
				minWidth: "200px",
				width: "max-content",
			}}
			onChange={(value: string) => {
				handleFilter(value);
				confirm();
			}}
		>
			{options.filter(removeEmpty).map((cm) => (
				<Option key={cm.key} value={cm.value}>
					{cm.value}
				</Option>
			))}
		</Select>
	);
};

export default FilterSelect;
