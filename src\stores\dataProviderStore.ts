import { action, observable } from "mobx";
import dataProviderService from "../services/dataProvider/dataProviderService";
import type { CreateOrUpdateDataProviderInput } from "../services/dataProvider/dto/CreateOrUpdateDataProviderInput";
import { GetDataProviderOutput } from "../services/dataProvider/dto/GetDataSourceOutput";
import type { PagedFilterAndSortedRequestDataProvider } from "../services/dataProvider/dto/PagedFilterAndSortedRequestDataProvider";
import type { UpdateDataProviderInput } from "../services/dataProvider/dto/UpdateDataProviderInput";
import { EntityDto } from "../services/dto/entityDto";
import type { PagedResultDto } from "../services/dto/pagedResultDto";

class DataProviderStore {
	@observable datasProvider!: PagedResultDto<GetDataProviderOutput>;
	@observable editDataProvider!: CreateOrUpdateDataProviderInput;

	@action
	async create(
		createOrUpdateDataProviderInput: CreateOrUpdateDataProviderInput,
	) {
		const result = await dataProviderService.create(
			createOrUpdateDataProviderInput,
		);
		this.datasProvider.items.push(result);
	}

	@action
	async update(updateDataProvider: UpdateDataProviderInput) {
		const result = await dataProviderService.update(updateDataProvider);
		this.datasProvider.items = this.datasProvider.items.map(
			(x: GetDataProviderOutput) => {
				if (x.id === updateDataProvider.id) x = result;
				return x;
			},
		);
	}

	@action
	async delete(entityDto: EntityDto) {
		await dataProviderService.delete(entityDto);
		this.datasProvider.items = this.datasProvider.items.filter(
			(x: GetDataProviderOutput) => x.id !== entityDto.id,
		);
	}

	@action
	async get(entityDto: EntityDto) {
		const result = await dataProviderService.get(entityDto);
		this.editDataProvider = result;
	}

	@action
	async getAll(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequestDataProvider,
	) {
		const result = await dataProviderService.getAll(
			pagedFilterAndSortedRequest,
		);
		this.datasProvider = result;
	}
}

export default DataProviderStore;
