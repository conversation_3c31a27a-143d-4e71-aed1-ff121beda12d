import { WrappedFormUtils } from "@ant-design/compatible/lib/form/Form";
import { PlusOutlined, SettingOutlined } from "@ant-design/icons";
import {
	<PERSON><PERSON>,
	Card,
	Col,
	Dropdown,
	Menu,
	Modal,
	Row,
	Select,
	Table,
	Tag,
	Tooltip,
} from "antd";
import { ColumnProps } from "antd/lib/table";
import {
	FilterDropdownProps,
	SorterResult,
	TablePaginationConfig,
	TableRowSelection,
} from "antd/lib/table/interface";
import { inject, observer } from "mobx-react";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import FilterSelect from "../../components/FilterSelect/FilterSelect";
import { checkMarkIcon } from "../../components/SiderMenu/icons/icons";
import { L, isGranted } from "../../lib/abpUtility";
import { FilterByColumn } from "../../models/Sort/SortState";
import { EntityDto } from "../../services/dto/entityDto";
import { ListResultDto } from "../../services/dto/pagedResultDto";
import { GetShipOutput } from "../../services/ship/dto/getShipOutput";
import EventsStore from "../../stores/eventsStore";
import MeterConfigurationStore from "../../stores/meterConfigurationStore";
import ShipStore from "../../stores/shipStore";
import Stores from "../../stores/storeIdentifier";
import utils from "../../utils/utils";
import { ModalType } from "../ModalConsts";
import { reasonsForEventCustomData } from "../enumUtils";
import {
	getTablePaginationOptions,
	renderDate,
	renderFilterIcon,
} from "../renderUtils";
import CreateOrUpdateShip from "./components/createOrUpdateShip";

export interface IShipProps {
	shipStore: ShipStore;
	eventsStore: EventsStore;
	meterConfigurationStore: MeterConfigurationStore;
}

export interface IShipState {
	modalVisible: boolean;
	loading: boolean;
	fetchingFilters: boolean;
	sendingNotifications: boolean;
	maxResultCount: number;
	skipCount: number;
	shipId: number;
	filters: Array<FilterByColumn>;
	sorters: SorterResult<GetShipOutput>[];
	selectedRows: ListResultDto<EntityDto>;
	shipIds: number[];
	meterTypes: string[];
	reason: string;
	searchColumn: string;
}

const confirm = Modal.confirm;
const { Option } = Select;

type PreviousState = {
	filters: IShipState["filters"];
	sorters: IShipState["sorters"];
};

@inject(Stores.ShipStore)
@inject(Stores.EventsStore)
@inject(Stores.MeterConfigurationStore)
@observer
class Ship extends AppComponentBase<IShipProps, IShipState> {
	formRef?: WrappedFormUtils;

	state: IShipState = {
		modalVisible: false,
		loading: false,
		fetchingFilters: false,
		maxResultCount: 10,
		skipCount: 0,
		sendingNotifications: false,
		shipId: 0,
		filters: [],
		sorters: [],
		selectedRows: {
			items: [],
		},
		shipIds: [],
		meterTypes: [],
		reason: "",
		searchColumn: "",
	};

	async componentDidMount() {
		const prevState = this.extractPreviousState();

		this.setState({ ...prevState }, async () => {
			await Promise.all([this.getAll()]);
		});
	}

	extractPreviousState(): PreviousState {
		let state: PreviousState = {
			filters: [],
			sorters: [],
		};

		const prevState =
			utils.getSortAndFilterFromStorage<PreviousState>("ship-filters");

		if (prevState)
			state = {
				...prevState,
			};

		utils.removeStateFromStorage("ship-filters");

		return state;
	}

	componentWillUnmount(): void {
		const settings: PreviousState = {
			sorters: this.state.sorters,
			filters: this.state.filters,
		};

		utils.saveSortAndFilterToStorage("ship-filters", settings);
	}

	async getAll() {
		this.setState({ loading: true });
		const sortString = utils.getSorterString(this.state.sorters);

		const [searchColumnString, keywordString] = utils.getFilterStrings([
			...this.state.filters,
		]);

		await this.props.shipStore.getAll({
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: keywordString,
			searchColumn: searchColumnString,
			sorting: sortString,
		});
		this.setState({ loading: false });
	}

	private handleFilter(
		value: string,
		column: string,
		confirm: FilterDropdownProps["confirm"],
	) {
		let currentFilters = [...this.state.filters];
		currentFilters = currentFilters.filter((f) => f.column !== column);
		if (value) {
			currentFilters.push({ value, column: column });
		}

		this.setState({ filters: currentFilters }, async () => {
			await this.getAll();
			confirm?.();
		});
	}

	getColumnSearchProps = (
		dataIndex: keyof GetShipOutput,
		displayName: string,
	): ColumnProps<GetShipOutput> => ({
		filterDropdown: (props: FilterDropdownProps) => (
			<div>
				{props.visible && (
					<FilterSelect
						{...props}
						loading={this.state.fetchingFilters}
						handleFilter={(value) =>
							this.handleFilter(value, dataIndex, props.confirm)
						}
						title={displayName}
						value={
							this.state.filters.find((x) => x.column === dataIndex)?.value
						}
						options={this.props.shipStore.filters?.map((x) => {
							return { key: x, value: x };
						})}
					/>
				)}
			</div>
		),
		onFilterDropdownVisibleChange: (v) => {
			if (v) this.fetchFilters(dataIndex);
		},
		filterIcon: () =>
			renderFilterIcon(
				this.state.filters.findIndex((x) => x.column === dataIndex) >= 0,
			),
		sortOrder: this.state.sorters.find((x) => x.columnKey === dataIndex)?.order,
	});

	handleTableChange = (
		pagination: TablePaginationConfig,
		filters: Partial<Record<keyof GetShipOutput, string[]>>,
		sorter: SorterResult<GetShipOutput> | SorterResult<GetShipOutput>[],
	) => {
		const sorters = utils.getSorters(sorter);
		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
				sorters: sorters,
			},
			async () => await this.getAll(),
		);
	};

	Modal = () => {
		this.setState({
			modalVisible: !this.state.modalVisible,
		});
	};

	async createOrUpdateModalOpen(entityDto: EntityDto) {
		if (entityDto.id === 0) {
			await this.props.shipStore.createShip();
		} else {
			await this.props.shipStore.get(entityDto);
		}

		this.setState({ shipId: entityDto.id });
		this.Modal();

		if (!this.formRef) return;

		this.formRef.setFieldsValue({
			...this.props.shipStore.editShip,
		});
	}

	delete(input: EntityDto, shipName: string) {
		const self = this;
		confirm({
			title: `Do you want to delete ${shipName}?`,
			onOk() {
				self.props.shipStore.delete(input);
			},
			onCancel() {
				console.log("Cancel");
			},
		});
	}

	async reaquestMeterReadings() {
		const { shipIds, reason } = this.state;

		await this.props.eventsStore.requestMeterReadings({
			shipIds,
			meterTypes: ["All Meter Types"],
			reason: reason,
		});
	}

	async fetchFilters(property: string) {
		this.setState({ fetchingFilters: true });
		const [searchColumnString, keywordString] = utils.getFilterStrings([
			...this.state.filters,
		]);

		try {
			await this.props.shipStore.getFilters(
				{
					keyword: keywordString,
					searchColumn: searchColumnString,
				},
				property,
			);
		} catch {
			Modal.error({
				title: "Error during fetching",
				content: (
					<div>
						<p>Failed to fetch filters for column</p>
					</div>
				),
			});
		}

		this.setState({ fetchingFilters: false });
	}

	handleReadAllMeters = async () => {
		this.setState({ sendingNotifications: true });
		try {
			await this.reaquestMeterReadings();

			Modal.info({
				title: "Meter Read",
				content: (
					<div>
						<p style={{ display: "flex", justifyContent: "space-between" }}>
							{checkMarkIcon}Notification has been sent to selected vessels
						</p>
					</div>
				),
				onOk() {},
			});
		} catch (error) {
			Modal.error({
				title: "Meter Read Error",
				content: (
					<div>
						<p>Notification was not sent to the selected vessels</p>
					</div>
				),
				onOk() {},
			});
		}
		this.setState({ sendingNotifications: true });
	};

	handleCreate = () => {
		if (!this.formRef) return;
		const form = this.formRef;

		// biome-ignore lint/suspicious/noExplicitAny: Poor type implementation from library
		form.validateFields(async (err: any, values: any) => {
			if (err) {
				return;
			}
			this.setState({ loading: true });
			try {
				if (this.state.shipId === 0) {
					await this.props.shipStore.create(values);
				} else {
					await this.props.shipStore.update({
						...this.props.shipStore.editShip,
						id: this.state.shipId,
						...values,
					});
				}
				await this.getAll();
				this.setState({ modalVisible: false });
				form.resetFields();
			} catch (ex) {
			} finally {
				this.setState({ loading: false });
			}
		});
	};

	// biome-ignore lint/suspicious/noExplicitAny: Poor type implementation from library
	saveFormRef = (formRef: any) => {
		if (!formRef) return;
		this.formRef = formRef.props.form;
	};

	formatTimeToHHMM = (time: string) => {
		if (!time) return "";
		const parts = time.split(":");
		if (parts.length >= 2) {
			return `${parts[0]}:${parts[1]}`;
		}
		return time;
	};

	public render() {
		const { ships } = this.props.shipStore;
		// const meterTypes = Array.from(meterTypesForShipsCustomData, ([key, value]) => key);
		const reason = Array.from(reasonsForEventCustomData, ([key, value]) => key);
		const paginationOptions = getTablePaginationOptions(ships?.totalCount);
		const columns: Array<ColumnProps<GetShipOutput>> = [
			{
				title: L("Ship Name"),
				dataIndex: "shipName",
				key: "shipName",
				width: 200,
				sorter: { multiple: 1 },
				...this.getColumnSearchProps("shipName", L("Ship Name")),
			},
			{
				title: L("IMO"),
				dataIndex: "imoNumber",
				key: "imoNumber",
				width: 150,
				sorter: { multiple: 2 },
				...this.getColumnSearchProps("imoNumber", L("IMO")),
			},
			{
				title: L("MMSI"),
				dataIndex: "mmsi",
				key: "mmsi",
				width: 150,
				sorter: { multiple: 3 },
				...this.getColumnSearchProps("MMSI", L("MMSI")),
			},
			{
				title: L("Vessel Type"),
				dataIndex: "type",
				key: "type",
				width: 200,
				sorter: { multiple: 4 },
				...this.getColumnSearchProps("type", L("Vessel Type")),
			},
			{
				title: L("Vessel SDWT"),
				dataIndex: "sdwt",
				key: "sdwt",
				width: 200,
				sorter: true,
				sortOrder: this.state.sorters.find((x) => x.columnKey === "sdwt")
					?.order,
			},
			{
				title: L("Vessel GT"),
				dataIndex: "gt",
				key: "gt",
				width: 150,
				sorter: { multiple: 5 },
				sortOrder: this.state.sorters.find((x) => x.columnKey === "gt")?.order,
			},
			{
				title: L("Ship Email"),
				dataIndex: "shipEmail",
				key: "shipEmail",
				width: 150,
				sorter: { multiple: 6 },
				sortOrder: this.state.sorters.find((x) => x.columnKey === "shipEmail")
					?.order,
			},
			{
				title: L("Captain Name"),
				dataIndex: "captainName",
				key: "captainName",
				width: 180,
				sorter: { multiple: 6 },
				sortOrder: this.state.sorters.find((x) => x.columnKey === "captainName")
					?.order,
			},
			{
				title: L("Chief Engineers Name"),
				dataIndex: "chiefEngineersName",
				key: "chiefEngineersName",
				width: 250,
				sorter: { multiple: 7 },
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "chiefEngineersName",
				)?.order,
			},
			{
				title: L("Engine Room Unmanned Hours From"),
				dataIndex: "engineRoomUnmannedHoursFrom",
				key: "engineRoomUnmannedHoursFrom",
				width: 340,
				render: (text: string) => this.formatTimeToHHMM(text),
				sorter: { multiple: 8 },
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "engineRoomUnmannedHoursFrom",
				)?.order,
			},
			{
				title: L("Engine Room Unmanned Hours To"),
				dataIndex: "engineRoomUnmannedHoursTo",
				key: "engineRoomUnmannedHoursTo",
				width: 320,
				render: (text: string) => this.formatTimeToHHMM(text),
				sorter: { multiple: 9 },
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "engineRoomUnmannedHoursTo",
				)?.order,
			},
			{
				title: L("Scrubber"),
				dataIndex: "scrubber",
				key: "scrubber",
				width: 150,
				sorter: { multiple: 10 },
				sortOrder: this.state.sorters.find((x) => x.columnKey === "scrubber")
					?.order,
			},
			{
				title: L("Last Modified"),
				dataIndex: "lastModificationTime",
				key: "lastModificationTime",
				width: 230,
				render: (text: string) => renderDate(text, true),
				sorter: { multiple: 11 },
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "lastModificationTime",
				)?.order,
			},
			{
				title: L("Last Modifier User Id"),
				dataIndex: "lastModifierUserId",
				key: "lastModifierUserId",
				width: 230,
				sorter: { multiple: 12 },
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "lastModifierUserId",
				)?.order,
			},
			{
				title: L("Creator User Id"),
				dataIndex: "creatorUserId",
				key: "creatorUserId",
				width: 180,
				sorter: { multiple: 13 },
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "creatorUserId",
				)?.order,
			},
			{
				title: L("Emissions Notifications"),
				dataIndex: "",
				key: "",
				width: 250,
				render: (text: string, item: GetShipOutput) => (
					<div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
						<div
							style={{
								display: "flex",
								justifyContent: "space-between",
								alignItems: "center",
							}}
						>
							<span style={{ paddingRight: "10px" }}>
								{L("Arrival or Departure @ Berth or Anchorage")}
							</span>
							<div>
								{item.settings?.arrivalAndAnchorage ? (
									<Tag color="#2db7f5">{L("Yes")}</Tag>
								) : (
									<Tag color="red">{L("No")}</Tag>
								)}
							</div>
						</div>
						<div
							style={{
								display: "flex",
								justifyContent: "space-between",
								alignItems: "center",
							}}
						>
							<span>{L("Entering or Leaving ECA")}</span>
							<div>
								{item.settings?.enteringECA ? (
									<Tag color="#2db7f5">{L("Yes")}</Tag>
								) : (
									<Tag color="red">{L("No")}</Tag>
								)}
							</div>
						</div>
					</div>
				),
			},
			{
				title: L("Daily Reporting"),
				dataIndex: "",
				key: "",
				width: 250,
				render: (text: string, item: GetShipOutput) => (
					<div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
						<div
							style={{
								display: "flex",
								justifyContent: "space-between",
								alignItems: "center",
							}}
						>
							<span>06:00 (LT) Daily Report</span>
							<div>
								{item.settings?.noonReportAt6 ? (
									<Tag color="#2db7f5">{L("Yes")}</Tag>
								) : (
									<Tag color="red">{L("No")}</Tag>
								)}
							</div>
						</div>
						<div
							style={{
								display: "flex",
								justifyContent: "space-between",
								alignItems: "center",
							}}
						>
							<span>12:00 (LT) Daily Report</span>
							<div>
								{item.settings?.noonReportAt12 ? (
									<Tag color="#2db7f5">{L("Yes")}</Tag>
								) : (
									<Tag color="red">{L("No")}</Tag>
								)}
							</div>
						</div>
						<div
							style={{
								display: "flex",
								justifyContent: "space-between",
								alignItems: "center",
							}}
						>
							<span>18:00 (LT) Daily Report</span>
							<div>
								{item.settings?.noonReportAt18 ? (
									<Tag color="#2db7f5">{L("Yes")}</Tag>
								) : (
									<Tag color="red">{L("No")}</Tag>
								)}
							</div>
						</div>
					</div>
				),
			},
			{
				title: L("Actions"),
				width: 130,
				fixed: "right" as const,
				render: (text: string, item: GetShipOutput) => (
					<div>
						<Dropdown
							trigger={["click"]}
							overlay={
								<Menu>
									{isGranted("Pages.Ships-Edit") && (
										<Menu.Item
											onClick={() =>
												this.createOrUpdateModalOpen({ id: item.id })
											}
										>
											{L("Edit")}
										</Menu.Item>
									)}
									{isGranted("Pages.Ships-Delete") && (
										<Menu.Item
											onClick={() =>
												this.delete({ id: item.id }, item.shipName)
											}
										>
											{L("Delete")}
										</Menu.Item>
									)}
								</Menu>
							}
							placement="bottomLeft"
						>
							<Button type="primary" icon={<SettingOutlined />}>
								{L("Actions")}
							</Button>
						</Dropdown>
					</div>
				),
			},
		];

		const hasNoActions =
			!isGranted("Pages.Ships-Edit") && !isGranted("Pages.Ships-Delete");

		if (hasNoActions) {
			columns.pop();
		}

		const rowSelection: TableRowSelection<GetShipOutput> = {
			columnWidth: 60,
			onChange: (_, selectedRows: GetShipOutput[]) => {
				const selectedRowsItems: EntityDto[] = selectedRows.map((x) => {
					return {
						id: x.id,
					};
				});
				this.setState({
					selectedRows: {
						items: selectedRowsItems,
					},
					shipIds: selectedRowsItems.map((x) => x.id),
				});
			},
		};

		return (
			<Card>
				<Row
					style={{
						display: "flex",
						justifyContent: "space-between",
						alignItems: "flex-end",
					}}
				>
					{reason &&
						isGranted("Pages.Notifications-Request Meter Readings") && (
							<>
								<Col
									xs={{ span: 4, offset: 0 }}
									sm={{ span: 4, offset: 0 }}
									md={{ span: 4, offset: 0 }}
									lg={{ span: 4, offset: 0 }}
									xl={{ span: 4, offset: 0 }}
									xxl={{ span: 4, offset: 0 }}
									style={{
										display: "flex",
										flexDirection: "column",
										gap: "10px",
										justifyContent: "left",
									}}
								>
									<>
										<div>{L("Send Adhoc Notification to Vessel(s)")}</div>
										<Select
											style={{ width: "240px" }}
											placeholder="Select Notification"
											onChange={(value) => {
												this.setState({ reason: value.toString() });
											}}
										>
											{reason.map((x) => (
												<Option key={x} value={x}>
													{x}
												</Option>
											))}
										</Select>
									</>
								</Col>
								<Col
									xs={{ span: 14, offset: 0 }}
									sm={{ span: 14, offset: 0 }}
									md={{ span: 14, offset: 0 }}
									lg={{ span: 14, offset: 0 }}
									xl={{ span: 14, offset: 0 }}
									xxl={{ span: 14, offset: 0 }}
									style={{ display: "flex", justifyContent: "left" }}
								>
									<Tooltip
										placement="right"
										title={
											this.state.shipIds.length <= 0 ? (
												<div>Select at least one ship</div>
											) : null
										}
									>
										<Button
											type="primary"
											onClick={() => this.handleReadAllMeters()}
											disabled={!(this.state.shipIds.length > 0)}
											loading={this.state.sendingNotifications}
										>
											Send Notification
										</Button>
									</Tooltip>
								</Col>
							</>
						)}
					<Col
						xs={{ span: 4, offset: 0 }}
						sm={{ span: 4, offset: 0 }}
						md={{ span: 4, offset: 0 }}
						lg={{ span: 4, offset: 0 }}
						xl={{ span: 4, offset: 0 }}
						xxl={{ span: 4, offset: 0 }}
						style={{ display: "flex", justifyContent: "right" }}
					>
						{isGranted("Pages.Ships-Add") && (
							<Button
								type="primary"
								shape="circle"
								icon={<PlusOutlined />}
								style={{
									display: "flex",
									justifyContent: "center",
									alignItems: "center",
								}}
								onClick={() => this.createOrUpdateModalOpen({ id: 0 })}
							/>
						)}
					</Col>
				</Row>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey={(record) => record.id.toString()}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={ships === undefined || this.state.loading}
							dataSource={ships === undefined ? [] : ships.items}
							onChange={this.handleTableChange}
							scroll={{ y: 850 }}
							rowSelection={rowSelection}
						/>
					</Col>
				</Row>
				<CreateOrUpdateShip
					loading={this.state.loading}
					shipStore={this.props.shipStore}
					wrappedComponentRef={this.saveFormRef}
					visible={this.state.modalVisible}
					onCancel={() =>
						this.setState({
							modalVisible: false,
						})
					}
					modalType={
						this.state.shipId === 0 ? ModalType.create : ModalType.edit
					}
					onCreate={this.handleCreate}
				/>
				<Chat />
			</Card>
		);
	}
}

export default Ship;
