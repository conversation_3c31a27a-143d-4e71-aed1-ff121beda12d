import { action, observable } from "mobx";
import dataSourceService from "../services/dataSource/dataSourceService";
import type { CreateOrUpdateDataSourceInput } from "../services/dataSource/dto/CreateOrUpdateDataSourceInput";
import { GetDataSourceOutput } from "../services/dataSource/dto/GetDataSourceOutput";
import type { PagedFilterAndSortedRequestDataSource } from "../services/dataSource/dto/PagedFilterAndSortedRequestDataSource";
import type { UpdateDataSourceInput } from "../services/dataSource/dto/UpdateDataSourceInput";
import { EntityDto } from "../services/dto/entityDto";
import type { PagedResultDto } from "../services/dto/pagedResultDto";

class DataSourceStore {
	@observable dataSources!: PagedResultDto<GetDataSourceOutput>;
	@observable editDataSource!: CreateOrUpdateDataSourceInput;

	@action
	async create(createOrUpdateDataSourceInput: CreateOrUpdateDataSourceInput) {
		const result = await dataSourceService.create(
			createOrUpdateDataSourceInput,
		);
		this.dataSources.items.push(result);
	}

	@action
	async update(updateDataSourceInput: UpdateDataSourceInput) {
		const result = await dataSourceService.update(updateDataSourceInput);
		this.dataSources.items = this.dataSources.items.map(
			(x: GetDataSourceOutput) => {
				if (x.id === updateDataSourceInput.id) x = result;
				return x;
			},
		);
	}

	@action
	async delete(entityDto: EntityDto) {
		await dataSourceService.delete(entityDto);
		this.dataSources.items = this.dataSources.items.filter(
			(x: GetDataSourceOutput) => x.id !== entityDto.id,
		);
	}

	@action
	async get(entityDto: EntityDto) {
		const result = await dataSourceService.get(entityDto);
		this.editDataSource = result;
	}

	@action
	async getAll(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequestDataSource,
	) {
		const result = await dataSourceService.getAll(pagedFilterAndSortedRequest);
		this.dataSources = result;
	}
}

export default DataSourceStore;
