import { L } from "../../lib/abpUtility";
import LoadableComponent from "./../Loadable/index";

export const userRouter = [
	{
		path: "/user",
		name: "user",
		title: L("User"),
		component: LoadableComponent(
			() => import("../../components/Layout/UserLayout"),
		),
		isLayout: true,
		showInMenu: false,
	},
	{
		path: "/user/login",
		name: "login",
		title: L("LogIn"),
		component: LoadableComponent(() => import("../../scenes/Login")),
		showInMenu: false,
	},
	{
		path: "/account/resetPassword",
		name: "ResetPassword",
		title: L("ResetPassword"),
		component: LoadableComponent(
			() => import("../../scenes/Account/ResetPassword"),
		),
		showInMenu: false,
	},
];

export const appRouters = [
	{
		path: "/home",
		title: L("Home"),
		component: LoadableComponent(() => import("../../scenes/Home")),
	},
	{
		path: "/dashboard",
		name: "dashboard",
		permission: "Pages.Dashboard",
		title: L("Fleet Activity"),
		icon: "dashboard",
		showInMenu: false,
		component: LoadableComponent(() => import("../../scenes/Dashboard")),
	},
	{
		path: "/controlBoard",
		name: "controlBoard",
		permission: "Pages.ControlBoard",
		title: L("Data Flow Control Board"),
		icon: "controlBoard",
		showInMenu: false,
		component: LoadableComponent(() => import("../../scenes/ControlBoard")),
	},
	{
		path: "/map",
		name: "map",
		permission: "Pages.Map",
		title: L("Map"),
		icon: "map",
		showInMenu: false,
		component: LoadableComponent(() => import("../../scenes/Map")),
	},
	{
		path: "/users",
		permission: "Pages.Users-View",
		title: L("Users"),
		name: "user",
		icon: "users",
		showInMenu: true,
		backgroundColor: "#A9C8E3",
		component: LoadableComponent(() => import("../../scenes/Users")),
	},
	{
		path: "/devices",
		permission: "Pages.Devices-View",
		title: L("Devices"),
		name: "Devices",
		icon: "devices",
		showInMenu: true,
		backgroundColor: "#A9C8E3",
		component: LoadableComponent(
			() => import("../../scenes/DeviceRegistration"),
		),
	},
	{
		path: "/roles",
		permission: "Pages.Roles-View",
		title: L("Roles"),
		name: "role",
		icon: "roles",
		showInMenu: true,
		backgroundColor: "#A9C8E3",
		component: LoadableComponent(() => import("../../scenes/Roles")),
	},
	{
		path: "/vessels",
		permission: "Pages.Ships-View",
		title: L("Vessels"),
		name: "vessels",
		icon: "ships",
		showInMenu: true,
		hideForHost: true,
		backgroundColor: "#A9C8E3",
		component: LoadableComponent(() => import("../../scenes/Ships")),
	},
	{
		path: "/chartedVessels",
		permission: "Pages.ShipRentals-View",
		title: L("Chartered Vessels"),
		name: "chartedVessels",
		icon: "shipRental",
		showInMenu: true,
		backgroundColor: "#A9C8E3",
		component: LoadableComponent(() => import("../../scenes/ShipRental")),
	},
	{
		path: "/cargo",
		permission: "Pages.Cargo-View",
		title: L("Cargo"),
		name: "cargo",
		icon: "cargo",
		showInMenu: true,
		backgroundColor: "#A9C8E3",
		component: LoadableComponent(() => import("../../scenes/Cargo")),
	},
	{
		path: "/meterConfiguration",
		permission: "Pages.Meters-View",
		title: L("Meter Configuration"),
		name: "meterConfiguration",
		icon: "meters",
		showInMenu: true,
		backgroundColor: "#A9C8E3",
		component: LoadableComponent(
			() => import("../../scenes/MeterConfiguration"),
		),
	},
	{
		path: "/meterReadings",
		permission: "Pages.MeterReadings-View",
		title: L("Meter Readings"),
		name: "meterReading",
		icon: "meterReadings",
		showInMenu: true,
		backgroundColor: "#B1D6B3",
		component: LoadableComponent(() => import("../../scenes/MeterReadings")),
	},
	{
		path: "/notifications",
		permission: "Pages.Notifications-View",
		title: L("Notifications"),
		name: "notifications",
		icon: "notifications",
		showInMenu: true,
		backgroundColor: "#B1D6B3",
		component: LoadableComponent(() => import("../../scenes/Notifications")),
	},
	{
		path: "/consumptionFormulae",
		permission: "Pages.ConsumptionConsumers-View",
		title: L("Consumption Formulae"),
		name: "ConsumptionFormulae",
		icon: "consumptionConsumer",
		showInMenu: true,
		backgroundColor: "#EEDECA",
		component: LoadableComponent(
			() => import("../../scenes/ConsumptionFormulae"),
		),
	},
	{
		path: "/consumptionResults",
		permission: "Pages.ConsumptionResults-View",
		title: L("Consumption Results"),
		name: "ConsumptionResults",
		icon: "consumptionResults",
		showInMenu: true,
		backgroundColor: "#EEDECA",
		component: LoadableComponent(
			() => import("../../scenes/ConsumptionResults"),
		),
	},
	{
		path: "/emissionReport",
		permission: "Pages.EmissionReport-View",
		title: L("Emission Report"),
		name: "emissionReport",
		icon: "emissionReport",
		showInMenu: true,
		backgroundColor: "#F4A1E6",
		component: LoadableComponent(() => import("../../scenes/EmissionReport")),
	},
	{
		path: "/performanceReport",
		permission: "Pages.PerformanceReport-View",
		title: L("Performance Report"),
		name: "performanceReport",
		icon: "performanceReport",
		showInMenu: true,
		backgroundColor: "#F4A1E6",
		component: LoadableComponent(
			() => import("../../scenes/PerformanceReport"),
		),
	},
	{
		path: "/bunkeringDocuments",
		permission: "Pages.BunkeringNotes-View",
		title: L("Bunkering Documents"),
		name: "bunkeringDocuments",
		icon: "bunkerNotes",
		showInMenu: true,
		backgroundColor: "#BABFC8",
		component: LoadableComponent(() => import("../../scenes/BunkeringNotes")),
	},
	{
		path: "/fuelTypes",
		permission: "Pages.FuelTypes-View",
		title: L("Fuel Types"),
		name: "FuelTypes",
		icon: "fuelTypes",
		showInMenu: true,
		backgroundColor: "#BABFC8",
		component: LoadableComponent(() => import("../../scenes/FuelType")),
	},
	{
		path: "/dataSource",
		permission: "Pages.DataSource",
		title: L("Data Source"),
		name: "dataSource",
		icon: "dataSource",
		showInMenu: true,
		backgroundColor: "#BABFC8",
		component: LoadableComponent(() => import("../../scenes/DataSource")),
	},
	{
		path: "/dataProvider",
		permission: "Pages.DataProvider",
		title: L("Data Provider"),
		name: "dataProvider",
		icon: "dataProvider",
		showInMenu: true,
		component: LoadableComponent(() => import("../../scenes/DataProvider")),
	},
	{
		path: "/shipInfo",
		permission: "Pages.ShipInfo",
		title: L("Ship Info"),
		name: "shipInfo",
		icon: "shipInfo",
		showInMenu: true,
		component: LoadableComponent(() => import("../../scenes/ShipInfo")),
	},
	{
		path: "/passages",
		permission: "Pages.Passages-View",
		title: L("Passages"),
		name: "passages",
		icon: "passages",
		showInMenu: false,
		component: LoadableComponent(() => import("../../scenes/Passages")),
	},
	{
		path: "/events",
		permission: "Pages.Events",
		title: L("Events"),
		name: "events",
		icon: "events",
		showInMenu: true,
		component: LoadableComponent(() => import("../../scenes/Events")),
	},
	{
		path: "/answersForEvents",
		permission: "Pages.AnswersForEvents",
		title: L("Answers For Events"),
		name: "answersForEvents",
		icon: "answersForEvents",
		showInMenu: true,
		component: LoadableComponent(() => import("../../scenes/AnswersForEvents")),
	},
	// {
	//   path: '/voyages',
	//   permission: 'Pages.Voyages',
	//   title: L('Voyages'),
	//   name: 'voyages',
	//   icon: 'voyages',
	//   showInMenu: true,
	//   component: LoadableComponent(() => import('../../scenes/Voyages')),
	// },

	{
		path: "/ocr",
		permission: "Pages.OCR-View",
		title: L("Ocr"),
		name: "ocr",
		icon: "OCR",
		showInMenu: false,
		backgroundColor: "#BABFC8",
		component: LoadableComponent(() => import("../../scenes/Ocr")),
	},
	{
		path: "/mailRecipients",
		permission: "Pages.MailRecipients",
		title: L("Mail Recipients"),
		name: "MailRecipients",
		icon: "mailRecipients",
		showInMenu: true,
		component: LoadableComponent(() => import("../../scenes/MailRecipients")),
	},
	// {
	//   path: '/drafts',
	//   permission: 'Pages.Drafts',
	//   title: L('Drafts'),
	//   name: 'Drafts',
	//   icon: 'drafts',
	//   showInMenu: true,
	//   component: LoadableComponent(() => import('../../scenes/Drafts')),
	// },
	// {
	//   path: '/integrations',
	//   permission: 'Pages.Integrations',
	//   title: L('Integrations'),
	//   name: 'Integrations',
	//   icon: 'integrations',
	//   showInMenu: true,
	//   component: LoadableComponent(() => import('../../scenes/Integrations')),
	// },
	// {
	//   path: '/consumptionCalculation',
	//   permission: 'Pages.FuelTypes',
	//   title: L('Consumption Calculation'),
	//   name: 'ConsumptionCalculation',
	//   icon: 'consumptionCalculation',
	//   showInMenu: true,
	//   component: LoadableComponent(() => import('../../scenes/ConsumptionCalculation')),
	// },
	{
		path: "/",
		exact: true,
		name: "home",
		permission: "",
		title: L("Home"),
		icon: "home",
		component: LoadableComponent(
			() => import("../../components/Layout/AppLayout"),
		),
		isLayout: true,
		showInMenu: false,
	},
	{
		path: "/vesselOperators",
		permission: "Pages.Tenants",
		title: L("Vessel Operators"),
		name: "vesselOperator",
		icon: "vesselOperator",
		showInMenu: true,
		component: LoadableComponent(() => import("../../scenes/Tenants")),
	},
	{
		path: "/licenceManager",
		permission: "Pages.Tenants",
		title: L("Licence Manager"),
		name: "licenceManager",
		icon: "licenceManager",
		showInMenu: true,
		component: LoadableComponent(() => import("../../scenes/LicenceManager")),
	},
	{
		path: "/integrators",
		permission: "Pages.Integrators-View",
		title: L("Integrators"),
		name: "integrators",
		icon: "integrations",
		showInMenu: true,
		component: LoadableComponent(() => import("../../scenes/Integrators")),
	},
	{
		path: "/logout",
		permission: "",
		title: L("Logout"),
		name: "logout",
		icon: "info-circle",
		showInMenu: false,
		component: LoadableComponent(() => import("../../components/Logout")),
	},
	{
		path: "/exception",
		permission: "",
		title: L("exception"),
		name: "exception",
		icon: "info-circle",
		showInMenu: false,
		component: LoadableComponent(() => import("../../scenes/Exception")),
	},
];

export const routers = [...userRouter, ...appRouters];
