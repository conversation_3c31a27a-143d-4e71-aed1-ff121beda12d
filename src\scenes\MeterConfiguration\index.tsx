import { PlusOutlined, SettingOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, <PERSON>, Col, Dropdown, InputRef, <PERSON>u, Modal, Row } from "antd";
import MenuItem from "antd/lib/menu/MenuItem";
import Table, { ColumnProps } from "antd/lib/table";
import {
	FilterDropdownProps,
	SorterResult,
	TablePaginationConfig,
} from "antd/lib/table/interface";
import { inject, observer } from "mobx-react";
import * as React from "react";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import FilterSelect from "../../components/FilterSelect/FilterSelect";
import { L, isGranted } from "../../lib/abpUtility";
import { FilterByColumn } from "../../models/Sort/SortState";
import { EntityDto } from "../../services/dto/entityDto";
import { GetMeterConfigurationOutput } from "../../services/meterConfiguration/dto/GetMeterConfigurationOutput";
import FuelTypeStore from "../../stores/fuelTypeStore";
import MeterConfigurationStore from "../../stores/meterConfigurationStore";
import ShipStore from "../../stores/shipStore";
import Stores from "../../stores/storeIdentifier";
import utils from "../../utils/utils";
import { ModalType } from "../ModalConsts";
import {
	getTablePaginationOptions,
	renderDate,
	renderFilterIcon,
} from "../renderUtils";
import CreateOrUpdateMeterConfiguration from "./components/createOrUpdateMeterConfiguration";

export interface IMeterConfigurationProps {
	meterConfigurationStore: MeterConfigurationStore;
	shipStore: ShipStore;
	fuelTypeStore: FuelTypeStore;
}

interface IMeterConfigurationDateFilters {
	lastValidReadingMeterDateStart?: string;
	lastValidReadingMeterDateEnd?: string;
}

export interface IMeterConfigurationState {
	maxResultCount: number;
	skipCount: number;
	sorting: string;
	searchTextTable: string;
	sorters: SorterResult<GetMeterConfigurationOutput>[];
	modalVisible: boolean;
	modalDetailsVisible: boolean;
	id: number;
	filters: Array<FilterByColumn>;
	meterId: number;
	okButtonDisabled: boolean;
	fetchingFilters: boolean;
	isPdfModalVisible: boolean;
	fileUrl: string;
	selectedPdfRecord: GetMeterConfigurationOutput | null;
	fuelType: string;
	maxResultCountFuelType: number;
	skipCountFuelType: number;
	sortingFuelType: string;
	searchColumn: string;
	loading: boolean;
	activeDateFilters: IMeterConfigurationDateFilters;
}

const confirm = Modal.confirm;

type PreviousState = {
	filters: IMeterConfigurationState["filters"];
	sorters: IMeterConfigurationState["sorters"];
	activeDateFilters: IMeterConfigurationState["activeDateFilters"];
};

@inject(Stores.MeterConfigurationStore)
@inject(Stores.ShipStore)
@inject(Stores.FuelTypeStore)
@observer
class MeterConfiguration extends AppComponentBase<
	IMeterConfigurationProps,
	IMeterConfigurationState
> {
	searchInput: InputRef | null = null;
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	formRef?: any;

	state: IMeterConfigurationState = {
		maxResultCount: 10,
		skipCount: 0,
		searchTextTable: "",
		sorting: "",
		sorters: [],
		fuelType: "",
		modalVisible: false,
		fetchingFilters: false,
		modalDetailsVisible: false,
		id: 0,
		okButtonDisabled: false,
		meterId: 0,
		filters: [],
		isPdfModalVisible: false,
		fileUrl: "",
		selectedPdfRecord: null,
		maxResultCountFuelType: 100,
		skipCountFuelType: 0,
		sortingFuelType: "",
		searchColumn: "",
		loading: false,
		activeDateFilters: {
			lastValidReadingMeterDateStart: undefined,
			lastValidReadingMeterDateEnd: undefined,
		},
	};

	async componentDidMount() {
		const prevState = this.extractPreviousState();

		this.setState({ ...prevState }, async () => {
			await Promise.all([
				this.getAll(),
				this.props.fuelTypeStore.getAll({
					maxResultCount: this.state.maxResultCountFuelType,
					skipCount: this.state.skipCountFuelType,
					keyword: this.state.sortingFuelType,
				}),
			]);
		});
	}

	componentWillUnmount(): void {
		const settings: PreviousState = {
			sorters: this.state.sorters,
			filters: this.state.filters,
			activeDateFilters: this.state.activeDateFilters,
		};

		utils.saveSortAndFilterToStorage("meters-filters", settings);
	}

	async fetchFilters(property: string) {
		this.setState({ fetchingFilters: true });
		const [searchColumnString, keywordString] = utils.getFilterStrings([
			...this.state.filters,
		]);

		try {
			await this.props.meterConfigurationStore.getFilters(
				{
					keyword: keywordString,
					searchColumn: searchColumnString,
				},
				property,
			);
		} catch {
			Modal.error({
				title: "Error during fetching",
				content: (
					<div>
						<p>Failed to fetch filters for column</p>
					</div>
				),
			});
		}

		this.setState({ fetchingFilters: false });
	}

	extractPreviousState(): PreviousState {
		let state: PreviousState = {
			filters: [],
			sorters: [],
			activeDateFilters: {},
		};

		const prevState =
			utils.getSortAndFilterFromStorage<PreviousState>("meters-filters");

		if (prevState)
			state = {
				...prevState,
			};

		utils.removeStateFromStorage("meters-filters");

		return state;
	}

	async getAll() {
		this.setState({ loading: true });
		const sortString = utils.getSorterString(this.state.sorters);

		const [searchColumnString, keywordString] = utils.getFilterStrings([
			...this.state.filters,
		]);

		await this.props.meterConfigurationStore.getAll({
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: keywordString,
			searchColumn: searchColumnString,
			sorting: sortString,
			lastValidReadingMeterDateStart:
				this.state.activeDateFilters.lastValidReadingMeterDateStart || "",
			lastValidReadingMeterDateEnd:
				this.state.activeDateFilters.lastValidReadingMeterDateEnd || "",
		});
		this.setState({ loading: false });
	}

	Modal = () => {
		this.setState({
			modalVisible: !this.state.modalVisible,
		});
	};

	ModalDetails = () => {
		this.setState({
			modalDetailsVisible: !this.state.modalDetailsVisible,
		});
	};

	showPdfModal = (record: GetMeterConfigurationOutput) => {
		this.setState({
			isPdfModalVisible: true,
			selectedPdfRecord: record,
		});
	};

	hidePdfModal = () => {
		this.setState({
			isPdfModalVisible: false,
			selectedPdfRecord: null,
		});
	};

	handleDateFilter = (startDate?: string, endDate?: string) => {
		this.setState(
			(prevState) => ({
				activeDateFilters: {
					...prevState.activeDateFilters,
					lastValidReadingMeterDateStart: startDate,
					lastValidReadingMeterDateEnd: endDate,
				},
			}),
			() => {
				this.getAll();
			},
		);
	};

	async createOrUpdateModalOpen(entityDto: EntityDto) {
		if (entityDto.id === 0) {
			await this.props.meterConfigurationStore.createMeterConfiguration();
		} else {
			await this.props.meterConfigurationStore.get(entityDto);
		}

		this.setState({ id: entityDto.id, fileUrl: this.state.fileUrl });
		this.Modal();

		this.formRef.props.form.setFieldsValue({
			...this.props.meterConfigurationStore.editMeterConfiguration,
		});
	}

	delete(input: EntityDto) {
		const self = this;
		confirm({
			title: "Do you want to delete these items?",
			onOk() {
				self.props.meterConfigurationStore.delete(input);
			},
			onCancel() {
				console.log("Cancel");
			},
		});
	}

	handleCreate = () => {
		if (this.state.okButtonDisabled === false) {
			const form = this.formRef.props.form;
			// biome-ignore lint/suspicious/noExplicitAny: <explanation>
			form.validateFields(async (err: any, values: any) => {
				if (err) {
					return;
				}

				this.setState({ okButtonDisabled: true });
				try {
					if (this.state.id === 0) {
						await this.props.meterConfigurationStore.create(
							values,
							this.formRef.state.selectedFile,
						);
					} else {
						await this.props.meterConfigurationStore.update(
							{
								...this.props.meterConfigurationStore.editMeterConfiguration,
								...values,
							},
							this.formRef.state.selectedFile,
						);
					}
					form.resetFields();
					this.setModalVisibleFalse();
					await this.getAll();
				} catch (ex) {
				} finally {
					this.setState({ okButtonDisabled: false });
				}
			});
		}
	};

	setModalVisibleFalse = () => {
		this.setState({ modalVisible: false });
		this.formRef.state.selectedFile = null;
		this.formRef.state.fileUploadRef.current.value = null;
		this.formRef.props.form.resetFields(["fileUrl"]);
	};

	// biome-ignore lint/suspicious/noExplicitAny: Poor type implementation from library
	saveFormRef = (formRef: any) => {
		if (!formRef) return;
		this.formRef = formRef;
	};

	private handleFilter(
		value: string,
		column: string,
		confirm: FilterDropdownProps["confirm"],
	) {
		let currentFilters = [...this.state.filters];
		currentFilters = currentFilters.filter((f) => f.column !== column);
		if (value) {
			currentFilters.push({ value, column: column });
		}

		this.setState({ filters: currentFilters }, async () => {
			await this.getAll();
			confirm?.();
		});
	}

	getColumnSearchProps = (
		dataIndex: string,
		displayName: string,
	): ColumnProps<GetMeterConfigurationOutput> => ({
		filterDropdown: (props: FilterDropdownProps) => (
			<div>
				{props.visible && (
					<FilterSelect
						{...props}
						loading={this.state.fetchingFilters}
						handleFilter={(value) =>
							this.handleFilter(value, dataIndex, props.confirm)
						}
						title={displayName}
						value={
							this.state.filters.find((x) => x.column === dataIndex)?.value
						}
						options={this.props.meterConfigurationStore.filters?.map((x) => {
							return { key: x, value: x };
						})}
					/>
				)}
			</div>
		),
		onFilterDropdownVisibleChange: (v) => {
			if (v) this.fetchFilters(dataIndex);
		},
		filterIcon: () =>
			renderFilterIcon(
				this.state.filters.findIndex((x) => x.column === dataIndex) >= 0,
			),
		sortOrder: this.state.sorters.find((x) => x.columnKey === dataIndex)?.order,
	});

	handleTableChange = (
		pagination: TablePaginationConfig,
		filters: Partial<Record<keyof GetMeterConfigurationOutput, string[]>>,
		sorter:
			| SorterResult<GetMeterConfigurationOutput>
			| SorterResult<GetMeterConfigurationOutput>[],
	) => {
		const sorters = utils.getSorters(sorter);

		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
				sorters: sorters,
			},
			async () => await this.getAll(),
		);
	};

	handleSearchTable = (
		selectedKeys: React.Key[],
		dataIndex: string,
		confirm?: () => void,
	) => {
		this.setState(
			{
				sorting: selectedKeys[0].toString(),
				searchColumn: dataIndex,
			},
			async () => await this.getAll(),
		);
	};

	public render() {
		const { meterConfigurations } = this.props.meterConfigurationStore;
		const paginationOptions = getTablePaginationOptions(
			meterConfigurations?.totalCount,
		);
		const columns: ColumnProps<GetMeterConfigurationOutput>[] = [
			{
				title: L("Ship Name"),
				dataIndex: ["ship", "shipName"],
				key: "ship.shipName",
				width: 180,
				sorter: { multiple: 14 },
				...this.getColumnSearchProps("ship.shipName", L("Ship Name")),
			},
			{
				title: L("IMO"),
				dataIndex: ["ship", "imoNumber"],
				key: "ship.imoNumber",
				width: 150,
				sorter: { multiple: 13 },
				...this.getColumnSearchProps("ship.imoNumber", L("IMO")),
			},
			{
				title: L("Meter Name"),
				dataIndex: "name",
				key: "name",
				width: 180,
				sorter: { multiple: 12 },
				...this.getColumnSearchProps("name", L("Meter Name")),
			},
			{
				width: 220,
				title: L("Control Image"),
				render: (text: string, record: GetMeterConfigurationOutput) => (
					<div>
						<Button type="primary" onClick={() => this.showPdfModal(record)}>
							{L("Show Control Image")}
						</Button>
					</div>
				),
			},
			{
				title: L("Meter Type"),
				dataIndex: "meterType",
				key: "meterType",
				width: 180,
				sorter: { multiple: 11 },
				...this.getColumnSearchProps("meterType", L("Meter Type")),
			},
			{
				title: L("Measured Commodity"),
				dataIndex: "measuringCommodity",
				key: "measuringCommodity",
				width: 240,
				sorter: { multiple: 10 },
				...this.getColumnSearchProps(
					"measuringCommodity",
					L("Measured Commodity"),
				),
			},
			{
				title: L("Unit Of Measure"),
				dataIndex: "unitOfMeasure",
				key: "unitOfMeasure",
				width: 200,
				sorter: { multiple: 9 },
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "unitOfMeasure",
				)?.order,
			},
			{
				title: L("Digits Count"),
				dataIndex: "digitsCount",
				key: "digitsCount",
				width: 180,
				sorter: { multiple: 8 },
				sortOrder: this.state.sorters.find((x) => x.columnKey === "digitsCount")
					?.order,
			},
			{
				title: L("Decimal Point Position"),
				dataIndex: "decimalPointPosition",
				key: "decimalPointPosition",
				width: 230,
				sorter: { multiple: 7 },
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "decimalPointPosition",
				)?.order,
			},
			{
				title: L("Flow Direction"),
				dataIndex: "flowDirection",
				key: "flowDirection",
				width: 180,
				sorter: { multiple: 6 },
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "flowDirection",
				)?.order,
			},
			{
				title: L("Measurement Type"),
				dataIndex: "measurementType",
				key: "measurementType",
				width: 220,
				sorter: { multiple: 5 },
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "measurementType",
				)?.order,
			},
			{
				title: L("Fuel Type"),
				dataIndex: "fuelType",
				key: "fuelType",
				width: 150,
				sorter: { multiple: 4 },
				sortOrder: this.state.sorters.find((x) => x.columnKey === "fuelType")
					?.order,
			},
			{
				title: L("Last Valid Reading Meter Date"),
				dataIndex: "lastValidReadingMeterDate",
				key: "lastValidReadingMeterDate",
				width: 280,
				sorter: { multiple: 2 },
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "lastValidReadingMeterDate",
				)?.order,
				render: (date) =>
					date === "0001-01-01T00:00:00" ? "" : renderDate(date),
			},
			{
				title: L("Actions"),
				width: 150,
				fixed: "right" as const,
				render: (text: string, item) => (
					<div>
						<Dropdown
							trigger={["click"]}
							overlay={
								<Menu>
									{isGranted("Pages.Meters-Edit") && (
										<MenuItem
											onClick={() =>
												this.createOrUpdateModalOpen({ id: item.id })
											}
										>
											{L("Edit")}
										</MenuItem>
									)}
									{isGranted("Pages.Meters-Delete") && (
										<MenuItem onClick={() => this.delete({ id: item.id })}>
											{L("Delete")}
										</MenuItem>
									)}
								</Menu>
							}
							placement="bottomLeft"
						>
							<Button type="primary" icon={<SettingOutlined />}>
								{L("Actions")}
							</Button>
						</Dropdown>
					</div>
				),
			},
		];

		const hasNoActions =
			!isGranted("Pages.Meters-Edit") && !isGranted("Pages.Meters-Delete");

		if (hasNoActions) {
			columns.pop();
		}

		return (
			<Card>
				<Row>
					<Col
						xs={{ span: 23, offset: 0 }}
						sm={{ span: 23, offset: 0 }}
						md={{ span: 23, offset: 0 }}
						lg={{ span: 23, offset: 0 }}
						xl={{ span: 23, offset: 0 }}
						xxl={{ span: 23, offset: 0 }}
						style={{ display: "flex", justifyContent: "end" }}
					>
						{isGranted("Pages.Meters-Add") && (
							<Button
								type="primary"
								shape="circle"
								icon={<PlusOutlined />}
								style={{
									display: "flex",
									justifyContent: "center",
									alignItems: "center",
								}}
								onClick={() => this.createOrUpdateModalOpen({ id: 0 })}
							/>
						)}
					</Col>
				</Row>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey={(record: GetMeterConfigurationOutput) =>
								record.id.toString()
							}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={this.state.loading}
							dataSource={
								meterConfigurations === undefined
									? []
									: meterConfigurations.items
							}
							onChange={this.handleTableChange}
							scroll={{ y: 850 }}
						/>
					</Col>
				</Row>
				<Modal
					title="Control image viewer"
					visible={this.state.isPdfModalVisible}
					onCancel={this.hidePdfModal}
					footer={null}
				>
					<div className="pdfContainer" id="pdfContainer">
						(
						<img
							src={this.state.selectedPdfRecord?.fileUrl}
							style={{ width: "100%", height: "auto" }}
							alt="Control Image"
						/>
						)
					</div>
				</Modal>
				<CreateOrUpdateMeterConfiguration
					fuelTypesStore={this.props.fuelTypeStore}
					shipStore={this.props.shipStore}
					meterConfigurationStore={this.props.meterConfigurationStore}
					wrappedComponentRef={this.saveFormRef}
					visible={this.state.modalVisible}
					onCancel={this.setModalVisibleFalse}
					modalType={this.state.id === 0 ? ModalType.create : ModalType.edit}
					onCreate={this.handleCreate}
					roles={this.props.meterConfigurationStore.roles}
					okButtonDisabled={this.state.okButtonDisabled}
				/>
				<Chat />
			</Card>
		);
	}
}

export default MeterConfiguration;
