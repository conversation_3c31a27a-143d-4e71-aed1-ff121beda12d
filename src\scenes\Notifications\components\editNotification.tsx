import { Form } from "@ant-design/compatible";
import * as React from "react";
import AppComponentBase from "../../../components/AppComponentBase";
import NotificationsStore from "../../../stores/notificationsStore";
import { ModalFormComponentProps } from "../../modalFormComponentProps";
import "@ant-design/compatible/assets/index.css";
import { InputNumber, Modal } from "antd";
import FormItem from "antd/lib/form/FormItem";
import ComponentLayout from "../../../components/Layout/ComponentLayout";
import { L } from "../../../lib/abpUtility";
import { FormWidths } from "../../ViewSettingsConsts";

export interface IEditNotificationProps extends ModalFormComponentProps {
	notificationsStore: NotificationsStore;
}

class EditNotification extends AppComponentBase<IEditNotificationProps> {
	render() {
		const { getFieldDecorator } = this.props.form;
		const { visible, onCancel, onCreate, okButtonDisabled } = this.props;

		return (
			<Modal
				visible={visible}
				cancelText={L("Cancel")}
				okText={L("OK")}
				onCancel={onCancel}
				onOk={onCreate}
				title={L("Cargo Quantity")}
				okButtonProps={{ loading: okButtonDisabled }}
			>
				<Form layout="vertical">
					<FormItem
						label={L("Cargo Quantity")}
						{...ComponentLayout.formItemLayout}
					>
						{getFieldDecorator("cargoQuantity")(
							<InputNumber
								style={{ width: FormWidths.wide, marginLeft: "15px" }}
							/>,
						)}
					</FormItem>
				</Form>
			</Modal>
		);
	}
}

export default Form.create<IEditNotificationProps>()(EditNotification);
