import { action, observable } from "mobx";
import fileService from "../services/azure/fileService";
import { EntityDto } from "../services/dto/entityDto";
import type { PagedResultDto } from "../services/dto/pagedResultDto";
import { MeterNameAndUuidDto } from "../services/meter/dto/meterNameAndUuidDto";
import type { CreateOrUpdateMeterConfigurationInput } from "../services/meterConfiguration/dto/CreateOrUpdateMeterConfigurationInput";
import { GetMeterConfigurationOutput } from "../services/meterConfiguration/dto/GetMeterConfigurationOutput";
import type { PagedFilterAndSortedRequestMeterConfiguration } from "../services/meterConfiguration/dto/PagedFilterAndSortedRequestMeterConfiguration";
import meterConfigurationService from "../services/meterConfiguration/meterConfigurationService";
import { GetShipOutput } from "../services/ship/dto/getShipOutput";
import { GetRoles } from "../services/user/dto/getRolesOuput";
import ShipStore from "./shipStore";

export const DefaultDecimalPoint = 2;

class MeterConfigurationStore {
	@observable meterConfigurations!: PagedResultDto<GetMeterConfigurationOutput>;
	@observable editMeterConfiguration!: CreateOrUpdateMeterConfigurationInput;
	@observable allMetersForShip!: {
		guid: string;
		name: string;
		archived: boolean;
	}[];
	@observable meterNames!: { guid: string; name: string }[];
	@observable roles: GetRoles[] = [];
	@observable filters: string[] = [];
	@observable ships!: GetShipOutput[];
	@observable fuelType!: { [key: string]: string };
	@observable fileUrl!: { [key: string]: string };
	@observable unitOfMeasure!: { [key: string]: string };
	@observable meterNameAndUuids!: MeterNameAndUuidDto[];
	@observable
	metersForCurrentShip!: PagedResultDto<GetMeterConfigurationOutput>;
	@observable meterValidationRules!: { [key: number]: string };

	shipStore: ShipStore = new ShipStore();

	@action
	async getFilters(
		filters: { keyword: string; searchColumn: string },
		property: string,
	) {
		const result = await meterConfigurationService.getFilters(
			filters,
			property,
		);

		this.filters = result;
	}

	@action
	async create(
		createMeterConfigurationInput: CreateOrUpdateMeterConfigurationInput,
		file: File | null,
	) {
		if (file !== null) {
			const fileName = await fileService.uploadFileMeterConfiguration(file);
			createMeterConfigurationInput.fileUrl = fileName;
		}
		const result = await meterConfigurationService.create(
			createMeterConfigurationInput,
		);
		this.meterConfigurations.items.push(result);
	}

	@action
	async update(
		updateMeterConfigurationInput: CreateOrUpdateMeterConfigurationInput,
		file: File | null,
	) {
		if (file != null) {
			const fileName = await fileService.uploadFileMeterConfiguration(file);
			updateMeterConfigurationInput.fileUrl = fileName;
		} else {
			updateMeterConfigurationInput.fileUrl =
				this.editMeterConfiguration.fileUrl;
		}
		const result = await meterConfigurationService.update(
			updateMeterConfigurationInput,
		);
		this.meterConfigurations.items = this.meterConfigurations.items.map(
			(x: GetMeterConfigurationOutput) => {
				if (x.id === updateMeterConfigurationInput.id) return result;
				return x;
			},
		);
	}

	@action
	async delete(entityDto: EntityDto) {
		await meterConfigurationService.delete(entityDto);
		this.meterConfigurations.items = this.meterConfigurations.items.filter(
			(x: GetMeterConfigurationOutput) => x.id !== entityDto.id,
		);
	}

	@action
	async getMeterNames() {
		const result = await meterConfigurationService.getMeterNames();
		this.meterNames = result;
	}

	@action
	async get(entityDto: EntityDto) {
		const result = await meterConfigurationService.get(entityDto);
		this.editMeterConfiguration = result;
	}

	@action
	async createMeterConfiguration() {
		await this.shipStore.getShipNames();
		const shipId = this.shipStore.allShipNames[0]?.id;
		this.editMeterConfiguration = {
			uuid: "",
			name: "",
			meterType: "",
			unitOfMeasure: "",
			digitsCount: 0,
			decimalPointPosition: 0,
			flowDirection: "",
			measurementType: "",
			fuelType: "",
			fileUrl: "",
			shipId: shipId,
			ship: {
				id: 0,
				shipName: "",
				imoNumber: "",
			},
			scheduledReadings: [],
			id: 0,
			consumerType: "",
			additionalScanHours: [],
			meterId: 0,
			lastValidMeterValue: 0,
			lastValidReadingMeterDate: new Date(),
			shipName: "",
			measuringCommodity: "",
		};
	}

	@action
	async getAll(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequestMeterConfiguration,
	) {
		const result = await meterConfigurationService.getAll(
			pagedFilterAndSortedRequest,
		);
		this.meterConfigurations = result;
	}

	@action
	async metersForShip(shipId: number, meterType: string) {
		const result = await meterConfigurationService.metersForShip(
			shipId,
			meterType,
		);
		this.allMetersForShip = result;
	}
}

export default MeterConfigurationStore;
