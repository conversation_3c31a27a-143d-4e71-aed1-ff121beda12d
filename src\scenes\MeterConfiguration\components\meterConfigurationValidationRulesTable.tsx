import React from "react";
import AppComponentBase from "../../../components/AppComponentBase";
import EnumFlagsTable from "../../../components/Enum/enumFlagsTable";

interface MeterConfigurationValidationRulesTableProps {
	value?: MeterConfigurationValidationRules;
	onChange?: (value: MeterConfigurationValidationRules | undefined) => void;
	meterConfigurationValidationRules: { [key: number]: string };
}

export enum MeterConfigurationValidationRules {
	ExactNumberOfDigits = 1 << 0,
	MaximumNumberOfDigits = 1 << 1,
	LowerReadingValidation = 1 << 2,
}

export default class MeterConfigurationValidationRulesTable extends AppComponentBase<MeterConfigurationValidationRulesTableProps> {
	handleTableChange = (value: number | undefined) => {
		const enumValue: MeterConfigurationValidationRules = this.validateSelection(
			value as MeterConfigurationValidationRules,
		);
		if (this.props.onChange) {
			this.props.onChange(enumValue);
		}
	};

	validateSelection = (
		selected: MeterConfigurationValidationRules,
	): MeterConfigurationValidationRules => {
		if (this.props.value && selected) {
			if (
				this.props.value.hasFlag(
					MeterConfigurationValidationRules.ExactNumberOfDigits,
				) &&
				selected.hasFlag(
					MeterConfigurationValidationRules.MaximumNumberOfDigits,
				)
			) {
				return selected.removeFlag(
					MeterConfigurationValidationRules.ExactNumberOfDigits,
				);
			}
			if (
				this.props.value.hasFlag(
					MeterConfigurationValidationRules.MaximumNumberOfDigits,
				) &&
				selected.hasFlag(MeterConfigurationValidationRules.ExactNumberOfDigits)
			) {
				return selected.removeFlag(
					MeterConfigurationValidationRules.MaximumNumberOfDigits,
				);
			}
		}
		return selected;
	};

	render() {
		return (
			<>
				<EnumFlagsTable
					value={this.props.value}
					onChange={this.handleTableChange}
					enumDataSource={this.props.meterConfigurationValidationRules}
					enumDataSourceNameSufix="Applies"
				/>
			</>
		);
	}
}
