export interface CreateOrUpdateNotificationsInput {
	totalCount: number;
	sent: number;
	sentPercent: number;
	received: number;
	receivedPercent: number;
	cancelled: number;
	cancelledPercent: number;
	inProgress: number;
	inProgressPercent: number;
	done: number;
	cargoQuantity: number;
	eventId: number;
	donePercent: number;
	notifications: {
		consumptionResult: {
			consumption24h: number;
			consumptionPerHour: number;
			consumptionTotal: number;
			name: string;
		};
		cargoQuantity: number;
		eventId: number;
		guid: string;
		creationTime: Date;
		title: string;
		description: string;
		statuses: {
			status: string;
			creationTime: Date;
		}[];
		longtitude: number;
		latitude: number;
		fileUrl: string;
		shipName: string;
		shipImo: string;
	}[];
}
