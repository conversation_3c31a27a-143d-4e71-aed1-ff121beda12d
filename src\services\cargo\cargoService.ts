import { EntityDto } from "../dto/entityDto";
import { PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoints";
import http from "../httpService";
import { CreateOrUpdateCargoInput } from "./dto/CreateOrUpdateCargoInput";
import { GetGroupByCounterparty } from "./dto/GetGroupByCounterparty";
import { PagedFilterAndSortedRequestCargo } from "./dto/PagedFilterAndSortedRequestCargo";
import { UpdateCargoInput } from "./dto/UpdateCargoInput";

class CargoService {
	public async create(createOrUpdateCargoInput: CreateOrUpdateCargoInput) {
		const result = await http.post(
			Endpoint.Cargo.Create,
			createOrUpdateCargoInput,
		);
		return result.data.result;
	}

	public async update(updateCargoInput: UpdateCargoInput) {
		const result = await http.put(Endpoint.Cargo.Update, updateCargoInput);
		return result.data.result;
	}

	public async delete(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.Cargo.Delete, {
			params: entityDto,
		});
		return result.data;
	}

	public async get(entityDto: EntityDto): Promise<CreateOrUpdateCargoInput> {
		const result = await http.get(Endpoint.Cargo.Get, { params: entityDto });
		return result.data.result;
	}

	public async getAll(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequestCargo,
	): Promise<PagedResultDto<CreateOrUpdateCargoInput>> {
		const result = await http.get(Endpoint.Cargo.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}

	public async getFilters(
		filters: { keyword: string; searchColumn: string },
		property: string,
	): Promise<string[]> {
		const result = await http.get(Endpoint.Cargo.Filters, {
			params: { ...filters, propertyName: property },
		});
		return result.data.result;
	}

	public async getGroupByCounterparty(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequestCargo,
	): Promise<PagedResultDto<GetGroupByCounterparty>> {
		const result = await http.get(Endpoint.Cargo.GetGroupByCounterparty, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}
}

export default new CargoService();
