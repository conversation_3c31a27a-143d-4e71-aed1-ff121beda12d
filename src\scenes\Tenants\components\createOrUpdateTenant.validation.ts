import { ValidationRule } from "@ant-design/compatible/lib/form";
import passwordsRules, {
	IPasswordsRules,
} from "../../../components/PasswordComponentBase/passwords.validation";
import { L } from "../../../lib/abpUtility";

interface rules extends IPasswordsRules {
	tenancyName: ValidationRule[];
	name: ValidationRule[];
	adminEmailAddress: ValidationRule[];
}

const rules: rules = {
	tenancyName: [{ required: true, message: L("ThisFieldIsRequired") }],
	name: [{ required: true, message: L("ThisFieldIsRequired") }],
	adminEmailAddress: [
		{ type: "email", required: true, message: L("ThisFieldIsRequired") },
	],
	password: passwordsRules.password,
	confirmPassword: passwordsRules.confirmPassword,
};

export default rules;
