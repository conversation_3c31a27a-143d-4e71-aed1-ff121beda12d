import { Table } from "antd";
import { ColumnProps } from "antd/lib/table";
import * as React from "react";
import AppComponentBase from "../AppComponentBase";
import { L } from "../../lib/abpUtility";
import { Key, TableRowSelection } from "antd/lib/table/interface";

interface EnumFlagsTableProps {
	value?: number;
	onChange?: (value: number | undefined) => void;
	enumDataSource: { [key: number]: string };
	enumDataSourceNamePrefix?: string;
	enumDataSourceNameSufix?: string;
}

interface EnumFlagsTableItem {
	key: string;
	name: string;
	enumValue: number;
}

class EnumFlagsTable extends AppComponentBase<EnumFlagsTableProps> {
	prepareSelected = (): string[] => {
		const value: number = this.props.value ?? 0;
		const selected: string[] = [];
		if (!this.props.enumDataSource) return selected;
		Object.keys(this.props.enumDataSource).forEach((key, index) => {
			if ((+key & value) === +key) {
				selected.push(this.props.enumDataSource[key as unknown as number]);
			}
		});

		return selected;
	};

	handleSelectionChange = (
		selectedRowKeys: Key[],
		selectedRows: EnumFlagsTableItem[],
	) => {
		const newValue: number = this.mapSelectedToValue(selectedRows);
		if (this.props.onChange) {
			this.props.onChange(newValue);
		}
	};

	mapSelectedToValue = (selectedRows: EnumFlagsTableItem[]): number => {
		if (selectedRows.length === 0) return 0;
		return selectedRows
			.map((x) => x.enumValue)
			.reduce((prev, curr) => prev + curr);
	};

	prepareData = (): EnumFlagsTableItem[] => {
		const rules = this.props.enumDataSource;
		if (!rules) return [];

		return Object.keys(rules).map((ruleNumber: string, i) => {
			return {
				key: rules[ruleNumber as unknown as number],
				name: L(
					(this.props.enumDataSourceNamePrefix ?? "") +
						rules[ruleNumber as unknown as number] +
						(this.props.enumDataSourceNameSufix ?? ""),
				),
				enumValue: +ruleNumber,
			} as EnumFlagsTableItem;
		});
	};

	render() {
		const columns: ColumnProps<EnumFlagsTableItem>[] = [
			{
				// title: L("ValidationRules"),
				dataIndex: "name",
				key: "key",
			},
		];

		const selected: string[] = this.prepareSelected();

		const rowSelection: TableRowSelection<EnumFlagsTableItem> = {
			onChange: this.handleSelectionChange,
			selectedRowKeys: selected,
		};

		return (
			<>
				<Table
					rowKey={(record) => record.key}
					bordered={true}
					columns={columns}
					dataSource={this.prepareData()}
					rowSelection={rowSelection}
					pagination={false}
					showHeader={false}
				/>
			</>
		);
	}
}

export default EnumFlagsTable;