import { action, observable } from "mobx";

import TenantModel from "../models/Tenants/TenantModel";
import { EntityDto } from "../services/dto/entityDto";
import type { PagedResultDto } from "../services/dto/pagedResultDto";
import type { PagedTenantResultRequestDto } from "../services/tenant/dto/PagedTenantResultRequestDto";
import CreateTenantInput from "../services/tenant/dto/createTenantInput";
import type CreateTenantOutput from "../services/tenant/dto/createTenantOutput";
import { GetAllTenantOutput } from "../services/tenant/dto/getAllTenantOutput";
import type UpdateTenantInput from "../services/tenant/dto/updateTenantInput";
import tenantService from "../services/tenant/tenantService";

class TenantStore {
	@observable tenants!: PagedResultDto<GetAllTenantOutput>;
	@observable tenantModel: TenantModel = new TenantModel();
	@observable createdTenant!: CreateTenantOutput;

	@action
	async create(createTenantInput: CreateTenantInput) {
		this.createdTenant = await tenantService.create(createTenantInput);
	}

	@action
	async createTenant() {
		this.tenantModel = {
			id: 0,
			isActive: true,
			name: "",
			tenancyName: "",
		};
	}

	@action
	async update(updateTenantInput: UpdateTenantInput) {
		const result = await tenantService.update(updateTenantInput);
		this.tenants.items = this.tenants.items.map((x: GetAllTenantOutput) => {
			if (x.id === updateTenantInput.id) {
				const updateData = updateTenantInput as GetAllTenantOutput;
				x = result as GetAllTenantOutput;
				x.customerName = updateData.customerName;
				x.isSynchToGs = updateData.isSynchToGs;
			}
			return x;
		});
	}

	@action
	async delete(entityDto: EntityDto) {
		await tenantService.delete(entityDto);
		this.tenants.items = this.tenants.items.filter(
			(x: GetAllTenantOutput) => x.id !== entityDto.id,
		);
	}

	@action
	async get(entityDto: EntityDto) {
		const result = await tenantService.get(entityDto);
		this.tenantModel = result;
	}

	@action
	async getAll(pagedFilterAndSortedRequest: PagedTenantResultRequestDto) {
		const result = await tenantService.getAll(pagedFilterAndSortedRequest);
		this.tenants = result;
	}
}

export default TenantStore;
