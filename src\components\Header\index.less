.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.trigger:hover {
  color: #1890ff;
}

.logo {
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px;
}

.logout {
  display: flex;
  justify-content: center;
  align-items: center;
}

.titlePage {
  font-size: 26px;
  font-weight: bold;
  color: #3F3F3F;
}

/* Let's get this party started */
::-webkit-scrollbar {
  width: 12px;
}

/* Track */
::-webkit-scrollbar-track {
  -webkit-border: 1px solid #E1E1E1;
  border: 1px solid #E1E1E1;
  -webkit-border-radius: 10px;
  border-radius: 10px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  -webkit-border-radius: 10px;
  border-radius: 10px;
  background: #D8E6FF;
  -webkit-border: 1px solid #E1E1E1;
  border: 1px solid #E1E1E1;
}

::-webkit-scrollbar-thumb:window-inactive {
  background: #D8E6FF;
}