import { Button, <PERSON> } from "antd";
import { inject, observer } from "mobx-react";
import moment from "moment";
import React from "react";
import { L } from "../../../lib/abpUtility";
import { OcrDto } from "../../../services/ocr/dTo/ocrDto";
import OcrStore from "../../../stores/ocrStore";
import Stores from "../../../stores/storeIdentifier";
import { renderDate } from "../../renderUtils";
import { TableRowSelection } from "antd/lib/table/interface";

interface IDeniedOcrTableProps {
	ocrStore: OcrStore;
	ocr: any;
}

@inject(Stores.OcrStore)
@observer
class DeniedTable extends React.Component<IDeniedOcrTableProps> {
	ocrStore: OcrStore = this.props.ocrStore;

	state = {
		isModalVisible: false,
		selectedFile: null,
		selectedRow: null,
		maxResultCount: 10,
		skipCount: 0,
		ocrId: 0,
		filter: "",
	};
	constructor(props: any) {
		super(props);
		this.state = {
			isModalVisible: false,
			selectedFile: null,
			selectedRow: null,
			maxResultCount: 10,
			skipCount: 0,
			ocrId: 0,
			filter: "",
		};
	}

	public render() {
		const columns = [
			{
				title: L("CreationTime"),
				dataIndex: "creationTime",
				key: "creationTime",
				width: 150,
				sorter: (a: OcrDto, b: OcrDto) =>
					moment(a.creationTime).diff(moment(b.creationTime)),
				render: (text: string, rekord: OcrDto, index: number) =>
					renderDate(text),
			},
			{
				title: L("LastModificationTime"),
				dataIndex: "lastModificationTime",
				key: "lastModificationTime",
				width: 150,
				sorter: (a: OcrDto, b: OcrDto) =>
					moment(a.lastModificationTime).diff(moment(b.lastModificationTime)),
				render: (text: string, rekord: OcrDto, index: number) =>
					renderDate(text),
			},
			{
				title: L("FileName"),
				dataIndex: "fileName",
				key: "fileName",
				width: 150,
			},
			{
				title: L("Status"),
				dataIndex: "status",
				key: "status",
				width: 150,
			},
		];

		const { selectedRow } = this.state;

		const rowSelection: TableRowSelection<OcrDto> = {
			onChange: (selectedRowKeys: any, selectedRows: OcrDto[]) => {
				this.setState({
					selectedRow:
						selectedRows.length > 0
							? selectedRows[selectedRows.length - 1]
							: null,
				});
			},
		};

		const hasSelected = selectedRow;

		return (
			<>
				<div className="ocr-header__buttons">
					<Button type="primary" disabled={!hasSelected}>
						{L("ReturnFile")}
					</Button>
				</div>
				<Table
					bordered={true}
					rowKey={(record: OcrDto) => record.id.toString()}
					columns={columns}
					dataSource={this.props.ocr ? this.props.ocr : []}
					rowSelection={rowSelection}
				/>
			</>
		);
	}
}

export default DeniedTable;
