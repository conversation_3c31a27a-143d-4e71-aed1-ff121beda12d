{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": true, "defaultBranch": "master"}, "files": {"ignoreUnknown": false, "ignore": []}, "formatter": {"enabled": true, "indentStyle": "tab"}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "a11y": {"all": false}, "style": {"useImportType": "off"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}}