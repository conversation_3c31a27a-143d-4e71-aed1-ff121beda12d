import { PagedFilterAndSortedRequest } from "../dto/pagedFilterAndSortedRequest";
import { PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoints";
import http from "../httpService";
import { GetIntegratorOutput } from "./dto/getIntegratorOutput";

class IntegratorService {
	public async getAll(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequest,
	): Promise<PagedResultDto<GetIntegratorOutput>> {
		const result = await http.get(Endpoint.Integrator.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}
}

export default new IntegratorService();
