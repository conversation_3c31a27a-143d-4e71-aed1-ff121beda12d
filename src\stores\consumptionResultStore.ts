import { Modal } from "antd";
import { action, observable } from "mobx";
import consumptionResultService from "../services/consumptionResult/consumptionResultService";
import type { ConsumptionResultDto } from "../services/consumptionResult/dto/consumptionResultDto";
import { EntityDto } from "../services/dto/entityDto";
import type { PagedFilterAndSortedRequest } from "../services/dto/pagedFilterAndSortedRequest";
import type {
	ListResultDto,
	PagedResultDto,
} from "../services/dto/pagedResultDto";

class ConsumptionResultStore {
	@observable consumptionResults!: PagedResultDto<ConsumptionResultDto>;
	@observable editConsumptionResult!: ConsumptionResultDto;
	@observable filters: string[] = [];

	@action
	async create(createConsumptionResultInput: ConsumptionResultDto) {
		const result = await consumptionResultService.create(
			createConsumptionResultInput,
		);
		if (result.message !== "" && result.message !== undefined) {
			Modal.error({ title: "Error", content: result.message });
		} else {
			this.consumptionResults.items.push(result);
		}
	}

	@action
	async update(updateConsumptionResultInput: ConsumptionResultDto) {
		const result = await consumptionResultService.update(
			updateConsumptionResultInput,
		);
		this.consumptionResults.items = this.consumptionResults.items.map(
			(x: ConsumptionResultDto) => {
				if (x.id === updateConsumptionResultInput.id) x = result;
				return x;
			},
		);
	}

	@action
	async getFilters(
		filters: { keyword: string; searchColumn: string },
		property: string,
	) {
		const result = await consumptionResultService.getFilters(filters, property);

		this.filters = result;
	}

	@action
	async delete(entityDto: EntityDto) {
		await consumptionResultService.delete(entityDto);
		this.consumptionResults.items = this.consumptionResults.items.filter(
			(x: ConsumptionResultDto) => x.id !== entityDto.id,
		);
	}

	@action
	async get(entityDto: EntityDto) {
		const result = await consumptionResultService.get(entityDto);
		this.editConsumptionResult = result;
	}

	@action
	async getAll(pagedFilterAndSortedRequest: PagedFilterAndSortedRequest) {
		const result = await consumptionResultService.getAll(
			pagedFilterAndSortedRequest,
		);
		this.consumptionResults = result;
	}

	// @action
	// async createConsumptionResult() {
	//   this.editConsumptionResult = {
	//     id: 0,
	//     consumptionConsumerUuid: '',
	//     consumptionConsumerInformation: '',
	//     firstReadingDate: moment().format(timeFormats.full),
	//     lastReadingDate: moment().format(timeFormats.full),
	//     timezone: getTimezone(),
	//     period: '',
	//     consumptionTotal: '',
	//     consumptionPerHour: '',
	//     consumption24h: '',
	//     uuid: '',
	//     density: '',
	//     idCorrect: false,
	//     shipId: 0,
	//     voyageId: 0,
	//     shipName: '',
	//     callSign: '',
	//     voyageNumber: '',
	//     isMadeManually: false,
	//     consumer: '',
	//     gsStatus: GsStatus.NotDefined,
	//   };
	// }

	@action
	async resendToGs(entities: ListResultDto<EntityDto>) {
		const result: Array<ConsumptionResultDto> =
			await consumptionResultService.resendToGs(entities);

		this.consumptionResults.items = this.consumptionResults.items.map(
			(item) => {
				if (result.map((x) => x.id).includes(item.id)) {
					item.gsStatus =
						result.find((x) => x.id === item.id)?.gsStatus ?? item.gsStatus;
				}
				return item;
			},
		);
	}
}

export default ConsumptionResultStore;
