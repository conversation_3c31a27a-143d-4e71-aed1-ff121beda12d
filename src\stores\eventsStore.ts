import { action, observable } from "mobx";
import { EntityDto } from "../services/dto/entityDto";
import type { PagedResultDto } from "../services/dto/pagedResultDto";
import type { CreateOrUpdateEventsInput } from "../services/events/dto/CreateOrUpdateEventsInput";
import { GetCargosOutput } from "../services/events/dto/GetCargosOutput";
import { GetEventsOutput } from "../services/events/dto/GetEventsOutput";
import type { PagedFilterAndSortedRequestEvents } from "../services/events/dto/PagedFilterAndSortedRequestEvents";
import type { RequestMeterReadingsInput } from "../services/events/dto/RequestMeterReadingsInput";
import type { UpdateEventsInput } from "../services/events/dto/UpdateEventsInput";
import eventsService from "../services/events/eventsService";

class EventsStore {
	@observable events!: PagedResultDto<GetEventsOutput>;
	@observable editEvents!: CreateOrUpdateEventsInput;
	@observable meterReadings!: PagedResultDto<RequestMeterReadingsInput>;
	@observable meterTypes!: { [key: string]: string[] };
	@observable getCargos!: GetCargosOutput[];

	@action
	async create(createOrUpdateEventsInput: CreateOrUpdateEventsInput) {
		const result = await eventsService.create(createOrUpdateEventsInput);
		this.events.items.push(result);
	}

	@action
	async update(updateEvents: UpdateEventsInput) {
		const result = await eventsService.update(updateEvents);
		this.events.items = this.events.items.map((x: GetEventsOutput) => {
			if (x.id === updateEvents.id) x = result;
			return x;
		});
	}

	@action
	async getCargo(shipId: number) {
		const result = await eventsService.getCargo(shipId);
		this.getCargos = result;
	}

	@action
	async delete(entityDto: EntityDto) {
		await eventsService.delete(entityDto);
		this.events.items = this.events.items.filter(
			(x: GetEventsOutput) => x.id !== entityDto.id,
		);
	}

	@action
	async requestMeterReadings(
		requestMeterReadingsInput: RequestMeterReadingsInput,
	) {
		await eventsService.requestMeterReadings(requestMeterReadingsInput);
	}

	@action
	async get(entityDto: EntityDto) {
		const result = await eventsService.get(entityDto);
		this.editEvents = result;
	}

	@action
	async getAll(pagedFilterAndSortedRequest: PagedFilterAndSortedRequestEvents) {
		const result = await eventsService.getAll(pagedFilterAndSortedRequest);
		this.events = result;
	}
}

export default EventsStore;
