import { Descriptions } from "antd";
import moment from "moment";
import React from "react";
import { ShipPositionDto } from "../../../services/map/dto/ShipPositionDto";
import { DescriptionsItemProps } from "antd/lib/descriptions/Item";

export interface MapShipPopupProps {
	shipPosition: ShipPositionDto | undefined;
}

const MapShipPopup: React.FC<MapShipPopupProps> = ({ shipPosition }) => {
	if (!shipPosition) return <></>;

	const items: DescriptionsItemProps[] = [
		{
			label: "Ship name",
			children: shipPosition.shipName,
		},
		{
			label: "Date",
			children: moment.utc(moment(shipPosition.timestamp).utc()).format(),
		},
		{
			label: "Status",
			children: shipPosition.status,
		},
		{
			label: "Latitude",
			children: shipPosition.latitude,
		},
		{
			label: "Longitude",
			children: shipPosition.longitude,
		},
		{
			label: "SOG",
			children: shipPosition.sog.toFixed(2),
		},
		{
			label: "Heading",
			children: shipPosition.heading,
		},
	];

	return (
		<Descriptions
			size={"small"}
			column={1}
			style={{ width: "250px", padding: "5px" }}
			// extra={<Button type="primary">Edit</Button>}
		>
			{items.map((item) => (
				<Descriptions.Item label={item.label} key={Math.random().toString()}>
					{item.children}
				</Descriptions.Item>
			))}
		</Descriptions>
	);
};

export default MapShipPopup;
