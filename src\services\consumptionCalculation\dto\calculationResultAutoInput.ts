import { Moment } from "moment";
import { ConsumptionResultDto } from "../../consumptionResult/dto/consumptionResultDto";

export interface ConsumptionCalculationDtos {
	items: Array<ConsumptionCalculationDto>;
	voyageId: number;
}

export interface ConsumptionCalculationDto {
	calculationResultInput: CalculationResultInput;
	calculationResult: CalculationResult;
	consumptionResult: ConsumptionResultDto;
}

export interface ConsumptionConfigurationDto {
	groupingPeriodInMinutes: number;
}

export interface CalculationResultAutoInput {
	voyageId: number;
	from: Moment;
	to: Moment;
}

export class CalculationResultInput {
	currentPoint!: RowTableData;
	previousPoint!: RowTableData;
	density!: string;
}

export interface RowTableData {
	averages: ReadingsAverage[];
	uuid: string;
	density: string;
}

export interface ReadingsAverage extends ReadingsTableSelect {
	amount: number;
}

export interface ReadingsTableSelect {
	orginalValue: string;
	currentValue: string;
	time: string; // c# DateTime
	id: string;
	meterReadingUuid: string;
	meterUuid: string;
	orginalUnit: number;
	currentUnit: number;
	unitShoxText: string;
	density: string;
}

export interface CalculationResult extends BaseCalculationResult {
	// public ConsumptionConsumer consumptionConsumer { get; set; }
	consumptionConsumerId: number;
	consumptionConsumerName: string;
	dateFrom: number;
	dateTo: number;
	uuid: string;
}

interface BaseCalculationResult {
	result: string;
	isCorrect: boolean;
}
