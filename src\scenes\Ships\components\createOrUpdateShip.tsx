import { Form } from "@ant-design/compatible";
import "@ant-design/compatible/assets/index.css";
import { FormComponentProps } from "@ant-design/compatible/lib/form";
import { Checkbox, Input, InputNumber, Modal, Select, Tabs } from "antd";
import FormItem from "antd/lib/form/FormItem";
import * as React from "react";
import ComponentLayout from "../../../components/Layout/ComponentLayout";
import { L } from "../../../lib/abpUtility";
import ShipStore from "../../../stores/shipStore";
import { FormWidths } from "../../ViewSettingsConsts";
import {
	cargoUnitsCustomData,
	scrubberCustomData,
	vesselTypesCustomData,
} from "../../enumUtils";
import { renderHoursOptionsEvery15Min } from "../../renderUtils";
import "../../scenes.less";
import rules from "./createOrUpdateShip.validation";

export interface ICreateOrUpdateShipProps extends FormComponentProps {
	visible: boolean;
	loading: boolean;
	onCancel: () => void;
	modalType: string;
	onCreate: () => void;
	shipStore: ShipStore;
}
class CreateOrUpdateShip extends React.Component<ICreateOrUpdateShipProps> {
	componentDidUpdate(prevProps: Readonly<ICreateOrUpdateShipProps>): void {
		if (this.props.form.getFieldValue("cargoUnit")) {
			return;
		}

		const cargoUnit: Map<string, string> = this.props.form.getFieldValue("type")
			? cargoUnitsCustomData[this.props.form.getFieldValue("type")]
			: new Map([]);

		if (cargoUnit.size === 1) {
			this.props.form.setFieldsValue({
				cargoUnit: cargoUnit.values().next().value,
			});
		}
	}

	render() {
		const { getFieldDecorator } = this.props.form;
		const { visible, onCancel, onCreate } = this.props;

		const initialValues = this.props.shipStore.editShip;

		const cargoUnit: Map<string, string> = this.props.form.getFieldValue("type")
			? cargoUnitsCustomData[this.props.form.getFieldValue("type")]
			: new Map([]);

		return (
			<Modal
				visible={visible}
				cancelText={L("Cancel")}
				okText={L("OK")}
				onCancel={onCancel}
				onOk={onCreate}
				title={"Vessel"}
				okButtonProps={{ loading: this.props.loading }}
			>
				<Form layout="vertical">
					<Tabs defaultActiveKey={"Ship"} size={"small"} tabBarGutter={64}>
						<Tabs.TabPane tab={L("General")} key="general">
							<Form.Item
								label={L("Ship Name")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("shipName", { rules: rules.shipName })(
									<Input style={{ width: FormWidths.wide }} />,
								)}
							</Form.Item>
							<Form.Item
								label={L("Ship Email")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("shipEmail", { rules: rules.shipEmail })(
									<Input style={{ width: FormWidths.wide }} />,
								)}
							</Form.Item>
							<Form.Item label={L("IMO")} {...ComponentLayout.formItemLayout}>
								{getFieldDecorator("imoNumber", { rules: rules.imoNumber })(
									<Input style={{ width: FormWidths.wide }} />,
								)}
							</Form.Item>
							<Form.Item label={L("MMSI")} {...ComponentLayout.formItemLayout}>
								{getFieldDecorator("mmsi", { rules: rules.mmsi })(
									<Input style={{ width: FormWidths.wide }} />,
								)}
							</Form.Item>
							<Form.Item
								label={L("Vessel Type")}
								{...ComponentLayout.formItemLayout}
								required
							>
								{getFieldDecorator("type", {
									rules: rules.type,
								})(
									<Select
										{...ComponentLayout.formItemLayout}
										style={{ width: FormWidths.wide }}
										placeholder={L("Select Vessel Type")}
										onChange={(value: string) => {
											this.props.form.setFieldsValue({
												type: value,
												cargoUnit: "",
											});
										}}
									>
										{[...vesselTypesCustomData].map((x) => {
											return <Select.Option key={x[0]}>{x[1]}</Select.Option>;
										})}
									</Select>,
								)}
							</Form.Item>
							<Form.Item
								label={L("Cargo Unit")}
								{...ComponentLayout.formItemLayout}
								required
							>
								{getFieldDecorator("cargoUnit", {
									rules: rules.type,
								})(
									<Select
										{...ComponentLayout.formItemLayout}
										style={{ width: FormWidths.wide }}
										placeholder={L("Select Cargo Unit")}
										disabled={!this.props.form.getFieldValue("type")}
										onChange={(value: string) => {
											this.props.form.setFieldsValue({
												cargoUnit: value,
											});
										}}
									>
										{[...cargoUnit].map((x) => {
											return <Select.Option key={x[0]}>{x[1]}</Select.Option>;
										})}
									</Select>,
								)}
							</Form.Item>
							<Form.Item
								label={L("Vessel SDWT")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("sdwt", { rules: rules.sDWT })(
									<InputNumber style={{ width: FormWidths.wide }} />,
								)}
							</Form.Item>
							<Form.Item
								label={L("Vessel GT")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("gt", { rules: rules.gT })(
									<InputNumber style={{ width: FormWidths.wide }} />,
								)}
							</Form.Item>
							<Form.Item
								label={L("Captain Name")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("captainName", { rules: rules.captainName })(
									<Input style={{ width: FormWidths.wide }} />,
								)}
							</Form.Item>
							<Form.Item
								label={L("Chief Engineers Name")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("chiefEngineersName", {
									rules: rules.chiefEngineersName,
								})(<Input style={{ width: FormWidths.wide }} />)}
							</Form.Item>
							<Form.Item
								label={L("Engine Room \n Unmanned Hours From")}
								{...ComponentLayout.formItemLayout}
								labelCol={{
									...ComponentLayout.formItemLayout.labelCol,
									style: { whiteSpace: "pre-wrap" },
								}}
							>
								{getFieldDecorator("engineRoomUnmannedHoursFrom", {
									rules: rules.engineRoomUnmannedHoursFrom,
								})(
									<Select
										{...ComponentLayout.formItemLayout}
										style={{ width: FormWidths.wide }}
										onChange={(values: Array<string>) => {
											this.props.form.setFieldsValue({
												engineRoomUnmannedHoursFrom: values,
											});
										}}
									>
										{renderHoursOptionsEvery15Min()}
									</Select>,
								)}
							</Form.Item>
							<Form.Item
								label={L("Engine Room \n Unmanned Hours To")}
								{...ComponentLayout.formItemLayout}
								labelCol={{
									...ComponentLayout.formItemLayout.labelCol,
									style: { whiteSpace: "pre-wrap" },
								}}
							>
								{getFieldDecorator("engineRoomUnmannedHoursTo", {
									rules: rules.engineRoomUnmannedHoursTo,
								})(
									<Select
										{...ComponentLayout.formItemLayout}
										style={{ width: FormWidths.wide }}
										onChange={(values: Array<string>) => {
											this.props.form.setFieldsValue({
												engineRoomUnmannedHoursTo: values,
											});
										}}
									>
										{renderHoursOptionsEvery15Min()}
									</Select>,
								)}
							</Form.Item>
							<Form.Item
								label={L("Scrubber")}
								{...ComponentLayout.formItemLayout}
								labelAlign={"left"}
								required
							>
								{getFieldDecorator("scrubber", {
									rules: rules.scrubber,
								})(
									<Select
										{...ComponentLayout.formItemLayout}
										style={{ width: FormWidths.wide }}
										onChange={(values: Array<string>) => {
											this.props.form.setFieldsValue({
												scrubber: values,
											});
										}}
									>
										{[...scrubberCustomData].map((x) => {
											return <Select.Option key={x[0]}>{x[1]}</Select.Option>;
										})}
									</Select>,
								)}
							</Form.Item>
						</Tabs.TabPane>
						<Tabs.TabPane tab={L("Notifications")} key="events">
							<h3 style={{ marginBlock: ".5rem" }}>EMISSIONS NOTIFICATIONS</h3>
							<FormItem
								label={L("")}
								{...ComponentLayout.formItemLayout}
								style={{ marginBottom: ".5rem" }}
							>
								{getFieldDecorator("settings.arrivalAndAnchorage", {
									initialValue: initialValues?.settings?.arrivalAndAnchorage,
									valuePropName: "checked",
								})(
									<Checkbox style={{ width: FormWidths.wide }}>
										{L("Arrival or Departure @ Berth or Anchorage")}
									</Checkbox>,
								)}
							</FormItem>
							<FormItem
								label={L("")}
								{...ComponentLayout.formItemLayout}
								style={{ marginBottom: ".5rem" }}
							>
								{getFieldDecorator("settings.enteringECA", {
									initialValue: initialValues?.settings?.enteringECA,
									valuePropName: "checked",
								})(
									<Checkbox style={{ width: FormWidths.wide }}>
										{L("Entering or Leaving ECA")}
									</Checkbox>,
								)}
							</FormItem>
							<h3 style={{ marginBlock: ".5rem" }}>DAILY REPORTING</h3>
							<FormItem
								label={L("")}
								{...ComponentLayout.formItemLayout}
								style={{ marginBottom: ".5rem" }}
							>
								{getFieldDecorator("settings.noonReportAt6", {
									initialValue: initialValues?.settings?.noonReportAt6,
									valuePropName: "checked",
								})(
									<Checkbox style={{ width: FormWidths.wide }}>
										{L("06:00 (LT) Daily Report")}
									</Checkbox>,
								)}
							</FormItem>
							<FormItem
								label={L("")}
								{...ComponentLayout.formItemLayout}
								style={{ marginBottom: ".5rem" }}
							>
								{getFieldDecorator("settings.noonReportAt12", {
									initialValue: initialValues?.settings?.noonReportAt12,
									valuePropName: "checked",
								})(
									<Checkbox style={{ width: FormWidths.wide }}>
										{L("12:00  (LT) Daily Report")}
									</Checkbox>,
								)}
							</FormItem>
							<FormItem
								label={L("")}
								{...ComponentLayout.formItemLayout}
								style={{ marginBottom: ".5rem" }}
							>
								{getFieldDecorator("settings.noonReportAt18", {
									initialValue: initialValues?.settings?.noonReportAt18,
									valuePropName: "checked",
								})(
									<Checkbox style={{ width: FormWidths.wide }}>
										{L("18:00  (LT) Daily Report")}
									</Checkbox>,
								)}
							</FormItem>
							<p style={{ marginTop: "1rem", fontWeight: 500 }}>
								{L(
									"Ad Hoc notifications created manually from vessel or from the shore are controlled by User",
								)}
							</p>
						</Tabs.TabPane>
					</Tabs>
				</Form>
			</Modal>
		);
	}
}

export default Form.create<ICreateOrUpdateShipProps>()(CreateOrUpdateShip);
