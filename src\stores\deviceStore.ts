import { Modal } from "antd";
import { action, observable } from "mobx";
import deviceService from "../services/device/deviceService";
import type { PagedDeviceResultRequestDto } from "../services/device/dto/PagedDeviceResultRequestDto";
import type { CreateOrUpdateDeviceInput } from "../services/device/dto/createOrUpdateDeviceInput";
import { GetDeviceOutput } from "../services/device/dto/getDeviceOutput";
import type { RegisterDeviceInput } from "../services/device/dto/registerDeviceInput";
import type Dictionary from "../services/dictionary";
import { EntityDto } from "../services/dto/entityDto";
import type { PagedResultDto } from "../services/dto/pagedResultDto";

class DeviceStore {
	@observable devices!: PagedResultDto<GetDeviceOutput>;
	@observable editDevice!: CreateOrUpdateDeviceInput;
	@observable deviceStatuses!: Dictionary<number, string>;
	@observable registerDevices!: RegisterDeviceInput;
	@observable status!: { [key: number]: string };

	@action
	async create(createDeviceInput: CreateOrUpdateDeviceInput) {
		const result = await deviceService.create(createDeviceInput);
		if (result.message !== "" && result.message !== undefined) {
			Modal.error({ title: "Error", content: result.message });
		} else {
			this.devices.items.push(result);
		}
	}

	@action
	async update(updateDeviceInput: CreateOrUpdateDeviceInput) {
		const result = await deviceService.update(updateDeviceInput);
		this.devices.items = this.devices.items.map((x: GetDeviceOutput) => {
			if (x.id === updateDeviceInput.id) x = result;
			return x;
		});
	}

	@action
	async registerDevice(registerDeviceInput: RegisterDeviceInput) {
		const result = await deviceService.registerDevice(registerDeviceInput);
		this.registerDevices = result;
	}

	@action
	async delete(entityDto: EntityDto) {
		await deviceService.delete(entityDto);
		this.devices.items = this.devices.items.filter(
			(x: GetDeviceOutput) => x.id !== entityDto.id,
		);
	}

	@action
	async get(entityDto: EntityDto) {
		const result = await deviceService.get(entityDto);
		this.editDevice = result;
	}

	@action
	async getAll(pagedFilterAndSortedRequest: PagedDeviceResultRequestDto) {
		const result = await deviceService.getAll(pagedFilterAndSortedRequest);
		this.devices = result;
	}

	@action
	async getDeviceStatuses() {
		const result = await deviceService.getDeviceStatuses();
		this.deviceStatuses = result;
	}
	@action
	async createDevice() {
		this.editDevice = {
			id: 0,
			rank: "",
			shipName: "",
			shipImo: "",
			activationCode: 0,
			codeUsed: false,
			codeCreatedAt: new Date(),
			buildNumber: 0,
			status: "",
			ship: {
				shipName: "",
			},
			user: {
				rank: "",
				emailAddress: "",
				creationTime: new Date(),
				id: 0,
				name: "",
				isActive: false,
				surname: "",
			},
		};
	}
}

export default DeviceStore;
