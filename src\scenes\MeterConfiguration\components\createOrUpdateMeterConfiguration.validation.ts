import { L } from "../../../lib/abpUtility";

const rules = {
	name: [{ required: true, message: L("Please input meter name") }],
	fuelType: [{ required: true, message: L("Please select Fuel Type") }],
	meterType: [{ required: true, message: L("Please select Meter Type") }],
	unitOfMeasure: [{ required: true, message: L("Please select Measure Unit") }],
	measuringCommodity: [
		{ required: true, message: L("Please input Measuring Commodity") },
	],
	measureType: [{ required: true, message: L("Please select Measure Type") }],
	numberOfDigits: [
		{ required: true, message: L("Please input number of digits") },
	],
	decimalPointPosition: [
		{ required: true, message: L("Please input decimal point") },
	],
	flowDirection: [
		{ required: true, message: L("Please select Flow Direction") },
	],
	shipId: [{ required: true, message: L("Please select Ship") }],
	meterId: [{ required: true, message: L("Please select Meter") }],
};

export default rules;
