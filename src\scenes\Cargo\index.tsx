import { WrappedFormUtils } from "@ant-design/compatible/lib/form/Form";
import { PlusOutlined, SettingOutlined } from "@ant-design/icons";
import {
	<PERSON><PERSON>,
	Card,
	Col,
	DatePicker,
	Dropdown,
	Menu,
	Modal,
	Row,
} from "antd";
import MenuItem from "antd/lib/menu/MenuItem";
import Table, { ColumnProps } from "antd/lib/table";
import {
	FilterDropdownProps,
	SorterResult,
	TablePaginationConfig,
	TableRowSelection,
} from "antd/lib/table/interface";
import { inject, observer } from "mobx-react";
import moment from "moment";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import FilterSelect from "../../components/FilterSelect/FilterSelect";
import { L, isGranted } from "../../lib/abpUtility";
import { FilterByColumn } from "../../models/Sort/SortState";
import { GetCargoOutput } from "../../services/cargo/dto/GetCargoOutput";
import { EntityDto } from "../../services/dto/entityDto";
import { ListResultDto } from "../../services/dto/pagedResultDto";
import { GetUserOutput } from "../../services/user/dto/getUserOutput";
import CargoStore from "../../stores/cargoStore";
import EventsStore from "../../stores/eventsStore";
import ShipStore from "../../stores/shipStore";
import Stores from "../../stores/storeIdentifier";
import UserStore from "../../stores/userStore";
import utils from "../../utils/utils";
import { ModalType } from "../ModalConsts";
import {
	getTablePaginationOptions,
	renderDate,
	renderFilterIcon,
} from "../renderUtils";
import CreateOrUpdateCargo from "./components/createOrUpdateCargo";

export interface ICargoProps {
	userStore: UserStore;
	cargoStore: CargoStore;
	shipStore: ShipStore;
	eventsStore: EventsStore;
}

interface ICargoDateFilters {
	dateOfTransportStart?: string;
	dateOfTransportEnd?: string;
}

export interface ICargoState {
	maxResultCount: number;
	skipCount: number;
	sorters: SorterResult<GetCargoOutput>[];
	selectedRows: ListResultDto<EntityDto>;
	okButtonDisabled: boolean;
	fetchingFilters: boolean;
	modalVisible: boolean;
	id: number;
	listCounterparty: GetUserOutput[];
	tempSearchValue: string;
	loading: boolean;
	filters: Array<FilterByColumn>;
	activeDateFilters: ICargoDateFilters;
}

const confirm = Modal.confirm;

type PreviousState = {
	filters: ICargoState["filters"];
	sorters: ICargoState["sorters"];
	activeDateFilters: ICargoState["activeDateFilters"];
};

@inject(Stores.CargoStore)
@inject(Stores.UserStore)
@inject(Stores.ShipStore)
@inject(Stores.EventsStore)
@observer
class Cargo extends AppComponentBase<ICargoProps, ICargoState> {
	formRef?: WrappedFormUtils;

	state: ICargoState = {
		maxResultCount: 10,
		skipCount: 0,
		sorters: [],
		selectedRows: {
			items: [],
		},
		fetchingFilters: false,
		okButtonDisabled: false,
		modalVisible: false,
		id: 0,
		filters: [],
		listCounterparty: [],
		tempSearchValue: "",
		loading: false,
		activeDateFilters: {
			dateOfTransportStart: undefined,
			dateOfTransportEnd: undefined,
		},
	};

	extractPreviousState(): PreviousState {
		let state: PreviousState = {
			filters: [],
			sorters: [],
			activeDateFilters: {},
		};

		const prevState =
			utils.getSortAndFilterFromStorage<PreviousState>("cargo-filters");

		if (prevState)
			state = {
				...prevState,
			};

		utils.removeStateFromStorage("cargo-filters");

		return state;
	}

	componentWillUnmount(): void {
		const settings: PreviousState = {
			sorters: this.state.sorters,
			activeDateFilters: this.state.activeDateFilters,
			filters: this.state.filters,
		};

		utils.saveSortAndFilterToStorage("cargo-filters", settings);
	}

	async fetchFilters(property: string) {
		this.setState({ fetchingFilters: true });
		const [searchColumnString, keywordString] = utils.getFilterStrings([
			...this.state.filters,
		]);

		try {
			await this.props.cargoStore.getFilters(
				{
					keyword: keywordString,
					searchColumn: searchColumnString,
				},
				property,
			);
		} catch {
			Modal.error({
				title: "Error during fetching",
				content: (
					<div>
						<p>Failed to fetch filters for column</p>
					</div>
				),
			});
		}

		this.setState({ fetchingFilters: false });
	}

	async componentDidMount() {
		const prevState = this.extractPreviousState();

		this.setState({ ...prevState }, async () => {
			await Promise.all([
				this.getAll(),
				this.props.shipStore.getShipNames(),
				this.getListCounterparty(),
			]);
		});
	}

	async getListCounterparty() {
		await this.props.userStore.getCharterers({
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: "",
		});
		const listCounterparty = this.props.userStore.users;
		if (!listCounterparty) return;
		this.setState({ listCounterparty: listCounterparty.items });
	}

	async getAll() {
		this.setState({ loading: true });
		const sortString = utils.getSorterString(this.state.sorters);

		const [searchColumnString, keywordString] = utils.getFilterStrings([
			...this.state.filters,
		]);

		await this.props.cargoStore.getAll({
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: keywordString,
			searchColumn: searchColumnString,
			sorting: sortString,
			dateOfTransportStart:
				this.state.activeDateFilters.dateOfTransportStart || "",
			dateOfTransportEnd: this.state.activeDateFilters.dateOfTransportEnd || "",
		});
		this.setState({ loading: false });
	}

	handleDateFilter = (startDate?: string, endDate?: string) => {
		this.setState(
			(prevState) => ({
				activeDateFilters: {
					...prevState.activeDateFilters,
					dateOfTransportStart: startDate,
					dateOfTransportEnd: endDate,
				},
			}),
			() => {
				this.getAll();
			},
		);
	};

	getColumnSearchProps = (
		dataIndex: string,
		displayName: string,
	): ColumnProps<GetCargoOutput> => ({
		filterDropdown: (props) => {
			return (
				<div>
					{props.visible && (
						<FilterSelect
							{...props}
							loading={this.state.fetchingFilters}
							handleFilter={(value) =>
								this.handleFilter(value, dataIndex, props.confirm)
							}
							title={displayName}
							value={
								this.state.filters.find((x) => x.column === dataIndex)?.value
							}
							options={this.props.cargoStore.filters?.map((x) => {
								return { key: x, value: x };
							})}
						/>
					)}
				</div>
			);
		},
		onFilterDropdownVisibleChange: (v) => {
			if (v) this.fetchFilters(dataIndex);
		},
		sortOrder: this.state.sorters.find((x) => x.columnKey === dataIndex)?.order,
		filterIcon: () =>
			renderFilterIcon(
				this.state.filters.findIndex((x) => x.column === dataIndex) >= 0,
			),
	});

	handleTableChange = (
		pagination: TablePaginationConfig,
		_: Partial<Record<keyof GetCargoOutput, string[]>>,
		sorter: SorterResult<GetCargoOutput> | SorterResult<GetCargoOutput>[],
	) => {
		const sorters = utils.getSorters(sorter);

		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
				sorters: sorters,
			},
			async () => await this.getAll(),
		);
	};

	private handleFilter(
		value: string,
		column: string,
		confirm: FilterDropdownProps["confirm"],
	) {
		let currentFilters = [...this.state.filters];
		currentFilters = currentFilters.filter((f) => f.column !== column);

		if (value) {
			currentFilters.push({ value, column });
		}

		this.setState({ filters: currentFilters }, async () => {
			await this.getAll();
			confirm?.();
		});
	}

	delete(input: EntityDto) {
		const self = this;
		confirm({
			title: "Do you want to delete these items?",
			async onOk() {
				await self.props.cargoStore.delete(input);
				await self.getAll();
			},
			onCancel() {},
		});
	}

	// biome-ignore lint/suspicious/noExplicitAny: Poor type implementation from library
	saveFormRef = (formRef: any) => {
		if (!formRef) return;
		this.formRef = formRef.props.form;
	};

	Modal = () => {
		this.setState({
			modalVisible: !this.state.modalVisible,
		});
	};

	setModalVisibleFalse = () => {
		this.setState({ modalVisible: false, okButtonDisabled: false });
	};

	handleCreate = () => {
		if (this.state.okButtonDisabled === false) {
			const form = this.formRef;
			if (!form) return;
			// biome-ignore lint/suspicious/noExplicitAny: Poor type implementation from library
			form.validateFields(async (err: any, values: any) => {
				this.setState({ okButtonDisabled: true });
				if (err) {
					this.setState({ okButtonDisabled: false });
					return;
				}
				try {
					if (this.state.id === 0) {
						const { id, ...data } = values;
						await this.props.cargoStore.create(data);
					} else {
						await this.props.cargoStore.update({
							id: this.state.id,
							...values,
							counterparty: this.props.cargoStore.editCargos.counterparty,
							ship: this.props.cargoStore.editCargos.ship,
						});
					}
					await this.getAll();
					form.resetFields();
					this.setModalVisibleFalse();
				} catch (ex) {
				} finally {
					this.setState({ okButtonDisabled: false });
				}
			});
		}
	};

	async createOrUpdateModalOpen(entityDto: EntityDto) {
		if (entityDto.id === 0) {
			await this.props.cargoStore.createCargo();
		} else {
			await this.props.cargoStore.get(entityDto);
		}

		this.setState({ id: entityDto.id });
		this.Modal();

		const cargoProps = this.props.cargoStore.editCargos;

		if (cargoProps !== null) {
			if (!this.formRef) return;

			this.formRef.setFieldsValue({
				...cargoProps,
				dateOfTransport: cargoProps.dateOfTransport
					? moment(cargoProps.dateOfTransport)
					: null,
				counterparty: {
					id: cargoProps.counterparty.id,
					fullName: cargoProps.counterparty.fullName,
				},
				ship: { id: cargoProps.ship.id, shipName: cargoProps.ship.shipName },
			});
		}
	}

	public render() {
		const { cargos } = this.props.cargoStore;
		const paginationOptions = getTablePaginationOptions(cargos?.totalCount);
		const columns: Array<ColumnProps<GetCargoOutput>> = [
			{
				title: L("Ship Name"),
				dataIndex: ["ship", "shipName"],
				key: "ship.shipName",
				width: 450,
				sorter: { multiple: 1 },
				...this.getColumnSearchProps("ship.shipName", L("Ship Name")),
			},
			{
				title: L("Start Date/ Time of Cargo"),
				dataIndex: "dateOfTransport",
				key: "dateOfTransport",
				width: 250,
				render: (text: string) => renderDate(text, true),
				sorter: { multiple: 2 },
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "dateOfTransport",
				)?.order,
				filterDropdown: ({ confirm, clearFilters }) => (
					<div style={{ padding: 8 }}>
						<DatePicker.RangePicker
							showTime={{ format: "HH:mm" }}
							style={{ marginBottom: 8, width: 200 }}
							onChange={(dates) => {
								if (dates && dates.length === 2 && dates[0] && dates[1]) {
									const start = dates[0];
									const end = dates[1];
									this.handleDateFilter(start.toISOString(), end.toISOString());
									confirm?.();
								} else {
									this.handleDateFilter(undefined, undefined);
								}
							}}
						/>
					</div>
				),
				filterIcon: () =>
					renderFilterIcon(
						this.state.activeDateFilters.dateOfTransportEnd !== undefined &&
							this.state.activeDateFilters.dateOfTransportStart !== undefined,
					),
			},
			{
				title: L("Quantity"),
				dataIndex: "quantity",
				key: "quantity",
				width: 250,
				sorter: { multiple: 3 },
				render: (text: number, item: GetCargoOutput) =>
					`${text} ${item.units || ""}`,
				sortOrder: this.state.sorters.find((x) => x.columnKey === "quantity")
					?.order,
			},
			{
				title: L("User"),
				dataIndex: ["counterparty", "userName"],
				key: "counterparty.userName",
				width: 250,
				sorter: { multiple: 4 },
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "counterparty.userName",
				)?.order,
			},
			{
				title: L("Actions"),
				width: "10%",
				render: (text: string, item: GetCargoOutput) => (
					<div>
						<Dropdown
							trigger={["click"]}
							overlay={
								<Menu>
									{isGranted("Pages.Cargo-Edit") && (
										<MenuItem
											onClick={() =>
												this.createOrUpdateModalOpen({ id: item.id })
											}
										>
											{L("Edit")}
										</MenuItem>
									)}
									{isGranted("Pages.Cargo-Delete") && (
										<MenuItem onClick={() => this.delete({ id: item.id })}>
											{L("Delete")}
										</MenuItem>
									)}
								</Menu>
							}
							placement="bottomLeft"
						>
							<Button type="primary" icon={<SettingOutlined />}>
								{L("Actions")}
							</Button>
						</Dropdown>
					</div>
				),
			},
		];

		const hasNoActions =
			!isGranted("Pages.Cargo-Edit") && !isGranted("Pages.Cargo-Delete");

		if (hasNoActions) {
			columns.pop();
		}

		const rowSelection: TableRowSelection<GetCargoOutput> = {
			fixed: true,
			onChange: (_, selectedRows: GetCargoOutput[]) => {
				const selectedRowsItems: EntityDto[] = selectedRows.map((x) => {
					return {
						id: x.id,
					};
				});
				this.setState({
					selectedRows: {
						items: selectedRowsItems,
					},
				});
			},
		};

		return (
			<Card>
				<Row>
					<Col
						xs={{ span: 23, offset: 0 }}
						sm={{ span: 23, offset: 0 }}
						md={{ span: 23, offset: 0 }}
						lg={{ span: 23, offset: 0 }}
						xl={{ span: 23, offset: 0 }}
						xxl={{ span: 23, offset: 0 }}
						style={{ display: "flex", justifyContent: "end" }}
					>
						{isGranted("Pages.Cargo-Add") && (
							<Button
								type="primary"
								shape="circle"
								icon={<PlusOutlined />}
								style={{
									display: "flex",
									justifyContent: "center",
									alignItems: "center",
								}}
								onClick={() => this.createOrUpdateModalOpen({ id: 0 })}
							/>
						)}
					</Col>
				</Row>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey={(record) => record.id.toString()}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={this.state.loading}
							dataSource={cargos === undefined ? [] : cargos.items}
							onChange={this.handleTableChange}
							scroll={{ y: 850 }}
							rowSelection={rowSelection}
						/>
					</Col>
				</Row>
				<CreateOrUpdateCargo
					eventsStore={this.props.eventsStore}
					listCounterparty={this.state.listCounterparty}
					shipStore={this.props.shipStore}
					cargoStore={this.props.cargoStore}
					wrappedComponentRef={this.saveFormRef}
					visible={this.state.modalVisible}
					onCancel={this.setModalVisibleFalse}
					modalType={this.state.id === 0 ? ModalType.create : ModalType.edit}
					onCreate={this.handleCreate}
					okButtonDisabled={this.state.okButtonDisabled}
					roles={this.props.cargoStore.roles}
				/>
				<Chat />
			</Card>
		);
	}
}

export default Cargo;
