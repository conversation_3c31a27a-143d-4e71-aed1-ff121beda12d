export interface CreateOrUpdateShipInput {
	id: number;
	shipName: string;
	imoNumber: string;
	shipEmail: string;
	captainName: string;
	chiefEngineersName: string;
	engineRoomUnmannedHoursFrom: string[];
	engineRoomUnmannedHoursTo: string[];
	scrubber: string;
	mmsi: string;
	type: string;
	cargoUnit: string;
	sdwt: number;
	gt: number;
	settings: {
		arrivalAndAnchorage: boolean;
		readMetersOnFAOP: boolean;
		enteringECA: boolean;
		noonReportAt6: boolean;
		noonReportAt12: boolean;
		noonReportAt18: boolean;
	};
}
