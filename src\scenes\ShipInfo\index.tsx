import * as React from "react";
import "./index.less";
import { SearchOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Card, Col, Input, Row, Table } from "antd";
import { ColumnProps } from "antd/lib/table";
import { inject, observer } from "mobx-react";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import { L } from "../../lib/abpUtility";
import { EntityDto } from "../../services/dto/entityDto";
import { ListResultDto } from "../../services/dto/pagedResultDto";
import http from "../../services/httpService";
import { CreateOrUpdateShipInfoInput } from "../../services/shipInfo/dto/CreateOrUpdateShipInfoInput";
import { GetShipInfoOutput } from "../../services/shipInfo/dto/GetShipInfoOutput";
import ShipInfoStore from "../../stores/shipInfoStore";
import Stores from "../../stores/storeIdentifier";
import {
	getTablePaginationOptions,
	renderDate,
	renderSearchIcon,
} from "../renderUtils";
import {
	FilterDropdownProps,
	TablePaginationConfig,
	TableRowSelection,
} from "antd/lib/table/interface";

export interface IShipInfoProps {
	shipInfoStore: ShipInfoStore;
}

export interface IShipInfoState {
	shipId: number;
	maxResultCount: number;
	skipCount: number;
	sorting: string;
	searchTextInsideTable: Array<{ index: string; searchText: string }>;
	searchTextTable: string;
	searchedColumnTable: string;
	selectedRows: ListResultDto<EntityDto>;
	searchColumn: string;
	loading: boolean;
}

@inject(Stores.ShipInfoStore)
@observer
class ShipInfo extends AppComponentBase<IShipInfoProps, IShipInfoState> {
	state = {
		maxResultCount: 10,
		skipCount: 0,
		shipId: 0,
		sorting: "",
		searchTextInsideTable: [{ index: "", searchText: "" }],
		searchTextTable: "",
		searchedColumnTable: "",
		selectedRows: {
			items: [],
		},
		searchColumn: "",
		loading: false,
	};

	async componentDidMount() {
		await this.getAll();
	}

	async getAll() {
		this.setState({ loading: true });
		await this.props.shipInfoStore.getAll({
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: this.state.sorting,
			searchColumn: this.state.searchColumn,
		});
		this.setState({ loading: false });
	}

	public async stoppedSim() {
		await http.get("api/StoppedSim", {
			baseURL: process.env.REACT_APP_FUNCTION_URL,
		});
	}

	public async ecaSim() {
		await http.get("api/ECASim", {
			baseURL: process.env.REACT_APP_FUNCTION_URL,
		});
	}

	public async restartSim() {
		await http.get("api/ResetSim", {
			baseURL: process.env.REACT_APP_FUNCTION_URL,
		});
	}

	getColumnSearchProps = (
		dataIndex: string,
	): ColumnProps<GetShipInfoOutput> => ({
		filterDropdown: (props: FilterDropdownProps) => (
			<div style={{ padding: 8 }}>
				<Input
					ref={(node) => {
						if (!node) return;
					}}
					autoFocus
					placeholder={`Search ${dataIndex}`}
					value={props.selectedKeys ? props.selectedKeys[0] : ""}
					onChange={(e) => {
						if (!props.setSelectedKeys) return;
						props.setSelectedKeys(e.target.value ? [e.target.value] : []);
					}}
					onPressEnter={() =>
						this.handleSearchTable(
							props.selectedKeys || [],
							dataIndex,
							props.confirm,
						)
					}
					style={{ width: 188, marginBottom: 8, display: "block" }}
				/>
				<Button
					type="primary"
					onClick={() =>
						this.handleSearchTable(
							props.selectedKeys || [],
							dataIndex,
							props.confirm,
						)
					}
					icon={<SearchOutlined />}
					size="small"
					style={{ width: 90, marginRight: 8 }}
				>
					Search
				</Button>
				<Button
					onClick={() => this.handleReset(dataIndex, props.clearFilters)}
					size="small"
					style={{ width: 90 }}
				>
					Reset
				</Button>
			</div>
		),
		filterIcon: renderSearchIcon,
	});

	handleTableChange = (pagination: TablePaginationConfig) => {
		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
			},
			async () => await this.getAll(),
		);
	};

	handleSearchTable = (
		selectedKeys: React.Key[],
		dataIndex: string,
		confirm?: () => void,
	) => {
		if (dataIndex === "title") {
			this.setState(
				{ sorting: selectedKeys[0].toString() },
				async () => await this.getAll(),
			);
			return;
		}
		const allSearches = this.state.searchTextInsideTable;
		const filtred = allSearches.filter((x) => x.index !== dataIndex);

		filtred.push({ index: dataIndex, searchText: selectedKeys[0].toString() });
		if (confirm) confirm();
		this.setState({
			searchTextTable: selectedKeys[0].toString(),
			searchedColumnTable: dataIndex,
			searchTextInsideTable: filtred,
		});
	};

	handleReset = (dataIndex: string, clearFilters?: () => void) => {
		const allSearches = this.state.searchTextInsideTable;
		const filtred = allSearches.filter((x) => x.index !== dataIndex);
		if (clearFilters) clearFilters();
		this.setState({
			searchTextTable: "",
			searchTextInsideTable: filtred,
		});
	};

	public render() {
		const { shipsInfo } = this.props.shipInfoStore;
		const paginationOptions = getTablePaginationOptions(shipsInfo?.totalCount);
		const columns = [
			{
				title: L("Ship Name"),
				dataIndex: "ship.shipName",
				key: "ship.shipName",
				width: 150,
				sorter: (a: GetShipInfoOutput, b: GetShipInfoOutput) =>
					a.ship.shipName?.length - b.ship.shipName?.length,
				...this.getColumnSearchProps("ship.shipName"),
			},
			{
				title: L("IMO"),
				dataIndex: "ship.imoNumber",
				key: "ship.imoNumber",
				width: 150,
				sorter: (a: GetShipInfoOutput, b: GetShipInfoOutput) =>
					a.ship.imoNumber?.length - b.ship.imoNumber?.length,
				...this.getColumnSearchProps("ship.imoNumber"),
			},
			{
				title: L("Mmsi"),
				dataIndex: "mmsi",
				key: "mmsi",
				width: 150,
				sorter: (a: GetShipInfoOutput, b: GetShipInfoOutput) =>
					a.mmsi?.length - b.mmsi?.length,
				...this.getColumnSearchProps("mmsi"),
			},
			{
				title: L("Timestamp"),
				dataIndex: "timeStamp",
				key: "timeStamp",
				width: 150,
				render: (text: string) => renderDate(text),
				sorter: (a: GetShipInfoOutput, b: GetShipInfoOutput) =>
					new Date(a.timeStamp).getTime() - new Date(b.timeStamp).getTime(),
				...this.getColumnSearchProps("timeStamp"),
			},
			{
				title: L("Latitude"),
				dataIndex: "latitude",
				key: "latitude",
				width: 150,
				sorter: (a: GetShipInfoOutput, b: GetShipInfoOutput) =>
					a.latitude - b.latitude,
				...this.getColumnSearchProps("latitude"),
			},
			{
				title: L("Longitude"),
				dataIndex: "longitude",
				key: "longitude",
				width: 150,
				sorter: (a: GetShipInfoOutput, b: GetShipInfoOutput) =>
					a.longitude - b.longitude,
				...this.getColumnSearchProps("longitude"),
			},
			{
				title: L("Course"),
				dataIndex: "course",
				key: "course",
				width: 150,
				sorter: (a: GetShipInfoOutput, b: GetShipInfoOutput) =>
					a.course - b.course,
				...this.getColumnSearchProps("course"),
			},
			{
				title: L("Sog"),
				dataIndex: "sog",
				key: "sog",
				width: 150,
				sorter: (a: GetShipInfoOutput, b: GetShipInfoOutput) => a.sog - b.sog,
				...this.getColumnSearchProps("sog"),
			},
			{
				title: L("Heading"),
				dataIndex: "heading",
				key: "heading",
				width: 150,
				sorter: (a: GetShipInfoOutput, b: GetShipInfoOutput) =>
					a.heading - b.heading,
				...this.getColumnSearchProps("heading"),
			},
			{
				title: L("Distance Remaining"),
				dataIndex: "distanceRemaining",
				key: "distanceRemaining",
				width: 200,
				sorter: (a: GetShipInfoOutput, b: GetShipInfoOutput) =>
					a.distanceRemaining - b.distanceRemaining,
				...this.getColumnSearchProps("distanceRemaining"),
			},
			{
				title: L("Estimated Time Of Arrival"),
				dataIndex: "estimatedTimeOfArrival",
				key: "estimatedTimeOfArrival",
				width: 230,
				render: (item: string) => renderDate(item, true),
				sorter: (a: GetShipInfoOutput, b: GetShipInfoOutput) =>
					new Date(a.estimatedTimeOfArrival).getTime() -
					new Date(b.estimatedTimeOfArrival).getTime(),
				...this.getColumnSearchProps("estimatedTimeOfArrival"),
			},
			{
				title: L("Avg Sog"),
				dataIndex: "avgSOG",
				key: "avgSOG",
				width: 150,
				sorter: (a: GetShipInfoOutput, b: GetShipInfoOutput) =>
					a.avgSOG - b.avgSOG,
				...this.getColumnSearchProps("avgSOG"),
			},
			{
				title: L("Min Sog"),
				dataIndex: "minSOG",
				key: "minSOG",
				width: 150,
				sorter: (a: GetShipInfoOutput, b: GetShipInfoOutput) =>
					a.minSOG - b.minSOG,
				...this.getColumnSearchProps("minSOG"),
			},
			{
				title: L("Max Sog"),
				dataIndex: "maxSOG",
				key: "maxSOG",
				width: 150,
				sorter: (a: GetShipInfoOutput, b: GetShipInfoOutput) =>
					a.maxSOG - b.maxSOG,
				...this.getColumnSearchProps("maxSOG"),
			},
			{
				title: L("Sog Amplitude"),
				dataIndex: "sogAmplitude",
				key: "sogAmplitude",
				width: 150,
				sorter: (a: GetShipInfoOutput, b: GetShipInfoOutput) =>
					a.sogAmplitude - b.sogAmplitude,
				...this.getColumnSearchProps("sogAmplitude"),
			},
			{
				title: L("Time Between Rows(mins)"),
				dataIndex: "timeBetweenRows",
				key: "timeBetweenRows",
				width: 190,
				sorter: (a: GetShipInfoOutput, b: GetShipInfoOutput) =>
					a.timeBetweenRows - b.timeBetweenRows,
				...this.getColumnSearchProps("timeBetweenRows"),
			},
			{
				title: L("AIS Distance Points"),
				dataIndex: "aisDistancePoints",
				key: "aisDistancePoints",
				width: 190,
				sorter: (a: GetShipInfoOutput, b: GetShipInfoOutput) =>
					a.aisDistancePoints - b.aisDistancePoints,
				...this.getColumnSearchProps("aisDistancePoints"),
			},
			{
				title: L("AIS Distance Points Direct Line"),
				dataIndex: "aisDistancePointsDirectLine",
				key: "aisDistancePointsDirectLine",
				width: 200,
				sorter: (a: GetShipInfoOutput, b: GetShipInfoOutput) =>
					a.aisDistancePointsDirectLine - b.aisDistancePointsDirectLine,
				...this.getColumnSearchProps("aisDistancePointsDirectLine"),
			},
		];

		const rowSelection: TableRowSelection<GetShipInfoOutput> = {
			fixed: true,
			columnWidth: 60,
			onChange: (_, selectedRows: GetShipInfoOutput[]) => {
				const selectedRowsItems: EntityDto[] = selectedRows.map((x) => {
					return {
						id: x.id,
					};
				});
				this.setState({
					selectedRows: {
						items: selectedRowsItems,
					},
				});
			},
		};

		return (
			<Card>
				<Row>
					{/* <Col
              xs={{ span: 24, offset: 0 }}
              sm={{ span: 24, offset: 0 }}
              md={{ span: 24, offset: 0 }}
              lg={{ span: 24, offset: 0 }}
              xl={{ span: 24, offset: 0 }}
              xxl={{ span: 24, offset: 0 }}
            >
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ margin: '0 10px 0 0', height: 'fit-content' }}>Select Vessel</span>
                <Select
                  style={{ minWidth: '250px', marginRight: 10 }}
                  dropdownMenuStyle={{ width: '250px' }}
                  onChange={(value: string) => {
                    this.setState({  });
                  }}
                >

                    <Option >
                      
                    </Option>
                 
                </Select>
              </div>
            </Col> */}
				</Row>
				<Row>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<div style={{ display: "flex" }}>
							<Button
								style={{ marginRight: 20 }}
								type="primary"
								onClick={() => this.ecaSim()}
							>
								Simulation of ECA
							</Button>
							<Button
								style={{ marginRight: 20 }}
								type="primary"
								onClick={() => this.stoppedSim()}
							>
								Simulation of Stopped
							</Button>
							<Button type="primary" onClick={() => this.restartSim()}>
								Restart Simulation
							</Button>
						</div>
					</Col>
				</Row>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey={(record: CreateOrUpdateShipInfoInput) =>
								record.id.toString()
							}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={this.state.loading}
							dataSource={shipsInfo === undefined ? [] : shipsInfo.items}
							onChange={this.handleTableChange}
							rowSelection={rowSelection}
							scroll={{ x: true, y: 400 }}
						/>
					</Col>
				</Row>
				<Chat />
			</Card>
		);
	}
}

export default ShipInfo;
