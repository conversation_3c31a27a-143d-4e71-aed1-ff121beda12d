import { Card, Col, Radio, Row, Table } from "antd";
import { RadioChangeEvent } from "antd/lib/radio";
import { ColumnProps } from "antd/lib/table";
import { inject, observer } from "mobx-react";
import * as React from "react";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import { L } from "../../lib/abpUtility";
import { GetMovingAverageOutput } from "../../services/meterReading/dto/getMovingAverageOutput";
import MeterReadingStore from "../../stores/meterReadingStore";
import Stores from "../../stores/storeIdentifier";
import { renderTimeSpan } from "../renderUtils";

export interface IControlBoardProps {
	meterReadingStore: MeterReadingStore;
}

export interface IControlBoardState {
	maxResultCount: number;
	skipCount: number;
	sorting: string;
	searchTextInsideTable: Array<{ index: string; searchText: string }>;
	searchTextTable: string;
	searchedColumnTable: string;
	faopDuration: number;
	loadingFaop: boolean;
	faultsLogsDuration: number;
	loadingLogs: boolean;
}

const columns = [
	{
		title: L("Ship Name"),
		dataIndex: "shipName",
		key: "shipName",
	},
	{
		title: L("Meter Readings"),
		dataIndex: "totalCount",
		key: "totalCount",
	},
	{
		title: L("Total Errors"),
		dataIndex: "totalErrors",
		key: "totalErrors",
	},
	{
		title: L("% Error Meters"),
		dataIndex: "totalErrorsPercent",
		key: "totalErrorsPercent",
	},
	{
		title: L("Digits"),
		dataIndex: "digitsErrors",
		key: "digitsErrors",
	},
	{
		title: L("Low"),
		dataIndex: "lowerErrors",
		key: "lowerErrors",
	},
	{
		title: L("Consumption"),
		dataIndex: "consumptionErrors",
		key: "consumptionErrors",
	},
	{
		title: L("Fuel Type"),
		dataIndex: "fuelTypeErrors",
		key: "fuelTypeErrors",
	},
	{
		title: L("Manual"),
		dataIndex: "manualErrors",
		key: "manualErrors",
	},
];

const columnsVesselNotSendingData = [
	{
		title: L("Vessel name"),
		dataIndex: "shipName",
		key: "shipName",
	},
	{
		title: L("Since last data received"),
		dataIndex: "lastDate",
		key: "lastDate",
	},
];

const columnsFAOPPerformanceMeasuredPassages = [
	{
		title: L("Vessel"),
		dataIndex: "shipName",
		key: "shipName",
	},
	{
		title: L("FAOP Passages"),
		dataIndex: "passages",
		key: "passages",
	},
	{
		title: L("Total time"),
		dataIndex: "totalTime",
		key: "totalTime",
		render: renderTimeSpan,
	},
];

const columns24HMovingAverage: ColumnProps<GetMovingAverageOutput>[] = [
	{
		title: L("Error Free"),
		dataIndex: "withoutErrors",
		key: "withoutErrors",
		render: (text: string, item: GetMovingAverageOutput) => (
			<>
				<div>{item.withoutErrors.toFixed(1)}</div>
			</>
		),
	},
	{
		title: L("With Errors"),
		dataIndex: "withErrors",
		key: "withErrors",
		render: (text: string, item: GetMovingAverageOutput) => (
			<>
				<div>{item.withErrors.toFixed(1)}</div>
			</>
		),
	},
];

@inject(Stores.MeterReadingStore)
@observer
class ControlBoard extends AppComponentBase<
	IControlBoardProps,
	IControlBoardState
> {
	state: IControlBoardState = {
		maxResultCount: 10,
		skipCount: 0,
		sorting: "",
		searchTextInsideTable: [{ index: "", searchText: "" }],
		searchTextTable: "",
		searchedColumnTable: "",
		faopDuration: 7,
		loadingFaop: false,
		loadingLogs: false,
		faultsLogsDuration: 7,
	};

	async componentDidMount() {
		await Promise.all([
			this.props.meterReadingStore.getMovingAverage(),
			this.props.meterReadingStore.getFaopPerformance(this.state.faopDuration),
			this.props.meterReadingStore.getFaultLogs(this.state.faultsLogsDuration),
			this.props.meterReadingStore.getLastMeterReadings(),
		]);
	}

	changeFaopPerfomance = async (e: RadioChangeEvent) => {
		this.setState({ loadingFaop: true });
		const value = e.target.value;
		if (!value) return;
		if (typeof value !== "number") return;
		this.setState({ faopDuration: value });
		await this.props.meterReadingStore.getFaopPerformance(value);
		this.setState({ loadingFaop: false });
	};

	changeFaultsLogs = async (e: RadioChangeEvent) => {
		this.setState({ loadingLogs: true });
		const value = e.target.value;
		if (!value) return;
		if (typeof value !== "number") return;
		this.setState({ faultsLogsDuration: value });
		await this.props.meterReadingStore.getFaultLogs(value);
		this.setState({ loadingLogs: false });
	};

	render() {
		const { movingAverage, faultLogs, faopPerformance, lastReadings } =
			this.props.meterReadingStore;

		return (
			<>
				<Card>
					<Row style={{ marginBlock: "2rem" }}>
						<Col
							xs={{ span: 6, offset: 0 }}
							sm={{ span: 6, offset: 0 }}
							md={{ span: 6, offset: 0 }}
							lg={{ span: 6, offset: 0 }}
							xl={{ span: 6, offset: 0 }}
							xxl={{ span: 6, offset: 0 }}
						>
							<Table
								rowKey={""}
								size={"middle"}
								bordered={true}
								columns={columns24HMovingAverage}
								title={() => (
									<>
										<h3 style={{ color: "var(--antd-wave-shadow-color)" }}>
											Read Meter sets / vessel received{" "}
										</h3>
										<h3
											style={{
												fontWeight: 500,
												color: "var(--antd-wave-shadow-color)",
											}}
										>
											Last 24h
										</h3>
									</>
								)}
								loading={movingAverage === undefined}
								pagination={false}
								// pagination={paginationOptions}
								dataSource={movingAverage === undefined ? [] : [movingAverage]}
							/>
						</Col>
					</Row>
					<Row style={{ marginBlock: "1rem" }}>
						<Col
							xs={{ span: 11, offset: 0 }}
							sm={{ span: 11, offset: 0 }}
							md={{ span: 11, offset: 0 }}
							lg={{ span: 11, offset: 0 }}
							xl={{ span: 11, offset: 0 }}
							xxl={{ span: 11, offset: 0 }}
						>
							<Table
								rowKey={""}
								size={"middle"}
								bordered={true}
								columns={columnsVesselNotSendingData}
								title={() => (
									<h3 style={{ color: "var(--antd-wave-shadow-color)" }}>
										Registered vessels not sending data
									</h3>
								)}
								// pagination={paginationOptions}
								loading={lastReadings === undefined}
								pagination={false}
								dataSource={lastReadings === undefined ? [] : lastReadings}
							/>
						</Col>
						<Col
							xs={{ span: 11, offset: 2 }}
							sm={{ span: 11, offset: 2 }}
							md={{ span: 11, offset: 2 }}
							lg={{ span: 11, offset: 2 }}
							xl={{ span: 11, offset: 2 }}
							xxl={{ span: 11, offset: 2 }}
						>
							<Table
								rowKey={""}
								size={"middle"}
								bordered={true}
								columns={columnsFAOPPerformanceMeasuredPassages}
								loading={
									faopPerformance === undefined || this.state.loadingFaop
								}
								title={() => (
									<div
										style={{
											display: "flex",
											justifyContent: "space-between",
											gap: "8px",
										}}
									>
										<h3 style={{ color: "var(--antd-wave-shadow-color)" }}>
											Measured Performance Passages
										</h3>
										<Radio.Group
											value={this.state.faopDuration}
											onChange={this.changeFaopPerfomance}
											buttonStyle="solid"
										>
											<Radio.Button value={1}>24h</Radio.Button>
											<Radio.Button value={7}>7d</Radio.Button>
											<Radio.Button value={28}>28d</Radio.Button>
										</Radio.Group>
									</div>
								)}
								// pagination={paginationOptions}

								pagination={false}
								dataSource={
									faopPerformance === undefined ? [] : faopPerformance
								}
							/>
						</Col>
					</Row>
					<Row style={{ marginBlock: "2rem" }}>
						<Col
							xs={{ span: 24, offset: 0 }}
							sm={{ span: 24, offset: 0 }}
							md={{ span: 24, offset: 0 }}
							lg={{ span: 24, offset: 0 }}
							xl={{ span: 24, offset: 0 }}
							xxl={{ span: 24, offset: 0 }}
						>
							<Table
								rowKey={""}
								size={"middle"}
								bordered={true}
								columns={columns}
								// pagination={paginationOptions}
								title={() => (
									<div
										style={{
											display: "flex",
											justifyContent: "space-between",
											gap: "8px",
										}}
									>
										<h3 style={{ color: "var(--antd-wave-shadow-color)" }}>
											Faults Log
										</h3>
										<Radio.Group
											value={this.state.faultsLogsDuration}
											onChange={this.changeFaultsLogs}
											buttonStyle="solid"
										>
											<Radio.Button value={1}>24h</Radio.Button>
											<Radio.Button value={7}>7d</Radio.Button>
											<Radio.Button value={28}>28d</Radio.Button>
										</Radio.Group>
									</div>
								)}
								loading={faultLogs === undefined || this.state.loadingLogs}
								pagination={false}
								dataSource={faultLogs === undefined ? [] : faultLogs}
							/>
						</Col>
					</Row>
				</Card>
				<Chat />
			</>
		);
	}
}

export default ControlBoard;
