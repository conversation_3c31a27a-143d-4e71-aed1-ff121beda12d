import * as React from "react";

import { Redirect, Route, RouteProps } from "react-router-dom";

import { isGranted } from "../../lib/abpUtility";

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
declare let abp: any;

type ProtectedRouteProps = {
	path?: string;
	Component?: JSX.Element;
	permission?: string;
} & RouteProps;

const ProtectedRoute = ({
	path,
	component: Component,
	permission,
	render,
	...rest
}: ProtectedRouteProps) => {
	return (
		<Route
			{...rest}
			render={(props) => {
				if (!abp.session.userId)
					return (
						<Redirect
							to={{
								pathname: "/user/login",
								state: { from: props.location },
							}}
						/>
					);

				if (permission && !isGranted(permission)) {
					return (
						<Redirect
							to={{
								pathname: "/exception?type=401",
								state: { from: props.location },
							}}
						/>
					);
				}

				// biome-ignore lint/style/noNonNullAssertion:
				return Component ? <Component {...props} /> : render!(props);
			}}
		/>
	);
};

export default ProtectedRoute;
