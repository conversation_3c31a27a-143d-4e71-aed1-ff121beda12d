import { Col, Row, Statistic } from "antd";
import moment from "moment";
import React from "react";
import { ShipTrace } from "../../../services/notifications/dto/ShipTrace";
export interface NotificationFooterProps {
	shipTrace: ShipTrace | null;
}
const NotificationFooter: React.FC<NotificationFooterProps> = ({
	shipTrace,
}) => {
	if (shipTrace == null) {
		return <></>;
	}

	return (
		<Row gutter={6} style={{ padding: 2, margin: 5 }}>
			{/* style={{background: "#ffefea", padding: 2,  borderRadius: 15, margin: 5}}> */}
			<Col span={8}>
				<Statistic
					valueStyle={{ fontSize: "20px" }}
					title="Date"
					value={moment.utc(moment(shipTrace.Timestamp).utc()).format()}
				/>
			</Col>
			<Col span={6}>
				<Statistic
					valueStyle={{ fontSize: "20px" }}
					title="Status"
					value={shipTrace.Status}
				/>
			</Col>
			<Col span={4}>
				<Statistic title="Latitude" value={shipTrace.Latitude} precision={5} />
			</Col>
			<Col span={4}>
				<Statistic
					title="Longitude"
					value={shipTrace.Longitude}
					precision={5}
				/>
			</Col>
			<Col span={2}>
				<Statistic title="SOG" value={shipTrace.SOG} />
			</Col>
		</Row>
	);
};

export default NotificationFooter;
