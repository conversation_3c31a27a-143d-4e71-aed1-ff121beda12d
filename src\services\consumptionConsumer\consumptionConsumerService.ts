import { AbpResponse } from "../dto/abpResponse";
import { EntityDto } from "../dto/entityDto";
import { PagedFilterAndSortedRequest } from "../dto/pagedFilterAndSortedRequest";
import { PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoints";
import http from "../httpService";
import { ConsumptionConsumerDto } from "./dto/consumptionConsumerDto";

class ConsumptionConsumerService {
	public async create(createConsumptionConsumerInput: ConsumptionConsumerDto) {
		const result = await http.post(
			Endpoint.ConsumptionConsumer.Create,
			createConsumptionConsumerInput,
		);
		return result.data.result;
	}

	public async update(updateConsumptionConsumerInput: ConsumptionConsumerDto) {
		const result = await http.put(
			Endpoint.ConsumptionConsumer.Update,
			updateConsumptionConsumerInput,
		);
		return result.data.result;
	}

	public async delete(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.ConsumptionConsumer.Delete, {
			params: entityDto,
		});
		return result.data;
	}

	public async get(entityDto: EntityDto): Promise<ConsumptionConsumerDto> {
		const result = await http.get(Endpoint.ConsumptionConsumer.Get, {
			params: entityDto,
		});
		return result.data.result;
	}

	public async getAll(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequest,
	): Promise<PagedResultDto<ConsumptionConsumerDto>> {
		const result = await http.get(Endpoint.ConsumptionConsumer.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}

	public async getConsumptionConsumerByShip(
		shipId: number,
	): Promise<PagedResultDto<ConsumptionConsumerDto>> {
		const result = await http.get<
			AbpResponse<PagedResultDto<ConsumptionConsumerDto>>
		>(Endpoint.ConsumptionConsumer.GetAllForShip, {
			params: { shipId: shipId },
		});
		return result.data.result;
	}

	public async getConsumerTypes(): Promise<{ [key: number]: string }> {
		const result = await http.get<AbpResponse<{ [key: number]: string }>>(
			Endpoint.ConsumptionConsumer.GetConsumerTypes,
		);
		return result.data.result;
	}
	public async getMeasureUnits(): Promise<{ [key: number]: string }> {
		const result = await http.get<AbpResponse<{ [key: number]: string }>>(
			Endpoint.ConsumptionConsumer.GetMeasureUnits,
		);
		return result.data.result;
	}

	public async getNames(shipId?: number) {
		const result = await http.get(Endpoint.ConsumptionConsumer.GetNames, {
			params: { shipId },
		});
		return result.data.result;
	}
}

export default new ConsumptionConsumerService();
