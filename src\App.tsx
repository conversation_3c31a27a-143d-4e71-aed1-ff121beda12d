import "./App.css";

import * as React from "react";

import Router from "./components/Router";
import SessionStore from "./stores/sessionStore";
import Stores from "./stores/storeIdentifier";
import { inject } from "mobx-react";

export interface IAppProps {
	sessionStore?: SessionStore;
}

@inject(Stores.SessionStore)
class App extends React.Component<IAppProps> {
	async componentDidMount() {
		await this.props.sessionStore?.getCurrentLoginInformations();
	}

	public render() {
		return <Router />;
	}
}

export default App;
