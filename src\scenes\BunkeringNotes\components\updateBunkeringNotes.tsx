import { Form } from "@ant-design/compatible";
import { InboxOutlined } from "@ant-design/icons";
import * as React from "react";
import "@ant-design/compatible/assets/index.css";
import { DatePicker, Input, InputNumber, Modal, Upload } from "antd";
import FormItem from "antd/lib/form/FormItem";
import AppComponentBase from "../../../components/AppComponentBase";
import ComponentLayout from "../../../components/Layout/ComponentLayout";
import TrustDropdown from "../../../components/TrustDropdown";
import { L } from "../../../lib/abpUtility";
import Endpoint from "../../../services/endpoints";
import BunkeringNotesStore from "../../../stores/bunkeringNotesStore";
import FuelTypeStore from "../../../stores/fuelTypeStore";
import PortStore from "../../../stores/portStore";
import ShipStore from "../../../stores/shipStore";
import Constants from "../../Constants";
import { FormWidths } from "../../ViewSettingsConsts";
import { ModalFormComponentProps } from "../../modalFormComponentProps";
import Utils from "./../../../utils/utils";
import rules from "./updateBunkeringNotes.validation";
// biome-ignore lint/suspicious/noExplicitAny: <explanation>
declare let abp: any;
const { Dragger } = Upload;

export interface IUpdateBunkeringNotesProps extends ModalFormComponentProps {
	bunkeringNotesStore: BunkeringNotesStore;
	fuelTypeStore: FuelTypeStore;
	portStore: PortStore;
	shipStore: ShipStore;
	defaultFiles: { url: string; name: string }[];
}

export interface IUpdateBunkeringState {
	maxResultCount: number;
	skipCount: number;
	sorting: string;
	confirmDirty: boolean;
	fetching: boolean;
	selectedFiles: string[];
	fileUploadRef: React.RefObject<HTMLInputElement>;
	selectedHours: Array<string>;
}
class UpdateBunkeringNotes extends AppComponentBase<
	IUpdateBunkeringNotesProps,
	IUpdateBunkeringState
> {
	bunkeringNotesStore: BunkeringNotesStore = this.props.bunkeringNotesStore;
	constructor(props: IUpdateBunkeringNotesProps) {
		super(props);

		this.fetchPorts = Utils.debounce(this.fetchPorts, 500);
	}

	state: IUpdateBunkeringState = {
		maxResultCount: Constants.allEntity,
		skipCount: 0,
		sorting: "",
		confirmDirty: false,
		fetching: false,
		selectedFiles: [],
		fileUploadRef: React.createRef(),
		selectedHours: [],
	};

	async componentDidMount() {
		await Promise.all([
			this.props.fuelTypeStore.getAll({
				maxResultCount: this.state.maxResultCount,
				skipCount: this.state.skipCount,
				keyword: this.state.sorting,
			}),
			this.props.portStore.getAll(""),
			this.props.shipStore.getShipNames(),
		]);
	}

	fetchPorts = (value: string) => {
		if (value.length < 3) return;
		this.setState({ fetching: true });
		this.props.portStore.getAll(value).finally(() => {
			this.setState({ fetching: false });
			this.forceUpdate();
		});
	};

	render() {
		const { getFieldDecorator } = this.props.form;
		const { visible, onCancel, onCreate, okButtonDisabled } = this.props;
		const { fuelTypeLoaded } = this.props.bunkeringNotesStore;
		const { editBunkeringNotes } = this.props.bunkeringNotesStore;

		const fuelTypes = new Map(
			this.props.fuelTypeStore.fuelTypes.items.map((x) => [x.id, x.type]),
		);

		let ports = this.props.portStore.ports;

		if (editBunkeringNotes) {
			if (editBunkeringNotes.portOfBunkering) {
				ports = new Map([
					[
						editBunkeringNotes.portOfBunkering.id,
						editBunkeringNotes.portOfBunkering.name,
					],
					...this.props.portStore.ports,
				]);
			} else {
				ports = new Map([
					[0, editBunkeringNotes.portName],
					...this.props.portStore.ports,
				]);
			}
		}

		const ships = this.props.shipStore.allShipNames
			? new Map(
					this.props.shipStore.allShipNames.map((x) => [x.id, x.shipName]),
				)
			: new Map();
		return (
			<Modal
				visible={visible}
				cancelText={L("Cancel")}
				okText={L("OK")}
				onCancel={onCancel}
				onOk={onCreate}
				title={L("Bunkering Note")}
				okButtonProps={{ disabled: okButtonDisabled }}
			>
				{getFieldDecorator("id")(<Input hidden />)}
				<Form layout="vertical">
					<FormItem
						label={L("Select port of bunkering")}
						{...ComponentLayout.formItemLayout}
					>
						<TrustDropdown
							data={fuelTypeLoaded}
							placeholder={L("Port Of Bunkering")}
							formRef={this.props.form}
							fieldDecoratorKey={"portOfBunkering.id"}
							onSearch={this.fetchPorts}
							customData={ports}
							initialValue={
								this.props.form.getFieldValue("portOfBunkering.id") || 0
							}
							loading={this.state.fetching}
							rules={rules.portOfBunkering}
						/>
					</FormItem>
					<FormItem
						label={L("Select fuel type loaded")}
						{...ComponentLayout.formItemLayout}
					>
						<TrustDropdown
							data={fuelTypeLoaded}
							placeholder={L("Fuel Type Loaded")}
							formRef={this.props.form}
							fieldDecoratorKey={"fuelTypeLoaded.id"}
							customData={fuelTypes}
							rules={rules.fuelTypeLoaded}
						/>
					</FormItem>
					<FormItem
						label={L("Select ship")}
						{...ComponentLayout.formItemLayout}
					>
						<TrustDropdown
							data={fuelTypeLoaded}
							placeholder={L("Ship")}
							formRef={this.props.form}
							fieldDecoratorKey={"ship.id"}
							customData={ships}
							rules={rules.fuelTypeLoaded}
						/>
					</FormItem>
					<FormItem label={L("Supplier")} {...ComponentLayout.formItemLayout}>
						{getFieldDecorator("supplier", { rules: rules.supplier })(
							<Input style={{ width: FormWidths.wide, marginLeft: "15px" }} />,
						)}
					</FormItem>
					<FormItem label={L("CO2 Factor")} {...ComponentLayout.formItemLayout}>
						{getFieldDecorator("co2Factor", { rules: rules.co2Factor })(
							<InputNumber
								style={{ width: FormWidths.wide, marginLeft: "15px" }}
							/>,
						)}
					</FormItem>
					<FormItem label={L("Fuel LCV")} {...ComponentLayout.formItemLayout}>
						{getFieldDecorator("fuelLCV", { rules: rules.fuelLCV })(
							<InputNumber
								style={{ width: FormWidths.wide, marginLeft: "15px" }}
							/>,
						)}
					</FormItem>
					<FormItem
						label={L("Quantity Loaded")}
						{...ComponentLayout.formItemLayout}
					>
						{getFieldDecorator("quantityLoaded", {
							rules: rules.quantityLoaded,
						})(
							<InputNumber
								style={{ width: FormWidths.wide, marginLeft: "15px" }}
							/>,
						)}
					</FormItem>
					<FormItem label={L("Density")} {...ComponentLayout.formItemLayout}>
						{getFieldDecorator("density", { rules: rules.destiny })(
							<InputNumber
								style={{ width: FormWidths.wide, marginLeft: "15px" }}
							/>,
						)}
					</FormItem>
					<FormItem label={L("Date")} {...ComponentLayout.formItemLayout}>
						{getFieldDecorator("date", { rules: rules.date })(
							<DatePicker
								style={{ width: FormWidths.wide, marginLeft: "15px" }}
								onChange={(date) =>
									this.props.form.setFieldsValue({ date: date })
								}
							/>,
						)}
					</FormItem>
					<FormItem label={L("Documents")} {...ComponentLayout.formItemLayout}>
						{getFieldDecorator("tempFileUrls")(
							<Dragger
								style={{ width: FormWidths.wide, marginLeft: "15px" }}
								fileList={
									this.props.form.getFieldValue("tempFileUrls")?.fileList
								}
								multiple
								headers={{
									"x-ms-blob-type": "BlockBlob",
									Authorization: `Bearer ${abp.auth.getToken()}`,
								}}
								onChange={(info) => {
									let fileList = [...info.fileList];
									fileList = fileList.map((file) => {
										if (file.response) {
											file.url = file.response.result.url;
										}
										return file;
									});
								}}
								action={`${process.env.REACT_APP_REMOTE_SERVICE_BASE_URL}/${Endpoint.Storage.UploadBunkeringNotes}`}
							>
								<span className="ant-upload-drag-icon">
									<InboxOutlined style={{ fontSize: "30px" }} />
								</span>
								<p className="ant-upload-text" style={{ fontSize: "0.7rem" }}>
									Click or drag file to this area to upload
								</p>
							</Dragger>,
						)}
					</FormItem>
				</Form>
			</Modal>
		);
	}
}

export default Form.create<IUpdateBunkeringNotesProps>()(UpdateBunkeringNotes);
