import { Form } from "@ant-design/compatible";
import "@ant-design/compatible/assets/index.css";
import { DatePicker, Input, InputNumber, Modal, Select } from "antd";
import moment from "moment";
import AppComponentBase from "../../../components/AppComponentBase";
import ComponentLayout from "../../../components/Layout/ComponentLayout";
import { L } from "../../../lib/abpUtility";
import { GetUserOutput } from "../../../services/user/dto/getUserOutput";
import CargoStore from "../../../stores/cargoStore";
import EventsStore from "../../../stores/eventsStore";
import ShipStore from "../../../stores/shipStore";
import { FormWidths } from "../../ViewSettingsConsts";
import { ModalFormComponentProps } from "../../modalFormComponentProps";
import rules from "./createOrUpdateCargo.validation";

export interface ICreateOrUpdateCargoProps extends ModalFormComponentProps {
	cargoStore: CargoStore;
	shipStore: ShipStore;
	listCounterparty: GetUserOutput[];
	eventsStore: EventsStore;
}

class CreateOrUpdateCargo extends AppComponentBase<ICreateOrUpdateCargoProps> {
	cargoStore: CargoStore = this.props.cargoStore;
	shipStore: ShipStore = this.props.shipStore;
	eventsStore: EventsStore = this.props.eventsStore;
	componentDidUpdate(): void {
		if (
			this.shipStore.allShipNames?.[0] &&
			this.props.form.getFieldValue("ship.id") === 0
		) {
			this.props.form.setFieldsValue({
				ship: { id: this.shipStore.allShipNames[0].id },
			});
		}
	}

	render() {
		const { getFieldDecorator } = this.props.form;
		const { visible, onCancel, onCreate, okButtonDisabled } = this.props;

		const ships = this.props.shipStore.allShipNames
			? Object.values(this.props.shipStore.allShipNames)
			: [];

		return (
			<Modal
				visible={visible}
				cancelText={L("Cancel")}
				okText={L("OK")}
				onCancel={onCancel}
				onOk={onCreate}
				title={L("Cargo")}
				okButtonProps={{ loading: okButtonDisabled }}
			>
				<Form layout="vertical">
					{getFieldDecorator("user")(<Input hidden />)}
					<Form.Item
						label={L("Ship Name")}
						{...ComponentLayout.formItemLayout}
						required
					>
						{getFieldDecorator("ship.id", {
							rules: rules.shipName,
						})(
							<Select
								{...ComponentLayout.formItemLayout}
								style={{ width: FormWidths.wide }}
								placeholder={L("Select Ship")}
								onChange={(value: string) => {
									this.props.form.setFieldsValue({
										ship: {
											id: value,
										},
									});
								}}
							>
								{ships.map((x) => {
									return (
										<Select.Option key={x.id} value={x.id}>
											{x.shipName}
										</Select.Option>
									);
								})}
							</Select>,
						)}
					</Form.Item>
					<Form.Item
						label={L("Date of cargo transport")}
						{...ComponentLayout.formItemLayout}
						required
					>
						{getFieldDecorator("dateOfTransport", {
							rules: rules.dateOfTransport,
							initialValue: null,
						})(
							<DatePicker
								showTime={{ format: "HH:mm" }}
								style={{ width: FormWidths.wide }}
								onChange={(date) =>
									this.props.form.setFieldsValue({ dateOfTransport: date })
								}
								disabledDate={(current) =>
									current ? current > moment().startOf("day") : false
								}
							/>,
						)}
					</Form.Item>
					<Form.Item
						label={L("Cargo Quantity")}
						{...ComponentLayout.formItemLayout}
					>
						{getFieldDecorator("quantity", { rules: rules.cargo })(
							<InputNumber style={{ width: FormWidths.wide }} />,
						)}
					</Form.Item>
				</Form>
			</Modal>
		);
	}
}

export default Form.create<ICreateOrUpdateCargoProps>()(CreateOrUpdateCargo);
