import { Moment } from "moment";
import { EntityDto } from "../../dto/entityDto";

export interface CreateOrUpdateBunkeringNotesInput {
	portOfBunkering: EntityDto & { name: string };
	creationTime: Date;
	date: Moment;
	fuelTypeLoaded: EntityDto;
	quantityLoaded: string;
	density: string;
	portName: string;
	loadedInto: string;
	supplier: string;
	id: number;
	fileUrls: { url: string; name: string }[];
	co2Factor: number;
	fuelLCV: number;
}
