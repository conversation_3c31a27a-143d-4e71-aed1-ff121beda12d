import { Modal } from "antd";
import * as React from "react";
import { ECABorder } from "../../../services/ais/dto/ECABorder";
import { ShipTrace } from "../../../services/notifications/dto/ShipTrace";
import { SingleNotification } from "../../../services/notifications/dto/SingleNotification";
import NotificationMapWrapper from "./notificationMapWrapper";

export interface NotificationMapProps {
	visible: boolean;
	onCancel: () => void;
	notification: SingleNotification | null;
	shipTraces: ShipTrace[] | null;
	ecaBorders: ECABorder[];
}

class NotificationModal extends React.Component<NotificationMapProps> {
	render() {
		const { visible, onCancel } = this.props;
		const { notification } = this.props;
		const { shipTraces } = this.props;
		const { ecaBorders } = this.props;

		if (notification == null || shipTraces == null) return <div />;

		return (
			<Modal
				title={<>Ship name: {notification.shipName}</>}
				visible={visible}
				onCancel={onCancel}
				footer={null}
				bodyStyle={{ padding: 0 }}
				width={760}
			>
				<NotificationMapWrapper
					notification={notification}
					shipTraces={shipTraces}
					ecaBorders={ecaBorders}
				/>
			</Modal>
		);
	}
}

export default NotificationModal;
