import { action, observable } from "mobx";
import draftService from "../services/draft/draftService";
import type { DraftDto } from "../services/draft/dto/draftDto";
import { EntityDto } from "../services/dto/entityDto";
import type { PagedFilterAndSortedRequest } from "../services/dto/pagedFilterAndSortedRequest";
import type {
	ListResultDto,
	PagedResultDto,
} from "../services/dto/pagedResultDto";

class DraftStore {
	@observable drafts!: PagedResultDto<DraftDto>;
	@observable tenantDrafts!: PagedResultDto<DraftDto>;
	@observable draftModel!: DraftDto;

	@action
	async getAll(pagedFilterAndSortedRequest: PagedFilterAndSortedRequest) {
		const result = await draftService.getAll(pagedFilterAndSortedRequest);
		this.drafts = result;
	}

	@action
	async get(entityDto: EntityDto) {
		const result = await draftService.get(entityDto);
		this.draftModel = result;
	}

	@action
	async delete(entityDto: EntityDto) {
		await draftService.delete(entityDto);
		this.drafts.items = this.drafts.items.filter(
			(x: DraftDto) => x.id !== entityDto.id,
		);
	}

	@action
	async resendToGs(entities: ListResultDto<EntityDto>) {
		const result: Array<DraftDto> = await draftService.resendToGs(entities);

		this.drafts.items = this.drafts.items.map((item) => {
			if (result.map((x) => x.id).includes(item.id)) {
				item.gsStatus =
					result.find((x) => x.id === item.id)?.gsStatus ?? item.gsStatus;
			}
			return item;
		});
	}
}

export default DraftStore;
