import {
	ValidationRule,
	WrappedFormUtils,
} from "@ant-design/compatible/lib/form/Form";
import { Select, Spin } from "antd";
import * as React from "react";
import { FormWidths } from "../scenes/ViewSettingsConsts";
import { normalizeCamelCaseText } from "../scenes/renderUtils";
import AppComponentBase from "./AppComponentBase";
import ComponentLayout from "./Layout/ComponentLayout";

const { Option } = Select;

export interface ITrustDropdownProps {
	data: string[] | { [key: number]: string };
	placeholder: string;
	formRef: WrappedFormUtils<any>;
	fieldDecoratorKey: string;
	rules: ValidationRule[];
	customData?: Map<number | string, string>;
	onChange?: any;
	onSearch?: (value: string) => void;
	disabled?: boolean;
	loading?: boolean;
	customStyles?: React.CSSProperties;
	initialValue?: any;
}

class TrustDropdown extends AppComponentBase<ITrustDropdownProps> {
	render() {
		const {
			data,
			placeholder,
			rules,
			fieldDecoratorKey,
			onChange,
			disabled,
			initialValue,
			onSearch,
			loading,
		} = this.props;
		const { getFieldDecorator } = this.props.formRef;
		const hasCustomData: boolean = !!this.props.customData;
		const optionsArray: JSX.Element[] = [];

		if (this.props.customData) {
			for (const [key, value] of this.props.customData) {
				const i: number | string = key;
				optionsArray.push(
					<Option key={i} value={i}>
						{value}
					</Option>,
				);
			}
		}

		return getFieldDecorator(fieldDecoratorKey, {
			rules: rules,
			initialValue: initialValue,
		})(
			<Select
				{...ComponentLayout.formItemLayout}
				showSearch
				style={{
					width: FormWidths.wide,
					marginLeft: 15,
					...this.props.customStyles,
				}}
				placeholder={placeholder}
				optionFilterProp="children"
				filterOption={(input, option) =>
					option?.value
						? option?.value
								?.toString()
								.toLowerCase()
								.indexOf(input.toLowerCase()) >= 0
						: false
				}
				onChange={onChange}
				onSearch={onSearch}
				disabled={disabled}
				notFoundContent={loading ? <Spin /> : undefined}
			>
				{hasCustomData && optionsArray.map((optionItem) => optionItem)}
				{!hasCustomData &&
					data &&
					Object.keys(data ?? {}).map((item, i) => (
						<Option key={+item} value={+item}>
							{normalizeCamelCaseText(String(data[item as unknown as number]))}
						</Option>
					))}
			</Select>,
		);
	}
}

export default TrustDropdown;
