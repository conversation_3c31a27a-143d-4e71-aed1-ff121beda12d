import { action, observable } from "mobx";
import answersForEventsService from "../services/answersForEvents/answersForEventsService";
import type { CreateOrUpdateAnswersForEventsInput } from "../services/answersForEvents/dto/CreateOrUpdateAnswersForEventsInput";
import { GetAnswersForEventsOutput } from "../services/answersForEvents/dto/GetAnswersForEventsOutput";
import type { PagedFilterAndSortedRequestAnswersForEvents } from "../services/answersForEvents/dto/PagedFilterAndSortedRequestAnswersForEvents";
import type { UpdateAnswersForEventsInput } from "../services/answersForEvents/dto/UpdateAnswersForEventsInput";
import { EntityDto } from "../services/dto/entityDto";
import type { PagedResultDto } from "../services/dto/pagedResultDto";

class AnswersForEventsStore {
	@observable answersForEvents!: PagedResultDto<GetAnswersForEventsOutput>;
	@observable editAnswersForEvents!: CreateOrUpdateAnswersForEventsInput;

	@action
	async create(
		createOrUpdateAnswersForEventsInput: CreateOrUpdateAnswersForEventsInput,
	) {
		const result = await answersForEventsService.create(
			createOrUpdateAnswersForEventsInput,
		);
		this.answersForEvents.items.push(result);
	}

	@action
	async update(updateAnswersForEvents: UpdateAnswersForEventsInput) {
		const result = await answersForEventsService.update(updateAnswersForEvents);
		this.answersForEvents.items = this.answersForEvents.items.map(
			(x: GetAnswersForEventsOutput) => {
				if (x.id === updateAnswersForEvents.id) x = result;
				return x;
			},
		);
	}

	@action
	async delete(entityDto: EntityDto) {
		await answersForEventsService.delete(entityDto);
		this.answersForEvents.items = this.answersForEvents.items.filter(
			(x: GetAnswersForEventsOutput) => x.id !== entityDto.id,
		);
	}

	@action
	async get(entityDto: EntityDto) {
		const result = await answersForEventsService.get(entityDto);
		this.editAnswersForEvents = result;
	}

	@action
	async getAll(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequestAnswersForEvents,
	) {
		const result = await answersForEventsService.getAll(
			pagedFilterAndSortedRequest,
		);
		this.answersForEvents = result;
	}
}

export default AnswersForEventsStore;
