import "./index.less";

import * as React from "react";

import { Col, Row } from "antd";

import { Link } from "react-router-dom";
import { L } from "../../lib/abpUtility";

import { ArrowExit20Filled } from "@fluentui/react-icons";
import { inject, observer } from "mobx-react";
import SessionStore from "../../stores/sessionStore";
import Stores from "../../stores/storeIdentifier";

export interface IHeaderProps {
	titlePage: string;
	sessionStore?: SessionStore;
}

@inject(Stores.SessionStore)
@observer
export class Header extends React.Component<IHeaderProps> {
	render() {
		const tenancyName =
			this.props.sessionStore?.currentLogin?.tenant?.tenancyName;
		return (
			<Row
				className={"header-container"}
				style={{ alignItems: "center" }}
				justify="space-between"
			>
				<Col
					style={{
						padding: "0 16px",
					}}
					xs={{ order: 2 }}
					lg={{ span: 10 }}
					md={{ order: 1, push: 0 }}
					xl={{ span: 8, order: 1 }}
					xxl={6}
				>
					{this.props.titlePage !== "Dashboard" &&
					this.props.titlePage !== "Home" ? (
						<h2 className="titlePage" style={{ textAlign: "left", margin: 0 }}>
							{this.props.titlePage}
						</h2>
					) : (
						<h2 />
					)}
				</Col>
				<Col
					xs={{ order: 1, push: 10 }}
					md={{ order: 2, push: 0 }}
					lg={{ span: 8 }}
					xl={{ span: 6, order: 2 }}
					xxl={4}
				>
					<div
						style={{
							display: "flex",
							alignItems: "center",
							justifyContent: "flex-end",
						}}
					>
						{/* <LanguageSelect /> {'   '} */}
						<h3
							style={{
								margin: "0 15px",
								fontSize: "14px",
								fontWeight: "bold",
							}}
						>
							{tenancyName
								? `${L("CurrentTenant")}: ${tenancyName}`
								: L("SuperAdmin")}
						</h3>
						<Link to="/logout" className="logout">
							<ArrowExit20Filled
								style={{ fontSize: "20px", color: "#676767" }}
							/>
						</Link>
					</div>
				</Col>
			</Row>
		);
	}
}

export default Header;
