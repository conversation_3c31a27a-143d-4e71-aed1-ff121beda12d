import { WrappedFormUtils } from "@ant-design/compatible/lib/form/Form";
import { SettingOutlined } from "@ant-design/icons";
import {
	<PERSON>ton,
	Col,
	DatePicker,
	Dropdown,
	Menu,
	Modal,
	Radio,
	Row,
	Select,
	Table,
} from "antd";
import MenuItem from "antd/lib/menu/MenuItem";
import { ColumnProps } from "antd/lib/table";
import {
	FilterDropdownProps,
	SorterResult,
	TablePaginationConfig,
} from "antd/lib/table/interface";
import { inject, observer } from "mobx-react";
import moment, { Moment } from "moment";
import * as React from "react";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import { L, isGranted } from "../../lib/abpUtility";
import { EntityDto } from "../../services/dto/entityDto";
import { GetNotificationsOutput } from "../../services/notifications/dto/GetNotificationsOutput";
import { SingleNotification } from "../../services/notifications/dto/SingleNotification";
import NotificationsStore from "../../stores/notificationsStore";
import ShipStore from "../../stores/shipStore";
import Stores from "../../stores/storeIdentifier";
import utils from "../../utils/utils";
import { ModalType } from "../ModalConsts";
import {
	getTablePaginationOptions,
	renderDate,
	renderFilterIcon,
} from "../renderUtils";
import EditNotification from "./components/editNotification";
import FilterSelect from "../../components/FilterSelect/FilterSelect";
import { FilterByColumn } from "../../models/Sort/SortState";

export interface INotificationsProps {
	notificationsStore: NotificationsStore;
	shipStore: ShipStore;
}

export interface INotificationsState {
	maxResultCount: number;
	skipCount: number;
	fromLastDays?: number;
	shipId: number;
	notificationMapContent: SingleNotification | null;
	eventId: number;
	editModalVisible: boolean;
	okButtonDisabledEdit: boolean;
	loading: boolean;
	filters: Array<FilterByColumn>;
	fetchingFilters: boolean;
	sorters: SorterResult<SingleNotification>[];
	currentPage: number;
	activeFilters: {
		startDate?: string;
		endDate?: string;
		statusFilter?: string;
	};
}

const { Option } = Select;

type StatisticDisplay = { title: string; count: number | string };

class TextWrapper extends React.Component<StatisticDisplay> {
	render() {
		return (
			<div
				style={{
					display: "flex",
					justifyContent: "space-between",
					fontSize: 16,
					fontWeight: 600,
					textTransform: "uppercase",
					minWidth: "300px",
					alignItems: "end",
				}}
			>
				<span>{this.props.title}</span>
				<span>{this.props.count}</span>
			</div>
		);
	}
}

type PreviousState = {
	shipId: INotificationsState["shipId"];
	fromLastDays: INotificationsState["fromLastDays"];
	sorters: INotificationsState["sorters"];
	filters: INotificationsState["filters"];
	activeFilters: INotificationsState["activeFilters"];
};

const statuses = [
	"Completed by mobile",
	"Cancelled by mobile",
	"Completed by mobile",
	"Sent to mobile",
	"Received by mobile",
];

@inject(Stores.NotificationsStore)
@inject(Stores.ShipStore)
@observer
class Notifications extends AppComponentBase<
	INotificationsProps,
	INotificationsState
> {
	formRefEdit?: WrappedFormUtils;
	state: INotificationsState = {
		maxResultCount: 10,
		skipCount: 0,
		fromLastDays: 1,
		shipId: 0,
		notificationMapContent: null,
		eventId: 0,
		editModalVisible: false,
		fetchingFilters: false,
		filters: [],
		okButtonDisabledEdit: false,
		loading: false,
		currentPage: 1,
		sorters: [],
		activeFilters: {
			startDate: undefined,
			endDate: undefined,
			statusFilter: undefined,
		},
	};

	extractPreviousState(): PreviousState {
		const { allShipNames } = this.props.shipStore;
		let state: PreviousState = {
			shipId: 0,
			fromLastDays: 1,
			sorters: [],
			filters: [],
			activeFilters: {},
		};
		if (!allShipNames || allShipNames.length === 0) {
			return state;
		}

		state.shipId = allShipNames[0].id;

		const paramShipId = utils.getUrlNumericParam("shipId");
		if (
			paramShipId &&
			allShipNames.findIndex((x) => x.id === paramShipId) >= 0
		) {
			state.shipId = paramShipId;
		}

		const fromLastDaysParam = utils.getUrlNumericParam("fromLastDays");
		state.fromLastDays = fromLastDaysParam;

		const prevState = utils.getSortAndFilterFromStorage<PreviousState>(
			"notification-filters",
		);

		if (prevState)
			state = {
				...prevState,
				fromLastDays: prevState.fromLastDays
					? prevState.fromLastDays
					: undefined,
			};

		utils.removeStateFromStorage("notification-filters");

		return state;
	}

	componentWillUnmount(): void {
		const settings: PreviousState = {
			sorters: this.state.sorters,
			shipId: this.state.shipId,
			fromLastDays: this.state.fromLastDays,
			filters: this.state.filters,
			activeFilters: this.state.activeFilters,
		};

		utils.saveSortAndFilterToStorage("notification-filters", settings);
	}

	async componentDidMount() {
		await this.props.shipStore.getShipNames();

		const prevState = this.extractPreviousState();

		this.setState({ ...prevState }, async () => {
			await this.getAll();
			await this.props.notificationsStore.getTitles();
		});
	}

	async getAll() {
		this.setState({ loading: true });

		const statusSorter = this.state.sorters.find((x) =>
			statuses.includes(x.columnKey?.toString() || ""),
		);

		const [searchColumnString, keywordString] = utils.getFilterStrings([
			...this.state.filters,
		]);

		await this.props.notificationsStore.getNotificationsSummary(
			{
				"input.maxResultCount": this.state.maxResultCount,
				"input.skipCount": this.state.skipCount,
				"input.keyword": keywordString,
				"input.searchColumn": searchColumnString,
				"input.sorting": utils.getSorterString(
					this.state.sorters.filter(
						(x) => !statuses.includes(x.columnKey?.toString() || ""),
					),
				),
				statusSort: statusSorter
					? `${statusSorter.columnKey}${statusSorter.order === "descend" ? " DESC" : ""}`
					: "",
				statusFilter: this.state.activeFilters.statusFilter || "",
				filterStartDate: this.state.activeFilters.startDate || "",
				filterEndDate: this.state.activeFilters.endDate || "",
			},
			this.state.shipId,
			this.state.fromLastDays,
		);
		this.setState({ loading: false });
	}

	handleTableChange = (
		pagination: TablePaginationConfig,
		_: Partial<Record<keyof SingleNotification, string[]>>,
		sorterResult:
			| SorterResult<SingleNotification>
			| SorterResult<SingleNotification>[],
	) => {
		const sorters = utils.getSorters(sorterResult);

		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
				sorters: sorters,
				currentPage: pagination.current ?? 1,
			},
			async () => {
				await this.getAll();
			},
		);
	};

	resetFilters = () => {
		this.setState(
			{
				activeFilters: {
					startDate: undefined,
					endDate: undefined,
					statusFilter: undefined,
				},
			},
			async () => {
				await this.getAll();
			},
		);
	};

	async editModalOpen(entityDto: EntityDto) {
		if (entityDto.id !== 0) {
			await this.props.notificationsStore.get(entityDto);
		}
		this.setState({ eventId: entityDto.id });
		this.modalEdit();

		if (!this.formRefEdit) return;

		this.formRefEdit.setFieldsValue({
			...this.props.notificationsStore.editNotification,
		});
	}

	modalEdit = () => {
		this.setState({
			editModalVisible: !this.state.editModalVisible,
		});
	};

	handleEdit = () => {
		const form = this.formRefEdit;
		if (!form) return;

		form.validateFields(async (err, values) => {
			if (err) {
				return;
			}
			this.setState({ okButtonDisabledEdit: true });
			try {
				if (this.state.eventId !== 0) {
					await this.props.notificationsStore.updateCargo({
						eventId: this.state.eventId,
						...values,
					});
				}
				await this.getAll();
				form.resetFields();
				this.setModalEditVisible();
			} catch (ex) {
			} finally {
				this.setState({ okButtonDisabledEdit: false });
			}
		});
	};

	async fetchFilters(property: string) {
		this.setState({ fetchingFilters: true });
		const [searchColumnString, keywordString] = utils.getFilterStrings([
			...this.state.filters,
		]);

		try {
			await this.props.notificationsStore.getFilters(
				{
					keyword: keywordString,
					searchColumn: searchColumnString,
				},
				property,
				this.state.shipId,
				this.state.fromLastDays,
			);
		} catch {
			Modal.error({
				title: "Error during fetching",
				content: (
					<div>
						<p>Failed to fetch filters for column</p>
					</div>
				),
			});
		}

		this.setState({ fetchingFilters: false });
	}

	private handleFilter(value: string, column: string) {
		let currentFilters = [...this.state.filters];
		currentFilters = currentFilters.filter((f) => f.column !== column);

		if (value) {
			currentFilters.push({ value, column: column });
		}

		this.setState({ filters: currentFilters }, async () => {
			await this.getAll();
		});
	}

	setModalEditVisible = () => {
		this.setState({ editModalVisible: false });
	};

	// biome-ignore lint/suspicious/noExplicitAny: Poor type implementation from library
	saveFormRefEdit = (formRefEdit: any) => {
		if (!formRefEdit) return;
		this.formRefEdit = formRefEdit.props.form;
	};

	private filterStatusByDate(
		dates: [Moment | null, Moment | null] | null,
		filterKey: string,
		props: FilterDropdownProps,
	) {
		if (dates) {
			const [startDate, endDate] = dates;
			const formattedDates = [startDate?.toISOString(), endDate?.toISOString()];

			if (formattedDates[0]) {
				this.setState(
					{
						activeFilters: {
							statusFilter: filterKey,
							startDate: formattedDates[0],
							endDate: formattedDates[1],
						},
					},
					async () => this.getAll(),
				);

				props.setSelectedKeys([...new Set([...props.selectedKeys, filterKey])]);
			} else {
				this.setState(
					{
						activeFilters: {
							statusFilter: "",
							startDate: undefined,
							endDate: undefined,
						},
					},
					async () => this.getAll(),
				);
				props.setSelectedKeys(
					props.selectedKeys.filter((x) => x !== filterKey),
				);
			}
			this.forceUpdate();
		} else {
			props.setSelectedKeys(props.selectedKeys.filter((x) => x !== filterKey));
			this.resetFilters();
		}
		props.confirm?.();
	}

	getColumnSearchProps = (
		dataIndex: string,
		displayName: string,
	): ColumnProps<SingleNotification> => ({
		filterDropdown: (props: FilterDropdownProps) => (
			<div>
				{props.visible && (
					<FilterSelect
						{...props}
						loading={this.state.fetchingFilters}
						handleFilter={(value) => this.handleFilter(value, dataIndex)}
						title={displayName}
						value={
							this.state.filters.find((x) => x.column === dataIndex)?.value
						}
						options={this.props.notificationsStore.filters?.map((x) => {
							return { key: x, value: x };
						})}
					/>
				)}
			</div>
		),
		onFilterDropdownVisibleChange: (v) => {
			if (v) this.fetchFilters(dataIndex);
		},
		filterIcon: () =>
			renderFilterIcon(
				this.state.filters.findIndex((x) => x.column === dataIndex) >= 0,
			),
		sortOrder: this.state.sorters.find((x) => x.columnKey === dataIndex)?.order,
	});

	public render() {
		const { notifications } = this.props.notificationsStore;
		const { allShipNames } = this.props.shipStore;
		const paginationOptions = getTablePaginationOptions(
			notifications?.totalCount,
		);
		paginationOptions.current = this.state.currentPage;
		const { sorters } = this.state;

		const columns: Array<ColumnProps<SingleNotification>> = [
			{
				title: L("Ship Name"),
				dataIndex: "shipName",
				key: "shipName",
				width: 180,
				sorter: { multiple: 1 },
				...this.getColumnSearchProps("ship.shipName", L("Ship Name")),
			},
			{
				title: L("IMO"),
				dataIndex: "shipImo",
				key: "shipImo",
				width: 120,
				...this.getColumnSearchProps("ship.imoNumber", L("IMO")),
				sorter: { multiple: 2 },
			},
			{
				title: L("Notification Name"),
				dataIndex: "notifications",
				key: "notifications",
				width: 200,
				render: (
					text: string,
					item: GetNotificationsOutput["notifications"][0],
				) => (
					<>
						<div
							style={{
								fontSize: 16,
								fontWeight: 700,
								textTransform: "uppercase",
							}}
						>
							{item.title}
						</div>
						<div style={{ fontSize: 14 }}>
							{renderDate(item.creationTime.toString(), true)}
						</div>
						<div
							style={{
								fontSize: 14,
								fontWeight: 700,
								textTransform: "uppercase",
								color: "red",
							}}
						>
							{item.description}
						</div>
					</>
				),
				...this.getColumnSearchProps("title", L("Notification Name")),
			},
			{
				title: L("Cargo Quantity"),
				dataIndex: "cargoQuantity",
				key: "cargoQuantity",
				width: 150,
				...this.getColumnSearchProps("cargoQuantity", L("Cargo Quantity")),
				sorter: { multiple: 3 },
			},
			{
				title: L("Sent from r@s cloud"),
				dataIndex: "sent",
				key: "Sent to mobile",
				width: 220,
				sorter: true,
				sortOrder: sorters.find((x) => x.columnKey === "Sent to mobile")?.order,
				render: (text: string) => renderDate(text, true),
				filterDropdown: (props: FilterDropdownProps) => (
					<div style={{ padding: 8 }}>
						<DatePicker.RangePicker
							value={
								this.state.activeFilters.statusFilter === "Sent to mobile"
									? [
											moment(this.state.activeFilters.startDate) || null,
											moment(this.state.activeFilters.endDate) || null,
										]
									: null
							}
							style={{ marginBottom: 8, width: 200 }}
							onChange={(dates) => {
								this.filterStatusByDate(dates, "Sent to mobile", props);
							}}
						/>
					</div>
				),
				filterIcon: () =>
					renderFilterIcon(
						this.state.activeFilters.statusFilter === "Sent to mobile",
					),
			},
			{
				title: L("Received by r@s app"),
				dataIndex: "received",
				key: "Received by mobile",
				width: 220,
				sorter: true,
				sortOrder: sorters.find((x) => x.columnKey === "Received by mobile")
					?.order,
				render: (text: string) => renderDate(text, true),
				filterDropdown: (props: FilterDropdownProps) => (
					<div style={{ padding: 8 }}>
						<DatePicker.RangePicker
							value={
								this.state.activeFilters.statusFilter === "Received by mobile"
									? [
											moment(this.state.activeFilters.startDate) || null,
											moment(this.state.activeFilters.endDate) || null,
										]
									: null
							}
							style={{ marginBottom: 8, width: 200 }}
							onChange={(dates) => {
								this.filterStatusByDate(dates, "Received by mobile", props);
							}}
						/>
					</div>
				),
				filterIcon: () =>
					renderFilterIcon(
						this.state.activeFilters.statusFilter === "Received by mobile",
					),
			},
			{
				title: L("Completed by OBU"),
				dataIndex: "completed",
				key: "Completed by mobile",
				width: 220,
				sorter: true,
				sortOrder: sorters.find((x) => x.columnKey === "Completed by mobile")
					?.order,
				render: (text: string) => renderDate(text, true),
				filterDropdown: (props: FilterDropdownProps) => (
					<div style={{ padding: 8 }}>
						<DatePicker.RangePicker
							value={
								this.state.activeFilters.statusFilter === "Completed by mobile"
									? [
											moment(this.state.activeFilters.startDate) || null,
											moment(this.state.activeFilters.endDate) || null,
										]
									: null
							}
							style={{ marginBottom: 8, width: 200 }}
							onChange={(dates) => {
								this.filterStatusByDate(dates, "Completed by mobile", props);
							}}
						/>
					</div>
				),
				filterIcon: () =>
					renderFilterIcon(
						this.state.activeFilters.statusFilter === "Completed by mobile",
					),
			},
			{
				title: L("Cancelled by OBU"),
				dataIndex: "cancelled",
				key: "Cancelled by mobile",
				width: 200,
				sorter: true,
				sortOrder: sorters.find((x) => x.columnKey === "Cancelled by mobile")
					?.order,
				render: (text: string) => renderDate(text, true),
				filterDropdown: (props: FilterDropdownProps) => (
					<div style={{ padding: 8 }}>
						<DatePicker.RangePicker
							value={
								this.state.activeFilters.statusFilter === "Cancelled by mobile"
									? [
											moment(this.state.activeFilters.startDate) || null,
											moment(this.state.activeFilters.endDate) || null,
										]
									: null
							}
							style={{ marginBottom: 8, width: 200 }}
							onChange={(dates) => {
								this.filterStatusByDate(dates, "Cancelled by mobile", props);
							}}
						/>
					</div>
				),
				filterIcon: () =>
					renderFilterIcon(
						this.state.activeFilters.statusFilter === "Cancelled by mobile",
					),
			},
			{
				title: L("Actions"),
				fixed: "right" as const,
				width: 120,
				render: (text: string, item: SingleNotification) => (
					<div>
						<Dropdown
							trigger={["click"]}
							overlay={
								<Menu>
									<MenuItem
										onClick={() => this.editModalOpen({ id: item.eventId })}
									>
										{L("Edit")}
									</MenuItem>
								</Menu>
							}
							placement="bottomLeft"
						>
							<Button type="primary" icon={<SettingOutlined />}>
								{L("Actions")}
							</Button>
						</Dropdown>
					</div>
				),
			},
		];

		const hasNoActions = !isGranted("Pages.Cargo-Add");

		if (hasNoActions) {
			columns.pop();
		}

		return (
			<>
				<Row style={{ display: "flex" }}>
					<Col
						span={12}
						style={{
							display: "flex",
							flexDirection: "column",
							justifyContent: "flex-end",
						}}
					>
						<div
							style={{
								display: "flex",
								flexDirection: "column",
								marginBottom: "1rem",
							}}
						>
							<h3>Vessel</h3>
							{allShipNames && (
								<Select
									showSearch
									style={{ width: "200px", height: "min-content" }}
									value={this.state.shipId}
									onChange={async (value: number) => {
										utils.insertUrlParam("shipId", value?.toString() || "");
										this.setState({ shipId: value }, async () => {
											await Promise.all([
												this.props.notificationsStore.getTitles(),
												this.getAll(),
											]);
										});
									}}
								>
									<Option key={undefined} value={undefined}>
										All Ships
									</Option>
									{allShipNames.map((x) => (
										<Option key={x.id} value={x.id}>
											{x.shipName}
										</Option>
									))}
								</Select>
							)}
						</div>
						<Radio.Group
							onChange={(e) => {
								utils.insertUrlParam(
									"fromLastDays",
									e.target.value?.toString() || "",
								);
								this.setState(
									{
										fromLastDays: e.target.value,
										skipCount: 0,
										currentPage: 1,
									},
									() => {
										this.getAll();
									},
								);
							}}
							value={this.state.fromLastDays}
							buttonStyle="solid"
							size="large"
						>
							<Radio.Button value={1}>24h</Radio.Button>
							<Radio.Button value={7}>7d</Radio.Button>
							<Radio.Button value={undefined}>All</Radio.Button>
						</Radio.Group>
					</Col>
					<Col span={4} />
					<Col span={7}>
						<div style={{ display: "flex", flexDirection: "column" }}>
							<TextWrapper
								title="INITIATED ON r@s APP"
								count={notifications?.sentByMobile}
							/>
							<TextWrapper
								title="INITIATED FROM r@s CLOUD"
								count={notifications?.sentFromServer}
							/>
							<TextWrapper
								title="RECEIVED ON r@s APP FROM CLOUD"
								count={notifications?.received}
							/>
							<TextWrapper
								title="CANCELLED ON r@s APP"
								count={notifications?.cancelled}
							/>
							<TextWrapper
								title="COMPLETED BY OBU"
								count={notifications?.done}
							/>
							<div style={{ height: "8px" }} />
							<TextWrapper
								title="% COMPLETED NOTIFICATIONS"
								count={`${notifications?.donePercent || 0}%`}
							/>
						</div>
					</Col>
					<Col span={1} />
				</Row>
				<Row style={{ marginTop: 20 }}>
					<Col span={24}>
						<Table
							rowKey={(record: GetNotificationsOutput["notifications"][0]) =>
								record.guid.toString()
							}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={this.state.loading}
							dataSource={
								notifications === undefined ? [] : notifications.notifications
							}
							onChange={this.handleTableChange}
							scroll={{ y: 700 }}
						/>
					</Col>
				</Row>
				<EditNotification
					notificationsStore={this.props.notificationsStore}
					wrappedComponentRef={this.saveFormRefEdit}
					visible={this.state.editModalVisible}
					modalType={ModalType.edit}
					okButtonDisabled={this.state.okButtonDisabledEdit}
					roles={this.props.notificationsStore.roles}
					onCreate={this.handleEdit}
					onCancel={this.setModalEditVisible}
				/>
				<Chat />
			</>
		);
	}
}

export default Notifications;
