import * as React from "react";
import "./index.less";
import AppComponentBase from "../../components/AppComponentBase";
import Logo from "../../images/LOGO.svg";

export type IHomeProps = {};

export type IHomeState = {};

export class Home extends AppComponentBase<IHomeProps, IHomeState> {
	async componentDidMount() {}

	state: IHomeState = {};

	render() {
		return (
			<React.Fragment>
				<div
					style={{
						display: "grid",
						placeItems: "center",
						height: "100%",
						zIndex: 1,
					}}
				>
					<div className="title">
						<h1
							style={{
								fontSize: "3rem",
								fontWeight: "bold",
								color: "#454545",
								margin: "0",
								verticalAlign: "top",
							}}
						>
							WELCOME TO
						</h1>
						<img
							style={{ width: "200px", height: "42px", marginBottom: "-8px" }}
							src={Logo}
						/>
					</div>
				</div>
			</React.Fragment>
		);
	}
}

export default Home;
