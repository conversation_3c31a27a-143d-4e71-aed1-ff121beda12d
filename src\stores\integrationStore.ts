import { action, observable } from "mobx";
import type { PagedFilterAndSortedRequest } from "../services/dto/pagedFilterAndSortedRequest";
import type { PagedResultDto } from "../services/dto/pagedResultDto";
import { GetIntegrationOutput } from "../services/integrations/dto/getIntegrationOutput";
import integrationService from "../services/integrations/integrationService";

class IntegrationStore {
	@observable integrations!: PagedResultDto<GetIntegrationOutput>;

	@action
	async getAll(pagedFilterAndSortedRequest: PagedFilterAndSortedRequest) {
		const result = await integrationService.getAll(pagedFilterAndSortedRequest);
		this.integrations = result;
	}
}

export default IntegrationStore;
