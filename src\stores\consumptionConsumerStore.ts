import { action, observable } from "mobx";
import {
	createPascalCaseText,
	normalizeCamelCaseText,
} from "../scenes/renderUtils";
import consumptionConsumerService from "../services/consumptionConsumer/consumptionConsumerService";
import type { ConsumptionConsumerDto } from "../services/consumptionConsumer/dto/consumptionConsumerDto";
import { EntityDto } from "../services/dto/entityDto";
import type { PagedFilterAndSortedRequest } from "../services/dto/pagedFilterAndSortedRequest";
import type { PagedResultDto } from "../services/dto/pagedResultDto";
import meterService from "../services/meter/meterService";
import { GetShipOutput } from "../services/ship/dto/getShipOutput";
import { GetRoles } from "../services/user/dto/getRolesOuput";
import MeterStore from "./meterStore";
import ShipStore from "./shipStore";

class ConsumptionConsumerStore {
	@observable consumptionConsumers!: PagedResultDto<ConsumptionConsumerDto>;
	@observable editConsumptionConsumer!: ConsumptionConsumerDto;
	@observable meterTypeCurrent!: string;
	@observable meterType!: { [key: string]: string };
	@observable measureUnits!: { [key: number]: string };
	@observable roles: GetRoles[] = [];
	@observable allShips!: GetShipOutput[];
	@observable allConsumerNames!: [{ name: string; id: number }];
	@observable actualConsumerTypes!: string[];
	@observable currentConsumerType!: string;
	@observable updateMode!: boolean;

	shipStore: ShipStore = new ShipStore();
	meterStore: MeterStore = new MeterStore();

	@action
	async create(createConsumptionConsumerInput: ConsumptionConsumerDto) {
		const result = await consumptionConsumerService.create(
			createConsumptionConsumerInput,
		);
		this.consumptionConsumers.items.push(result);
	}

	@action
	async update(updateConsumptionConsumerInput: ConsumptionConsumerDto) {
		const result = await consumptionConsumerService.update(
			updateConsumptionConsumerInput,
		);
		this.consumptionConsumers.items = this.consumptionConsumers.items.map(
			(x: ConsumptionConsumerDto) => {
				if (x.id === updateConsumptionConsumerInput.id) x = result;
				return x;
			},
		);
	}

	@action
	async getNames(shipId?: number) {
		const result = await consumptionConsumerService.getNames(shipId);
		this.allConsumerNames = result;
	}

	public emptyMetersCheck(ConsumptionConsumer: ConsumptionConsumerDto) {
		if (ConsumptionConsumer.metersMinus === null) {
			ConsumptionConsumer.metersMinus = [];
		}
		if (ConsumptionConsumer.metersPlus === null) {
			ConsumptionConsumer.metersPlus = [];
		}
		return ConsumptionConsumer;
	}

	@action
	async delete(entityDto: EntityDto) {
		await consumptionConsumerService.delete(entityDto);
		this.consumptionConsumers.items = this.consumptionConsumers.items.filter(
			(x: ConsumptionConsumerDto) => x.id !== entityDto.id,
		);
	}

	@action
	async get(entityDto: EntityDto) {
		this.updateMode = true;
		const result = await consumptionConsumerService.get(entityDto);
		this.editConsumptionConsumer = result;
	}

	@action
	async getConsumptionConsumerByShip(shipId: number) {
		const result =
			await consumptionConsumerService.getConsumptionConsumerByShip(shipId);
		this.consumptionConsumers = result;
	}

	@action
	async getAll(pagedFilterAndSortedRequest: PagedFilterAndSortedRequest) {
		const result = await consumptionConsumerService.getAll(
			pagedFilterAndSortedRequest,
		);
		this.consumptionConsumers = result;
	}

	@action
	async createConsumptionConsumer() {
		this.updateMode = false;
		this.editConsumptionConsumer = {
			name: "",
			metersMinus: [],
			metersPlus: [],
			uuid: "",
			id: 0,
			isActive: false,
			isAutoCalculation: false,
			ship: {
				shipName: "",
			},
			meterType: this.meterTypeCurrent,
			shipId: 0,
			additionalScanHours: [],
		};
		this.roles = [];
	}

	@action
	async getShips() {
		const result = await meterService.getShips();
		this.allShips = result.items;
	}

	@action
	async addActualConsumerTypes(actualConsumerTypes: string[]) {
		this.actualConsumerTypes = actualConsumerTypes;
	}

	@action
	async setActualConsumerTypes() {
		const allConsumerTypes: string[] = Object.values("");
		const alreadyUsedConsumerTypes: string[] =
			this.consumptionConsumers.items.map((x) => x.meterType);
		if (this.updateMode === true) {
			for (let i = 0; i < alreadyUsedConsumerTypes.length; i++) {
				if (
					alreadyUsedConsumerTypes[i] ===
					createPascalCaseText(this.editConsumptionConsumer?.meterType)
				) {
					alreadyUsedConsumerTypes.splice(i, 1);
				}
			}
		}
		const filteredConsumerTypes: string[] = allConsumerTypes.filter(
			(x) => !alreadyUsedConsumerTypes.includes(x),
		);
		this.addActualConsumerTypes(filteredConsumerTypes);
	}

	@action
	async getCurrentConsumerType() {
		await this.setActualConsumerTypes();
		if (this.actualConsumerTypes != null) {
			if (this.updateMode === true) {
				this.currentConsumerType = normalizeCamelCaseText(
					this.editConsumptionConsumer?.meterType,
				);
			} else {
				this.currentConsumerType = normalizeCamelCaseText(
					this.actualConsumerTypes[0],
				);
			}
		} else {
			this.currentConsumerType = "";
		}
	}

	@action
	async getConsumerTypes() {
		const result = await consumptionConsumerService.getConsumerTypes();
		return result;
	}

	@action
	async getMeasureUnits() {
		const result = await consumptionConsumerService.getMeasureUnits();
		this.measureUnits = result;
	}
}

export default ConsumptionConsumerStore;
