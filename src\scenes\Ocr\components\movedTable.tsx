import { Table } from "antd";
import { inject, observer } from "mobx-react";
import moment from "moment";
import React from "react";
import { L } from "../../../lib/abpUtility";
import { OcrDto } from "../../../services/ocr/dTo/ocrDto";
import OcrStore from "../../../stores/ocrStore";
import Stores from "../../../stores/storeIdentifier";
import { renderDate } from "../../renderUtils";

interface IVerifyOcrTableProps {
	ocrStore: OcrStore;
	ocr: any;
}

@inject(Stores.OcrStore)
@observer
class MovedTable extends React.Component<IVerifyOcrTableProps> {
	ocrStore: OcrStore = this.props.ocrStore;

	state = {
		selectedFiles: [],
		selectedRows: {
			items: [],
		},
		status: 0,
	};

	constructor(props: any) {
		super(props);
		this.state = {
			selectedFiles: [],
			selectedRows: {
				items: [],
			},
			status: 0,
		};
	}

	public render() {
		const columns = [
			{
				title: L("CreationTime"),
				dataIndex: "creationTime",
				key: "creationTime",
				width: 150,
				sorter: (a: OcrDto, b: OcrDto) =>
					moment(a.creationTime).diff(moment(b.creationTime)),
				render: (text: string, rekord: OcrDto, index: number) =>
					renderDate(text),
			},
			{
				title: L("LastModificationTime"),
				dataIndex: "lastModificationTime",
				key: "lastModificationTime",
				width: 150,
				sorter: (a: OcrDto, b: OcrDto) =>
					moment(a.lastModificationTime).diff(moment(b.lastModificationTime)),
				render: (text: string, rekord: OcrDto, index: number) =>
					renderDate(text),
			},
			{
				title: L("FileName"),
				dataIndex: "fileName",
				key: "fileName",
				width: 150,
			},
			{
				title: L("Status"),
				dataIndex: "status",
				key: "status",
				width: 150,
			},
		];

		return (
			<Table
				bordered={true}
				rowKey={(record: OcrDto) => record.id.toString()}
				columns={columns}
				dataSource={this.props.ocr ? this.props.ocr : []}
			/>
		);
	}
}

export default MovedTable;
