import Endpoint from "../endpoints";
import http from "../httpService";
import { ShipTraceCammelCase } from "../notifications/dto/ShipTrace";
import { ShipPositionDto } from "./dto/ShipPositionDto";

class MapService {
	public async getShipPositions(): Promise<ShipPositionDto[]> {
		const result = await http.get(Endpoint.ReadAtSeaInfo.GetShipLastPositions);
		return result.data.result;
	}

	public async getShipPosition(shipId: number): Promise<ShipTraceCammelCase[]> {
		const result = await http.get(Endpoint.ReadAtSeaInfo.GetShipLastPosition, {
			params: { shipId },
		});
		return result.data.result;
	}
}

export default new MapService();
