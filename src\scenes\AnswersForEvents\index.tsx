import { SearchOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Card, Col, Input, Row } from "antd";
import Table, { ColumnProps, TablePaginationConfig } from "antd/lib/table";
import { TableRowSelection } from "antd/lib/table/interface";
import { inject, observer } from "mobx-react";
import * as React from "react";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import { L } from "../../lib/abpUtility";
import { CreateOrUpdateAnswersForEventsInput } from "../../services/answersForEvents/dto/CreateOrUpdateAnswersForEventsInput";
import { GetAnswersForEventsOutput } from "../../services/answersForEvents/dto/GetAnswersForEventsOutput";
import { EntityDto } from "../../services/dto/entityDto";
import { ListResultDto } from "../../services/dto/pagedResultDto";
import AnswersForEventsStore from "../../stores/answersForEventsStore";
import Stores from "../../stores/storeIdentifier";
import { getTablePaginationOptions, renderSearchIcon } from "../renderUtils";

export interface IAnswersForEventsProps {
	answersForEventsStore: AnswersForEventsStore;
}

export interface IAnswersForEventsState {
	maxResultCount: number;
	skipCount: number;
	sorting: string;
	searchTextInsideTable: Array<{ index: string; searchText: string }>;
	searchTextTable: string;
	searchedColumnTable: string;
	selectedRows: ListResultDto<EntityDto>;
}

@inject(Stores.AnswersForEventsStore)
@observer
class AnswersForEvents extends AppComponentBase<
	IAnswersForEventsProps,
	IAnswersForEventsState
> {
	state = {
		maxResultCount: 10,
		skipCount: 0,
		sorting: "",
		searchTextInsideTable: [{ index: "", searchText: "" }],
		searchTextTable: "",
		searchedColumnTable: "",
		selectedRows: {
			items: [],
		},
	};

	async componentDidMount() {
		await this.getAll();
	}

	async getAll() {
		await this.props.answersForEventsStore.getAll({
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: this.state.sorting,
		});
	}

	getColumnsSearchProps = (
		dataIndex: keyof GetAnswersForEventsOutput,
	): ColumnProps<GetAnswersForEventsOutput> => ({
		filterDropdown: ({
			setSelectedKeys,
			selectedKeys,
			confirm,
			clearFilters,
		}) => {
			return (
				<div style={{ padding: 8 }}>
					<Input
						autoFocus
						placeholder={`Search ${dataIndex}`}
						value={selectedKeys ? selectedKeys[0] : ""}
						onChange={(e) => {
							if (setSelectedKeys)
								setSelectedKeys(e.target.value ? [e.target.value] : []);
						}}
						onPressEnter={() =>
							this.handleSearchTable(selectedKeys || [], dataIndex, confirm)
						}
						style={{ width: 188, marginBottom: 8, display: "block" }}
					/>
					<Button
						type="primary"
						onClick={() =>
							this.handleSearchTable(selectedKeys || [], dataIndex, confirm)
						}
						icon={<SearchOutlined />}
						size="small"
						style={{ width: 90, marginRight: 8 }}
					>
						Search
					</Button>
					<Button
						onClick={() => this.handleReset(dataIndex, clearFilters)}
						size="small"
						style={{ width: 90 }}
					>
						Reset
					</Button>
				</div>
			);
		},
		filterIcon: renderSearchIcon,
		onFilter: (value, record) => {
			return record[dataIndex] != null && record[dataIndex] !== "";
		},
	});

	handleTableChange = (pagination: TablePaginationConfig) => {
		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
			},
			async () => await this.getAll(),
		);
	};

	handleSearchTable = (
		selectedKeys: React.Key[],
		dataIndex: string,
		confirm?: () => void,
	) => {
		if (dataIndex === "title") {
			this.setState(
				{ sorting: selectedKeys[0].toString() },
				async () => await this.getAll(),
			);
			return;
		}
		const allSearches = this.state.searchTextInsideTable;
		const filtred = allSearches.filter((x) => x.index !== dataIndex);

		filtred.push({ index: dataIndex, searchText: selectedKeys[0].toString() });
		if (confirm) confirm();
		this.setState({
			searchTextTable: selectedKeys[0].toString(),
			searchedColumnTable: dataIndex,
			searchTextInsideTable: filtred,
		});
	};

	handleReset = (dataIndex: string, clearFilters?: () => void) => {
		const allSearches = this.state.searchTextInsideTable;
		const filtred = allSearches.filter((x) => x.index !== dataIndex);
		if (clearFilters) clearFilters();
		this.setState({
			searchTextTable: "",
			searchTextInsideTable: filtred,
		});
	};

	public render() {
		const { answersForEvents } = this.props.answersForEventsStore;
		const paginationOptions = getTablePaginationOptions(
			answersForEvents?.totalCount,
		);
		const columns = [
			{
				title: L("Guid"),
				dataIndex: "guid",
				key: "guid",
				width: 150,
				sorter: (a: GetAnswersForEventsOutput, b: GetAnswersForEventsOutput) =>
					a.guid?.length - b.guid?.length,
				...this.getColumnsSearchProps("guid"),
			},
			{
				title: L("Event Id"),
				dataIndex: "eventId",
				key: "eventId",
				width: 150,
				sorter: (a: GetAnswersForEventsOutput, b: GetAnswersForEventsOutput) =>
					a.eventId - b.eventId,
				...this.getColumnsSearchProps("eventId"),
			},
			{
				title: L("Device Id"),
				dataIndex: "deviceId",
				key: "deviceId",
				width: 150,
				sorter: (a: GetAnswersForEventsOutput, b: GetAnswersForEventsOutput) =>
					a.deviceId - b.deviceId,
				...this.getColumnsSearchProps("deviceId"),
			},
			{
				title: L("Ship Id"),
				dataIndex: "shipId",
				key: "shipId",
				width: 150,
				sorter: (a: GetAnswersForEventsOutput, b: GetAnswersForEventsOutput) =>
					a.shipId - b.shipId,
				...this.getColumnsSearchProps("shipId"),
			},
			{
				title: L("Message"),
				dataIndex: "message",
				key: "message",
				width: 150,
				sorter: (a: GetAnswersForEventsOutput, b: GetAnswersForEventsOutput) =>
					a.message?.length - b.message?.length,
				...this.getColumnsSearchProps("message"),
			},
			{
				title: L("Payload"),
				dataIndex: "payload",
				key: "payload",
				width: 1000,
				sorter: (a: GetAnswersForEventsOutput, b: GetAnswersForEventsOutput) =>
					a.payload?.length - b.payload?.length,
				...this.getColumnsSearchProps("payload"),
			},
		];

		const rowSelection: TableRowSelection<GetAnswersForEventsOutput> = {
			onChange: (
				selectedRowKeys,
				selectedRows: GetAnswersForEventsOutput[],
			) => {
				const selectedRowsItems: EntityDto[] = selectedRows.map((x) => {
					return {
						id: x.id,
					};
				});
				this.setState({
					selectedRows: {
						items: selectedRowsItems,
					},
				});
			},
		};

		return (
			<Card>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey={(record: CreateOrUpdateAnswersForEventsInput) =>
								record.id.toString()
							}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={answersForEvents === undefined}
							dataSource={
								answersForEvents === undefined ? [] : answersForEvents.items
							}
							onChange={this.handleTableChange}
							rowSelection={rowSelection}
							scroll={{ x: true }}
						/>
					</Col>
				</Row>
				<Chat />
			</Card>
		);
	}
}

export default AnswersForEvents;
