import { EntityDto } from "../dto/entityDto";
import { PagedFilterAndSortedRequest } from "../dto/pagedFilterAndSortedRequest";
import { ListResultDto, PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoints";
import httpCsv from "../httpCsvService";
import http from "../httpService";
import { DraftDto } from "./dto/draftDto";

class DraftService {
	public async delete(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.Draft.Delete, {
			params: entityDto,
		});
		return result.data;
	}

	public async get(entityDto: EntityDto): Promise<DraftDto> {
		const result = await http.get(Endpoint.Draft.Get, { params: entityDto });
		return result.data.result;
	}

	public async getAll(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequest,
	): Promise<PagedResultDto<DraftDto>> {
		const result = await http.get(Endpoint.Draft.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}

	public async resendToGs(entities: ListResultDto<EntityDto>) {
		const result = await http.post(Endpoint.Draft.ReSendToGs, entities);
		return result.data.result;
	}

	public async downloadCsv(entities: ListResultDto<EntityDto>) {
		const result = await httpCsv.post(Endpoint.Draft.SendCsv, entities);
		return result.data.result;
	}

	public async downloadAllCsv(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequest,
	) {
		const result = await httpCsv.post(
			Endpoint.Draft.SendAllCsv,
			pagedFilterAndSortedRequest,
		);
		return result.data.result;
	}
}

export default new DraftService();
