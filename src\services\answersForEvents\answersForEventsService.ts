import { EntityDto } from "../dto/entityDto";
import { PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoints";
import http from "../httpService";
import { CreateOrUpdateAnswersForEventsInput } from "./dto/CreateOrUpdateAnswersForEventsInput";
import { PagedFilterAndSortedRequestAnswersForEvents } from "./dto/PagedFilterAndSortedRequestAnswersForEvents";
import { UpdateAnswersForEventsInput } from "./dto/UpdateAnswersForEventsInput";

class AnswersForEventsService {
	public async create(
		createAnswersForEventsInput: CreateOrUpdateAnswersForEventsInput,
	) {
		const result = await http.post(
			Endpoint.AnswerForEvents.Create,
			createAnswersForEventsInput,
		);
		return result.data.result;
	}

	public async update(updateAnswersForEvents: UpdateAnswersForEventsInput) {
		const result = await http.put(
			Endpoint.AnswerForEvents.Update,
			updateAnswersForEvents,
		);
		return result.data.result;
	}

	public async delete(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.AnswerForEvents.Delete, {
			params: entityDto,
		});
		return result.data;
	}

	public async get(
		entityDto: EntityDto,
	): Promise<CreateOrUpdateAnswersForEventsInput> {
		const result = await http.get(Endpoint.AnswerForEvents.Get, {
			params: entityDto,
		});
		return result.data.result;
	}

	public async getAll(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequestAnswersForEvents,
	): Promise<PagedResultDto<CreateOrUpdateAnswersForEventsInput>> {
		const result = await http.get(Endpoint.AnswerForEvents.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}
}

export default new AnswersForEventsService();
