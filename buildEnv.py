import json
import os

lines = ''
with open('.env', 'r') as file:
    lines = file.read().splitlines()


config = dict()
for line in lines:
    splitLine = line.split('=')
    
    varName = splitLine[0]
    varValue = splitLine[1]
    
    if varName in os.environ:
        config[varName]=os.environ[varName]
    else:
        config[varName]=varValue

configPath = ''
filename = 'env-config.js'
if(os.path.exists('./public')):
    configPath='./public/'+filename
else: 
    configPath=filename

with open(configPath, 'w') as file:
    file.write("window._env_ = " + json.dumps(config))
