import { ValidationRule } from "@ant-design/compatible/lib/form";
import { L } from "../../lib/abpUtility";

const rules = {
	name: [
		{ required: true, message: L("PleaseSelectConsumer"), whitespace: true },
	] as ValidationRule[],
	voaygeId: [
		{ required: true, message: L("PleaseSelectVoyage") },
	] as ValidationRule[],
	// metersPlusMinus: (required: boolean) => [{ required: required, message: L('PleaseSelectMeterPlusOrMinus') }],
	from: (required: boolean) =>
		[
			{
				required: required,
				message: L("PleaseInputDate"),
				type: "object",
				whitespace: true,
			},
		] as ValidationRule[],
	to: [
		{
			required: true,
			message: L("PleaseInputTo"),
			type: "array",
			whitespace: true,
		},
	] as ValidationRule[],
	groupingPeriodInMinutes: [
		{
			required: true,
			message: L("PleaseInputNumber"),
			type: "number",
			min: 1,
			max: 90,
		},
	] as ValidationRule[],
};

export default rules;
