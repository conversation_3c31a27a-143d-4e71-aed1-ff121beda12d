import { AuditedEntityDto } from "../../dto/auditedEntityDto";

export interface GetAllDeviceOutput extends AuditedEntityDto {
	rank: string;
	shipName: string;
	shipImo: string;
	activationCode: number;
	codeUsed: boolean;
	codeCreatedAt: Date;
	buildNumber: number;
	status: string;
	ship: {
		shipName: string;
	};
	user: {
		rank: string;
		emailAddress: string;
		creationTime: Date;
		id: number;
		name: string;
		isActive: boolean;
		surname: string;
	};
}
