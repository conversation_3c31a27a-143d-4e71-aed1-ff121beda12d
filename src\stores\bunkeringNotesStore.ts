import { action, observable } from 'mobx';
import moment from 'moment';
import bunkeringNotesService from '../services/bunkeringNotes/bunkeringNotesService';
import type { CreateOrUpdateBunkeringNotesInput } from '../services/bunkeringNotes/dto/CreateOrUpdateBunkeringNotesInput';
import { GetBunkeringNotesOutput } from '../services/bunkeringNotes/dto/GetBunkeringNotesOutput';
import type { PagedFilterAndSortedRequestBunkeringNotes } from '../services/bunkeringNotes/dto/PagedFilterAndSortedRequestBunkeringNotes';
import type { UpdateBunkeringNotesInput } from '../services/bunkeringNotes/dto/UpdateBunkeringNotesInput';
import { EntityDto } from '../services/dto/entityDto';
import type { PagedResultDto } from '../services/dto/pagedResultDto';
import { GetRoles } from '../services/user/dto/getRolesOuput';
class BunkeringNotesStore {
  @observable bunkeringNotes!: PagedResultDto<GetBunkeringNotesOutput>;
  @observable editBunkeringNotes!: CreateOrUpdateBunkeringNotesInput;
  @observable fuelTypeLoaded!: { [key: string]: string };
  @observable roles: GetRoles[] = [];
  @observable filters: string[] = [];

  @action
  async create(createOrUpdateShipInput: CreateOrUpdateBunkeringNotesInput) {
    const result = await bunkeringNotesService.create(createOrUpdateShipInput);
    this.bunkeringNotes.items.push(result);
  }
  @action
  async update(updateBunkeringNotesInput: UpdateBunkeringNotesInput) {
    const result = await bunkeringNotesService.update(updateBunkeringNotesInput);
    this.bunkeringNotes.items = this.bunkeringNotes.items.map((x: GetBunkeringNotesOutput) => {
      if (x.id === updateBunkeringNotesInput.id) x = result;
      return x;
    });
  }
  @action
  async delete(entityDto: EntityDto) {
    await bunkeringNotesService.delete(entityDto);
    this.bunkeringNotes.items = this.bunkeringNotes.items.filter((x: GetBunkeringNotesOutput) => x.id !== entityDto.id);
  }
  @action
  async get(entityDto: EntityDto) {
    const result = await bunkeringNotesService.get(entityDto);
    this.editBunkeringNotes = result;
  }
  @action
  async getAll(pagedFilterAndSortedRequest: PagedFilterAndSortedRequestBunkeringNotes) {
    const result = await bunkeringNotesService.getAll(pagedFilterAndSortedRequest);
    this.bunkeringNotes = result;
  }

  @action
  async getFilters(filters: { keyword: string; searchColumn: string }, property: string) {
    const result = await bunkeringNotesService.getFilters(filters, property);

    this.filters = result;
  }

  @action
  async createBunkeringNotes() {
    this.editBunkeringNotes = {
      portOfBunkering: { id: 0, name: '' },
      creationTime: new Date(),
      fuelTypeLoaded: { id: 0 },
      quantityLoaded: '',
      density: '',
      date: moment(),
      loadedInto: '',
      id: 0,
      supplier: '',
      fileUrls: [],
      co2Factor: 0,
      fuelLCV: 0,
      portName: '',
      type: '',
    };
  }
}
export default BunkeringNotesStore;
