import { Form } from "@ant-design/compatible";
import "@ant-design/compatible/assets/index.css";
import { Input, InputNumber, Modal } from "antd";
import FormItem from "antd/lib/form/FormItem";
import * as React from "react";
import AppComponentBase from "../../../components/AppComponentBase";
import ComponentLayout from "../../../components/Layout/ComponentLayout";
import { L } from "../../../lib/abpUtility";
import MeterReadingStore from "../../../stores/meterReadingStore";
import { FormWidths } from "../../ViewSettingsConsts";
import { ModalFormComponentProps } from "../../modalFormComponentProps";
import rules from "./createOrUpdateMeterReading.validation";
import ReadingStatus from "./ReadingStatus";

export interface Rect {
	startX: number;
	startY: number;
	w?: number;
	h?: number;
}

export interface IEditMeterReadingState {
	rectangle: Rect;
	drag: boolean;
	setup: boolean;
	scaled: boolean;
	scale: number;
}

export interface IEditMeterReadingProps extends ModalFormComponentProps {
	meterReadingsStore: MeterReadingStore;
	loading: boolean;
	onSubmit: (rect?: Rect) => void;
}

class EditMeterReading extends AppComponentBase<
	IEditMeterReadingProps,
	IEditMeterReadingState
> {
	canvasRef = React.createRef<HTMLCanvasElement>();
	imgRef = React.createRef<HTMLImageElement>();
	state = {
		rectangle: {} as Rect,
		drag: false,
		setup: false,
		scaled: false,
		scale: 1,
	};

	render() {
		const { getFieldDecorator, getFieldsError } = this.props.form;
		const { visible, onCancel, onSubmit, okButtonDisabled } = this.props;
		const { rectangle, scale } = this.state;
		const { editMeterReader } = this.props.meterReadingsStore;

		return (
			<Modal
				visible={visible}
				cancelText={L("Cancel")}
				okText={L("Save")}
				onCancel={onCancel}
				onOk={() => {
					if (rectangle.startX === undefined && rectangle.startY === undefined)
						return onSubmit(undefined);

					onSubmit(
						rectangle
							? {
									startX: rectangle.startX * scale,
									startY: rectangle.startY * scale,
									w: (rectangle.w || 0) * scale,
									h: (rectangle.h || 0) * scale,
								}
							: undefined,
					);
				}}
				title={L("Meter Reading")}
				okButtonProps={{
					disabled:
						okButtonDisabled ||
						(getFieldsError().rollover !== undefined &&
							// biome-ignore lint/style/noNonNullAssertion: compilator doesn't get the first statment
							getFieldsError().rollover!.length > 0),
					loading: this.props.loading,
				}}
			>
				<Form layout="vertical">
					<FormItem
						style={{ marginLeft: "25px" }}
						label={L("Meter Reading")}
						{...ComponentLayout.formItemLayout}
					>
						{getFieldDecorator("editValue")(
							<Input style={{ width: FormWidths.wide }} />,
						)}
					</FormItem>
					<FormItem
						style={{ marginLeft: "25px" }}
						label={L("AI Value")}
						{...ComponentLayout.formItemLayout}
					>
						{getFieldDecorator("aiValue")(
							<Input style={{ width: FormWidths.wide }} disabled />,
						)}
					</FormItem>
					<FormItem
						style={{ marginLeft: "25px" }}
						label={L("Rollover adjust")}
						{...ComponentLayout.formItemLayout}
					>
						{getFieldDecorator("rollover", { rules: rules.rollover })(
							<InputNumber style={{ width: FormWidths.wide }} />,
						)}
					</FormItem>
					<FormItem
						style={{ marginLeft: "25px" }}
						label={L("Verification status")}
						{...ComponentLayout.formItemLayout}
					>
						<ReadingStatus verified={editMeterReader?.auditMetrics.checked} />
					</FormItem>
					<figure
						className="pdfContainer"
						id="pdfContainer"
						style={{ display: "flex", justifyContent: "center" }}
					>
						<img
							srcSet={`${editMeterReader?.fileUrl} ${this.state.scale}x`}
							ref={this.imgRef}
							alt="Control Image"
						/>
					</figure>
				</Form>
			</Modal>
		);
	}
}

export default Form.create<IEditMeterReadingProps>()(EditMeterReading);
