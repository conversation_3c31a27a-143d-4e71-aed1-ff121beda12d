import { ValidationRule } from "@ant-design/compatible/lib/form";
import { L } from "../../../lib/abpUtility";

const rules: Record<string, ValidationRule[]> = {
	shipName: [{ required: true, message: L("Please Input Ship Name") }],
	imoNumber: [
		{ required: true, message: L("Please Input IMO Number") },
		{
			pattern: /^\d+$/,
			message: L("Imo number must only contain numbers"),
		},
	],
	shipEmail: [
		{ required: true, message: L("Please Input Ship Email") },
		{
			pattern: /^\S+@\S+\.\S+$/,
			message: L("Please input valid email"),
		},
	],
	mmsi: [
		{ required: true, message: L("Please Input MMSI") },
		{
			pattern: /^\d+$/,
			message: L("MMSI must only contain numbers"),
		},
		{
			len: 9,
			message: L("MMSI must be 9-digit long"),
		},
	],
	type: [{ required: true, message: L("Please select Vessel Type") }],
	cargo: [{ required: true, message: L("Please select Cargo Unit") }],
	sDWT: [{ required: true, message: L("Please input Vessel SDWT") }],
	gT: [{ required: true, message: L("Please input Vessel GT") }],
	captainName: [{ required: true, message: L("Please input captain name") }],
	chiefEngineersName: [
		{ required: true, message: L("Please input chief engienners name") },
	],
	engineRoomUnmannedHoursFrom: [
		{
			required: true,
			message: L("Please input engine room unmanned hours from"),
		},
	],
	engineRoomUnmannedHoursTo: [
		{
			required: true,
			message: L("Please input engine room unmanned hours to"),
		},
	],
	scrubber: [{ required: true, message: L("Please select scrubber") }],
};

export default rules;
