.ship-info{
    &-header{
        display: grid;
        grid-template-columns: 0.55fr auto;
        grid-template-rows: 0.8fr 1fr;
        gap: 0.5rem 1rem;
        margin-bottom: 0.5rem;

        &__buttons{
            display: flex;
            justify-content: space-between;

            &__container{
                display: flex;
                flex-direction: column;

                &--reverse{
                    flex-direction: column-reverse;
                }

                & > button{
                    margin-bottom: 0.5rem;
                }
            }
        }
    }
}

.customTableShipInfo .ant-table-scroll table:first-of-type {
    margin-bottom: 20px;
}
