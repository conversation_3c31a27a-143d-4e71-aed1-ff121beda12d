import { action, observable } from "mobx";

import accountService from "../services/account/accountService";
import IsTenantAvaibleOutput from "../services/account/dto/isTenantAvailableOutput";
import { RequestResetPasswordInput } from "../services/account/dto/requestResetPasswordInput";
import { ResetPasswordInput } from "../services/account/dto/resetPasswordInput";

class AccountStore {
	@observable tenant: IsTenantAvaibleOutput = new IsTenantAvaibleOutput();

	@action
	public isTenantAvailable = async (tenancyName: string) => {
		this.tenant = await accountService.isTenantAvailable({
			tenancyName: tenancyName,
		});
	};

	@action
	public requestResetPassword = async (email: RequestResetPasswordInput) => {
		await accountService.requestResetPassword(email);
	};

	@action
	public resetPassword = async (input: ResetPasswordInput) => {
		await accountService.resetPassword(input);
	};
}

export default AccountStore;
