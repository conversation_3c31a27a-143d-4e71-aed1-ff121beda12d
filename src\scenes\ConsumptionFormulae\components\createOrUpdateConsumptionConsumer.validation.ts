import { ValidationRule } from "@ant-design/compatible/lib/form";
import { L } from "../../../lib/abpUtility";

const rules: Record<string, ValidationRule[]> = {
	formulaeName: [
		{ required: true, message: L("Please input name of formula") },
	],
	shipName: [{ required: true, message: L("Please select Ship") }],
	meterType: [{ required: true, message: L("Please select Meter Type") }],
	additionalScanHours: [{ required: true, message: L("Please select Hours") }],
};

export default rules;
