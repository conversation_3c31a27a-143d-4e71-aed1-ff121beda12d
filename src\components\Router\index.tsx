import * as React from "react";

import { Route, Switch } from "react-router-dom";

import utils from "../../utils/utils";
import ProtectedRoute from "./ProtectedRoute";

const Router = () => {
	const UserLayout = utils.getRoute("/user").component;
	const AppLayout = utils.getRoute("/").component;

	return (
		<Switch>
			<Route path="/user" render={(props) => <UserLayout {...props} />} />
			<Route path="/account" render={(props) => <UserLayout {...props} />} />
			<ProtectedRoute
				path="/"
				render={(props) => <AppLayout {...props} exact />}
			/>
		</Switch>
	);
};

export default Router;
