import { WrappedFormUtils } from "@ant-design/compatible/lib/form/Form";
import { SettingOutlined } from "@ant-design/icons";
import {
	<PERSON><PERSON>,
	Card,
	Col,
	DatePicker,
	Dropdown,
	Menu,
	Modal,
	Row,
	Table,
} from "antd";
import Select from "antd/lib/select";
import "./index.less";

import { ColumnProps, TablePaginationConfig } from "antd/lib/table";
import { FilterDropdownProps, SorterResult } from "antd/lib/table/interface";
import { inject, observer } from "mobx-react";
import moment from "moment";
import { Document, Page } from "react-pdf";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import FilterSelect from "../../components/FilterSelect/FilterSelect";
import { L, isGranted } from "../../lib/abpUtility";
import { FilterByColumn } from "../../models/Sort/SortState";
import { EntityDto } from "../../services/dto/entityDto";
import { GetMeterReadingOutput } from "../../services/meterReading/dto/getMeterReadingOutput";
import DeviceStore from "../../stores/deviceStore";
import MeterReadingStore from "../../stores/meterReadingStore";
import MeterStore from "../../stores/meterStore";
import ShipStore from "../../stores/shipStore";
import Stores from "../../stores/storeIdentifier";
import utils from "../../utils/utils";
import { ModalType } from "../ModalConsts";
import {
	getTablePaginationOptions,
	renderCheckboxValue,
	renderDate,
	renderFilterIcon,
} from "../renderUtils";
import DetailsMeterReading from "./components/detailsMeterReading";
import EditMeterReading, { Rect } from "./components/editMeterReading";
import ReadingStatus from "./components/ReadingStatus";
import ReadingKPIs from "./components/ReadingKPIs";

export interface IMeterReadingProps {
	meterReadingStore: MeterReadingStore;
	deviceStore: DeviceStore;
	meterStore: MeterStore;
	shipStore: ShipStore;
}

interface IMeterReadingDateFilters {
	creationTimeStart?: string;
	creationTimeEnd?: string;
}

export interface IMeterReadingState {
	editModalVisible: boolean;
	modalVisible: boolean;
	modalDetailsVisible: boolean;
	maxResultCount: number;
	skipCount: number;
	meterReadingId: number;
	sorting: string;
	filters: Array<FilterByColumn>;
	loading: boolean;
	okButtonDisabledEdit: boolean;
	okButtonDisabled: boolean;
	isPdfModalVisible: boolean;
	updateLoading: boolean;
	fileUrl: string;
	selectedPdfRecord: GetMeterReadingOutput | null;
	isFilePdf: boolean;
	resultEdit: boolean;
	fetchingFilters: boolean;
	downloading: boolean;
	searchColumn: string;
	metricFilters: string[];
	sorters: SorterResult<GetMeterReadingOutput>[];
	activeDateFilters: IMeterReadingDateFilters;
}

const { Option } = Select;

const confirm = Modal.confirm;

type PreviousState = {
	filters: IMeterReadingState["filters"];
	sorters: IMeterReadingState["sorters"];
	activeDateFilters: IMeterReadingState["activeDateFilters"];
	metricFilters: IMeterReadingState["metricFilters"];
};

@inject(Stores.MeterReadingStore)
@inject(Stores.MeterStore)
@inject(Stores.DeviceStore)
@inject(Stores.ShipStore)
@observer
class MeterReading extends AppComponentBase<
	IMeterReadingProps,
	IMeterReadingState
> {
	formRefEdit?: WrappedFormUtils;
	formRef?: WrappedFormUtils;

	state: IMeterReadingState = {
		editModalVisible: false,
		modalVisible: false,
		modalDetailsVisible: false,
		fetchingFilters: false,
		downloading: false,
		maxResultCount: 10,
		skipCount: 0,
		meterReadingId: 0,
		sorting: "",
		filters: [],
		metricFilters: [],
		fileUrl: "",
		updateLoading: false,
		isPdfModalVisible: false,
		isFilePdf: false,
		selectedPdfRecord: null,
		loading: false,
		sorters: [],
		okButtonDisabled: false,
		okButtonDisabledEdit: false,
		resultEdit: false,
		searchColumn: "",
		activeDateFilters: {
			creationTimeStart: undefined,
			creationTimeEnd: undefined,
		},
	};

	async componentDidMount() {
		const prevState = this.extractPreviousState();

		this.setState({ ...prevState }, async () => {
			await Promise.all([this.getAll()]);
		});
	}

	async fetchFilters(property: string) {
		this.setState({ fetchingFilters: true });
		const [searchColumnString, keywordString] = utils.getFilterStrings([
			...this.state.filters,
		]);

		try {
			await this.props.meterReadingStore.getFilters(
				{
					keyword: keywordString,
					searchColumn: searchColumnString,
				},
				property,
			);
		} catch {
			Modal.error({
				title: "Error during fetching",
				content: (
					<div>
						<p>Failed to fetch filters for column</p>
					</div>
				),
			});
		}

		this.setState({ fetchingFilters: false });
	}

	extractPreviousState(): PreviousState {
		let state: PreviousState = {
			filters: [],
			sorters: [],
			activeDateFilters: {},
			metricFilters: [],
		};

		const prevState =
			utils.getSortAndFilterFromStorage<PreviousState>("readings-filters");

		if (prevState)
			state = {
				...prevState,
			};

		utils.removeStateFromStorage("readings-filters");

		return state;
	}

	componentWillUnmount(): void {
		const settings: PreviousState = {
			sorters: this.state.sorters,
			filters: this.state.filters,
			activeDateFilters: this.state.activeDateFilters,
			metricFilters: this.state.metricFilters,
		};

		utils.saveSortAndFilterToStorage("readings-filters", settings);
	}

	async getAll() {
		this.setState({ loading: true });
		const sortString = utils.getSorterString(this.state.sorters);

		const [searchColumnString, keywordString] = utils.getFilterStrings([
			...this.state.filters,
		]);

		await this.props.meterReadingStore.getAll({
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: keywordString,
			sorting: sortString,
			searchColumn: searchColumnString,
			AuditMetrics: this.state.metricFilters,
			creationTimeStart: this.state.activeDateFilters.creationTimeStart,
			creationTimeEnd: this.state.activeDateFilters.creationTimeEnd,
		});
		this.setState({ loading: false });

		const { editMeterReader } = this.props.meterReadingStore;
		const areValuesEqual =
			editMeterReader && editMeterReader.value === editMeterReader.editValue;
		this.setState({ resultEdit: areValuesEqual });
	}

	handleTableChange = (
		pagination: TablePaginationConfig,
		_: Partial<Record<keyof GetMeterReadingOutput, string[]>>,
		sorter:
			| SorterResult<GetMeterReadingOutput>
			| SorterResult<GetMeterReadingOutput>[],
	) => {
		const sorters = utils.getSorters(sorter);

		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				sorters: sorters,
			},
			async () => await this.getAll(),
		);
	};

	modal = () => {
		this.setState({
			modalVisible: !this.state.modalVisible,
		});
	};

	showPdfModal = (record: GetMeterReadingOutput) => {
		const isPdf = record.fileUrl
			? record.fileUrl.split("?")[0].endsWith(".pdf")
			: false;
		this.setState({
			isPdfModalVisible: true,
			selectedPdfRecord: record,
			isFilePdf: isPdf,
			fileUrl: record.fileUrl,
		});
	};

	hidePdfModal = () => {
		this.setState({
			isPdfModalVisible: false,
			selectedPdfRecord: null,
			isFilePdf: false,
		});
	};

	async createOrUpdateModalOpen(entityDto: EntityDto) {
		if (entityDto.id === 0) {
			await this.props.meterReadingStore.createMeterReading();
		} else {
			await this.props.meterReadingStore.get(entityDto);
		}

		this.setState({ meterReadingId: entityDto.id });
		this.modal();

		if (!this.formRef) return;

		this.formRef.setFieldsValue({
			...this.props.meterReadingStore.editMeterReader,
			roleNames: this.props.meterReadingStore.editMeterReader.roleNames,
		});
	}

	async detailsModalOpen(entityDto: EntityDto) {
		if (entityDto.id !== 0) {
			await this.props.meterReadingStore.get(entityDto);
			this.setState({ meterReadingId: entityDto.id });
			this.toggleModalDetails(true);
			const editMeterReader = this.props.meterReadingStore.editMeterReader;
			if (!this.formRef) return;
			this.formRef.setFieldsValue({
				...editMeterReader,
				roleNames: editMeterReader.roleNames,
				meterName: editMeterReader.meter.name,
				shipName: editMeterReader.meter.ship.shipName,
				reasonsForEvent: editMeterReader.reasonForEvent,
				value: editMeterReader.value,
				fuelType: editMeterReader.fuelType,
				creationTime: editMeterReader.creationTime,
				imoNumber: editMeterReader.meter.ship.imoNumber,
				meterType: editMeterReader.meter.meterType,
				unitOfMeasure: editMeterReader.meter.unitOfMeasure,
				digitsCount: editMeterReader.meter.digitsCount,
				decimalPointPosition: editMeterReader.meter.decimalPointPosition,
				flowDirection: editMeterReader.meter.flowDirection,
				measurementType: editMeterReader.meter.measurementType,
				scheduledReading: editMeterReader.meter.scheduledReadings[0],
				scrubber: editMeterReader.scrubber,
			});
		}
	}

	toggleModalDetails = (togle: boolean) => {
		this.setState({
			modalDetailsVisible: togle,
		});
	};

	download = async () => {
		this.setState({ downloading: true });

		try {
			await this.props.meterReadingStore.download(
				this.state.activeDateFilters.creationTimeStart,
				this.state.activeDateFilters.creationTimeEnd,
			);
		} catch {
			Modal.error({
				title: "Error",
				content: "Downloading meter readings failed",
			});
		}

		this.setState({ downloading: false });
	};

	modalDetailsOnCancel = () => {
		this.toggleModalDetails(false);
	};

	delete(input: EntityDto) {
		const self = this;
		confirm({
			title: L("DoYouWantToDeleteThisItem"),
			onOk() {
				self.props.meterReadingStore.delete(input);
			},
			onCancel() {},
		});
	}

	handleAuditMetricsFilter = (value: string[]) => {
		this.setState({ metricFilters: value, skipCount: 0 }, async () => {
			await this.getAll();
		});
	};

	getColumnSearchProps = (
		dataIndex: string,
		displayName: string,
	): ColumnProps<GetMeterReadingOutput> => ({
		filterDropdown: (props: FilterDropdownProps) => (
			<div>
				{props.visible && (
					<FilterSelect
						{...props}
						loading={this.state.fetchingFilters}
						handleFilter={(value) => this.handleFilter(value, dataIndex)}
						title={displayName}
						value={
							this.state.filters.find((x) => x.column === dataIndex)?.value
						}
						options={this.props.meterReadingStore.filters?.map((x) => {
							return { key: x, value: x };
						})}
					/>
				)}
			</div>
		),
		onFilterDropdownVisibleChange: (v) => {
			if (v) this.fetchFilters(dataIndex);
		},
		filterIcon: () =>
			renderFilterIcon(
				this.state.filters.findIndex((x) => x.column === dataIndex) >= 0,
			),
		sortOrder: this.state.sorters.find((x) => x.columnKey === dataIndex)?.order,
	});

	handleReset = (dataIndex: string, clearFilters?: () => void) => {
		if (clearFilters) clearFilters();
		this.setState({
			skipCount: 0,
			sorting: "",
			searchColumn: "",
		});
	};

	handleCreate = () => {
		if (!this.formRef) return;
		const form = this.formRef;
		// biome-ignore lint/suspicious/noExplicitAny: Poor type implementation from library
		form.validateFields(async (err: any, values: any) => {
			if (err) {
				return;
			}
			if (this.state.meterReadingId === 0) {
				await this.props.meterReadingStore.create(values);
			} else {
				await this.props.meterReadingStore.update({
					id: this.state.meterReadingId,
					...values,
				});
			}
			await this.getAll();
			this.setState({ modalVisible: false });
			form.resetFields();
		});
	};

	// biome-ignore lint/suspicious/noExplicitAny: Poor type implementation from library
	saveFormRef = (formRef: any) => {
		if (!formRef) return;
		this.formRef = formRef.props.form;
	};

	handleSearch = (value: string) => {
		this.setState({ sorting: value }, async () => await this.getAll());
	};

	filterTypes = (value: string, record: GetMeterReadingOutput) => {
		return record.consumerType.toString() === value;
	};

	async editModalOpen(entityDto: EntityDto) {
		if (entityDto.id !== 0) {
			await this.props.meterReadingStore.get(entityDto);
		}
		this.setState({ meterReadingId: entityDto.id });
		this.modalEdit();
		if (!this.formRefEdit) return;

		this.formRefEdit.setFieldsValue({
			...this.props.meterReadingStore.editMeterReader,
			roleNames: this.props.meterReadingStore.editMeterReader.roleNames,
		});
	}

	async toggleStatus(id: EntityDto["id"]) {
		if (id <= 0) {
			return;
		}
		try {
			await this.props.meterReadingStore.toggleStatus(id);
			Modal.success({
				title: "Success",
				content: "Successfully updated status",
			});

			await this.getAll();
		} catch {
			Modal.error({
				title: "Error",
				content: "Changing meter reading status failed",
			});
		}
	}

	modalEdit = () => {
		this.setState({
			editModalVisible: !this.state.editModalVisible,
		});
	};

	handleEdit = (rect?: Rect) => {
		if (!this.formRefEdit) return;
		this.forceUpdate();
		const form = this.formRefEdit;
		// biome-ignore lint/suspicious/noExplicitAny: Poor type implementation from library
		form.validateFields(async (err: any, values: any) => {
			if (err) {
				return;
			}
			this.setState({ updateLoading: true });
			try {
				if (this.state.meterReadingId === 0) {
					await this.props.meterReadingStore.create(values);
				} else {
					await this.props.meterReadingStore.updateValue({
						id: this.state.meterReadingId,
						value: values.editValue,
						rectangle: rect,
						rollover: values.rollover,
					});
				}
				await this.getAll();
				form.resetFields();
				this.setModalEditVisible();
			} catch (ex) {
			} finally {
				this.setState({ updateLoading: false, okButtonDisabledEdit: false });
			}
		});
	};

	setModalEditVisible = () => {
		this.setState({ editModalVisible: false });
	};

	// biome-ignore lint/suspicious/noExplicitAny: Poor type implementation from library
	saveFormRefEdit = (formRefEdit: any) => {
		if (!formRefEdit) return;
		this.formRefEdit = formRefEdit.props.form;
	};

	private handleFilter(value: string, column: string) {
		let currentFilters = [...this.state.filters];
		currentFilters = currentFilters.filter((f) => f.column !== column);

		if (value) {
			currentFilters.push({ value, column: column });
		}

		this.setState({ filters: currentFilters }, async () => {
			await this.getAll();
		});
	}

	public render() {
		const { meterReadings } = this.props.meterReadingStore;
		const paginationOptions = getTablePaginationOptions(
			meterReadings?.totalCount,
		);

		const columns: ColumnProps<GetMeterReadingOutput>[] = [
			{
				title: L("Ship Name"),
				dataIndex: "meter.ship.shipName",
				key: "meter.ship.shipName",
				render: (_: string, record: GetMeterReadingOutput) => {
					return record.meter.ship.shipName;
				},
				sorter: { multiple: 5 },
				...this.getColumnSearchProps("meter.ship.shipName", L("Ship Name")),
				width: 200,
			},
			{
				title: L("Meter Name"),
				dataIndex: "meter.name",
				key: "meter.name",
				render: (_: string, record: GetMeterReadingOutput) => {
					return record.meter.name;
				},
				sortOrder: this.state.sorters.find((x) => x.columnKey === "meter.name")
					?.order,
				sorter: { multiple: 5 },
				...this.getColumnSearchProps("meter.name", L("Meter Name")),
				width: 200,
			},
			{
				title: L("Reasons for this event"),
				dataIndex: "reasonForEvent",
				key: "reasonForEvent",
				sorter: { multiple: 3 },
				...this.getColumnSearchProps(
					"reasonForEvent",
					L("Reasons for this event"),
				),
				width: 250,
			},
			{
				title: L("Vessel Status"),
				dataIndex: "vesselStatus",
				key: "vesselStatus",
				sorter: { multiple: 6 },
				...this.getColumnSearchProps("vesselStatus", L("Vessel Status")),
				width: 200,
				render: (text: string) => (text === "" ? "-" : text),
			},
			{
				title: L("Meter Reading"),
				dataIndex: "editValue",
				key: "editValue",
				sortOrder: this.state.sorters.find((x) => x.columnKey === "editValue")
					?.order,
				sorter: { multiple: 7 },
				width: 200,
			},
			{
				title: L("AI Meter Reading"),
				dataIndex: "aiValue",
				key: "aiValue",
				sortOrder: this.state.sorters.find((x) => x.columnKey === "aiValue")
					?.order,
				sorter: { multiple: 7 },
				width: 200,
			},
			{
				title: L("Rollover Adjust"),
				dataIndex: "rollover",
				key: "rollover",
				sorter: { multiple: 5 },
				...this.getColumnSearchProps("rollover", L("Rollover Adjust")),
				width: 200,
			},
			{
				title: isGranted("Pages.MeterReadings-Edit")
					? L("Audit metrics")
					: L("Status"),
				dataIndex: "validationErrors",
				key: "validationErrors",
				width: 250,
				render: (_: string, item: GetMeterReadingOutput) =>
					isGranted("Pages.MeterReadings-Edit") ? (
						<ReadingKPIs auditMetrics={item.auditMetrics} />
					) : (
						<ReadingStatus verified={item.auditMetrics.checked} />
					),
				filterDropdown: () => {
					return (
						<div
							style={{
								display: "flex",
								flexDirection: "column",
								maxWidth: 250,
							}}
						>
							<Select
								mode="multiple"
								showSearch
								placeholder={L("Select Audit Metrics")}
								allowClear
								value={this.state.metricFilters}
								style={{ minWidth: "200px", maxWidth: 250 }}
								onChange={(value: string[]) => {
									this.handleAuditMetricsFilter(value);
								}}
							>
								<Option key="editedBySBU" value="EditedBySBU">
									{L('Don\'t show if "Checked by SBU = Y"')}
								</Option>
								<Option key="checkedBySBU" value="CheckedBySBU">
									{L('Don\'t show if "Edited by SBU = Y"')}
								</Option>
								<Option key="aiDiscrepancy" value="AiDiscrepancy">
									{L('Show if "AI Discrepancy = Y"')}
								</Option>
								<Option key="digits" value="IncorrectDigits">
									{L('Show if "Fewer digits than previous = Y"')}
								</Option>
								<Option key="lower" value="ResultLower">
									{L('Show if "Lower than previous"')}
								</Option>
								<Option key="editedByOBU" value="EditedByOBU">
									{L('Don\'t show if "Edited by OBU = Y"')}
								</Option>
							</Select>
						</div>
					);
				},
				filterIcon: () =>
					renderFilterIcon(
						this.state.filters.findIndex((f) => f.column === "auditMetrics") >=
							0,
					),
			},
			{
				width: 200,
				title: L("Audit"),
				render: (text: string, record: GetMeterReadingOutput) => (
					<div>
						<Button type="primary" onClick={() => this.showPdfModal(record)}>
							{L("Audit image")}
						</Button>
					</div>
				),
			},
			{
				title: L("Meter read at (UTC)"),
				dataIndex: "creationTime",
				key: "creationTime",
				render: (text: string) => renderDate(text, true),
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "creationTime",
				)?.order,
				sorter: { multiple: 5 },
				filterDropdown: ({ confirm }: FilterDropdownProps) => (
					<div style={{ padding: 8 }}>
						<DatePicker.RangePicker
							style={{ marginBottom: 8, width: 200 }}
							value={
								this.state.activeDateFilters.creationTimeStart
									? [
											moment(this.state.activeDateFilters.creationTimeStart) ||
												null,
											moment(this.state.activeDateFilters.creationTimeEnd) ||
												null,
										]
									: null
							}
							onChange={(dates) => {
								const [startDate, endDate] = dates || [];
								const formattedDates = [
									startDate?.toISOString(),
									endDate?.toISOString(),
								];
								this.setState(
									{
										activeDateFilters: {
											creationTimeStart: formattedDates[0],
											creationTimeEnd: formattedDates[1],
										},
									},
									async () => await this.getAll(),
								);
								confirm?.();
							}}
						/>
					</div>
				),
				filterIcon: () =>
					renderFilterIcon(
						this.state.activeDateFilters.creationTimeEnd !== undefined &&
							this.state.activeDateFilters.creationTimeStart !== undefined,
					),
				width: 200,
			},
			{
				title: L("Fuel Type"),
				dataIndex: "fuelType",
				key: "fuelType",
				sorter: { multiple: 3 },
				...this.getColumnSearchProps("fuelType", L("Fuel Type")),
				width: 200,
			},
			{
				title: L("Scrubber working?"),
				dataIndex: "scrubber",
				key: "scrubber",
				sorter: { multiple: 8 },
				render: (value: boolean) => (
					<div style={{ display: "flex", justifyContent: "center" }}>
						{renderCheckboxValue(value)}
					</div>
				),
				...this.getColumnSearchProps("scrubber", L("Scrubber")),
				width: 150,
			},
			{
				title: L("IMO"),
				dataIndex: "meter.ship.imoNumber",
				key: "meter.ship.imoNumber",
				render: (text: string, record: GetMeterReadingOutput) => {
					return record.meter.ship.imoNumber;
				},
				sorter: { multiple: 7 },
				width: 200,
				...this.getColumnSearchProps("meter.ship.imoNumber", L("IMO")),
			},
			{
				title: L("Meter Type"),
				dataIndex: "meter.meterType",
				key: "meter.meterType",
				render: (text: string, record: GetMeterReadingOutput) => {
					return record.meter.meterType;
				},
				sorter: { multiple: 6 },
				width: 200,
				...this.getColumnSearchProps("meter.meterType", L("Meter Type")),
			},
			{
				title: L("Unit Of Measure"),
				dataIndex: "meter.unitOfMeasure",
				key: "meter.unitOfMeasure",
				render: (text: string, record: GetMeterReadingOutput) => {
					return record.meter.unitOfMeasure;
				},
				sorter: { multiple: 5 },
				...this.getColumnSearchProps(
					"meter.unitOfMeasure",
					L("Unit Of Measure"),
				),
				width: 200,
			},
			{
				width: 150,
				title: L("Actions"),
				fixed: "right" as const,
				render: (text: string, item: GetMeterReadingOutput) => (
					<div>
						<Dropdown
							trigger={["click"]}
							overlay={
								<Menu>
									{isGranted("Pages.MeterReadings-Edit") && (
										<Menu.Item
											onClick={() => this.detailsModalOpen({ id: item.id })}
										>
											{L("Details")}
										</Menu.Item>
									)}
									{isGranted("Pages.MeterReadings-Edit") && (
										<Menu.Item
											onClick={() => this.editModalOpen({ id: item.id })}
										>
											{L("Edit")}
										</Menu.Item>
									)}
									{isGranted("Pages.MeterReadings-Delete") && (
										<Menu.Item
											onClick={() => this.delete({ id: item.id })}
											danger
										>
											{L("Delete")}
										</Menu.Item>
									)}
									{isGranted("Pages.MeterReadings-Toggle Status") && (
										<Menu.Item onClick={() => this.toggleStatus(item.id)}>
											{L("Toggle status")}
										</Menu.Item>
									)}
								</Menu>
							}
							placement="bottomLeft"
						>
							<Button type="primary" icon={<SettingOutlined />}>
								{L("Actions")}
							</Button>
						</Dropdown>
					</div>
				),
			},
		];

		const hasNoActions =
			!isGranted("Pages.MeterReadings-Edit") &&
			!isGranted("Pages.MeterReadings-Delete") &&
			!isGranted("Pages.MeterReadings-Toggle Status");

		if (hasNoActions) {
			columns.pop();
		}

		// const hasSelected = selectedRows.items.length > 0;

		return (
			<Card className="meter-readings">
				<Row>
					<Col xxl={{ span: 2, offset: 22 }}>
						{isGranted("Pages.MeterReadings-Download Readings") && (
							<Button
								type={"primary"}
								loading={this.state.downloading}
								onClick={() => this.download()}
							>
								Download
							</Button>
						)}
					</Col>
				</Row>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey={(record) => record.id.toString()}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={this.state.loading}
							dataSource={meterReadings ? meterReadings.items : []}
							onChange={this.handleTableChange}
							scroll={{ y: 850 }}
						/>
					</Col>
				</Row>
				<Modal
					title="Control image viewer"
					visible={this.state.isPdfModalVisible}
					onCancel={this.hidePdfModal}
					footer={null}
				>
					<div className="pdfContainer" id="pdfContainer">
						{this.state.isFilePdf ? (
							<Document file={this.state.selectedPdfRecord?.fileUrl}>
								<Page
									pageNumber={1}
									height={
										document.getElementById("pdfContainer")?.clientHeight || 500
									}
									className={"canvasStyle"}
									renderInteractiveForms
									renderAnnotationLayer
								/>
							</Document>
						) : (
							<img
								src={this.state.selectedPdfRecord?.fileUrl}
								style={{ width: "100%", height: "auto" }}
								alt="Control Image"
							/>
						)}
					</div>
				</Modal>
				<DetailsMeterReading
					wrappedComponentRef={this.saveFormRef}
					visible={this.state.modalDetailsVisible}
					onCancel={this.modalDetailsOnCancel}
					modalType={ModalType.edit}
					okButtonDisabled={this.state.okButtonDisabled}
				/>
				{this.state.editModalVisible && (
					<EditMeterReading
						meterReadingsStore={this.props.meterReadingStore}
						wrappedComponentRef={this.saveFormRefEdit}
						visible={this.state.editModalVisible}
						modalType={ModalType.edit}
						loading={this.state.updateLoading}
						okButtonDisabled={this.state.okButtonDisabledEdit}
						roles={this.props.meterReadingStore.roles}
						onSubmit={this.handleEdit}
						onCreate={() => {}}
						onCancel={this.setModalEditVisible}
					/>
				)}
				<Chat />
			</Card>
		);
	}
}

export default MeterReading;
