import { EntityDto } from "../dto/entityDto";
import { PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoints";
import http from "../httpService";
import { CreateOrUpdateDataSourceInput } from "./dto/CreateOrUpdateDataSourceInput";
import { PagedFilterAndSortedRequestDataSource } from "./dto/PagedFilterAndSortedRequestDataSource";
import { UpdateDataSourceInput as UpdateDataSource } from "./dto/UpdateDataSourceInput";

class DataSourceService {
	public async create(createDataSourceInput: CreateOrUpdateDataSourceInput) {
		const result = await http.post(
			Endpoint.DataSource.Create,
			createDataSourceInput,
		);
		return result.data.result;
	}

	public async update(UpdateDataSourceInput: UpdateDataSource) {
		const result = await http.put(
			Endpoint.DataSource.Update,
			UpdateDataSourceInput,
		);
		return result.data.result;
	}

	public async delete(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.DataSource.Delete, {
			params: entityDto,
		});
		return result.data;
	}

	public async get(
		entityDto: EntityDto,
	): Promise<CreateOrUpdateDataSourceInput> {
		const result = await http.get(Endpoint.DataSource.Get, {
			params: entityDto,
		});
		return result.data.result;
	}

	public async getAll(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequestDataSource,
	): Promise<PagedResultDto<CreateOrUpdateDataSourceInput>> {
		const result = await http.get(Endpoint.DataSource.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}
}

export default new DataSourceService();
