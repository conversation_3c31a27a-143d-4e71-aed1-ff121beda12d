import { ValidationRule } from "@ant-design/compatible/lib/form";
import { L } from "../../../lib/abpUtility";

const rules = {
	fuelType: [{ required: true, message: L("Please Select Fuel Type") }],
	densityValue: [
		{ required: true, message: L("Please input Fuel Density (Kg/cbm)") },
		{
			pattern: new RegExp(/^\d+$/),
			message: L("Please input Fuel Density (Kg/cbm)"),
		},
	] as ValidationRule[],
	lcv: [
		{ required: true, message: L("Please Input Fuel LCV (KJ/Kg)") },
		{
			pattern: new RegExp(/^\d+$/),
			message: L("Please input Fuel LCV (KJ/Kg)"),
		},
	] as ValidationRule[],
	co2Emissions: [
		{ required: true, message: L("Please input CO2eq Emissions (gCO2eq/MJ)") },
		{
			pattern: new RegExp(/^\d+$/),
			message: L("Please input CO2eq Emissions (gCO2eq/MJ)"),
		},
	] as ValidationRule[],
	cslip: [
		{ required: true, message: L("Please input Cslip (% of Fuel mass)") },
		{
			pattern: new RegExp(/^\d+$/),
			message: L("Please input Cslip (% of Fuel mass)"),
		},
	] as ValidationRule[],
	calorificValue: [
		{ required: true, message: L("Please input Calorific") },
		{ pattern: new RegExp(/^\d+$/), message: L("Please Input Digits") },
	] as ValidationRule[],
	co2Factor: [{ required: true, message: L("Please input Cf (gCO2/g fuel)") }],
	n2oFactor: [{ required: true, message: L("Please input Cf (gN2O/g fuel)") }],
	ch4Factor: [{ required: true, message: L("Please input Cf (gCH4/g fuel)") }],
};

export default rules;
