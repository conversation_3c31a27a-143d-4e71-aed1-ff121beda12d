import { AuthenticationType, data, layer, source } from "azure-maps-control";
import * as React from "react";
import { useContext, useEffect, useMemo, useState } from "react";
import {
	AzureMap,
	AzureMapDataSourceProvider,
	AzureMapFeature,
	AzureMapHtmlMarker,
	AzureMapLayerProvider,
	AzureMapPopup,
	AzureMapsContext,
	IAzureMapOptions,
	IAzureMapPopupEvent,
	IAzureMapsContextProps,
} from "react-azure-maps";
import AppConsts from "../../../lib/appconst";
import { ECABorder } from "../../../services/ais/dto/ECABorder";
import { ShipTraceExtended } from "../../../services/notifications/dto/ShipTrace";
import { SingleNotification } from "../../../services/notifications/dto/SingleNotification";
import { azureMapControls } from "../../Map/common/azureMapControls";
import NotificationMapLoader from "./notificationMapLoader";
import NotificationPopUp from "./notificationPopUp";

export interface NotificationMapProps {
	notification: SingleNotification;
	shipTraces: ShipTraceExtended[];
	setFooterData: React.Dispatch<any>;
	ecaVisible: boolean;
	ecaBorders: ECABorder[];
}

const NotificationMap: React.FC<NotificationMapProps> = ({
	notification,
	shipTraces,
	setFooterData,
	ecaVisible,
	ecaBorders,
}) => {
	const option: IAzureMapOptions = {
		authOptions: {
			authType: AuthenticationType.subscriptionKey,
			subscriptionKey: AppConsts.azureMapSubscriptionKey,
		},
		center: [notification.longtitude, notification.latitude],
		zoom: 8,
		language: "en-US",
	};

	const [popupVisible, setPopUpVisible] = useState<boolean>(false);
	const [currentCoord, setCurrentCoord] = useState<ShipTraceExtended | null>(
		null,
	);
	const { mapRef, isMapReady } =
		useContext<IAzureMapsContextProps>(AzureMapsContext);

	function showPopUp() {
		setPopUpVisible(true);
	}

	function getCoordinates(e: any) {
		const threshold = 0.005;
		if (e.position && e.position.length > 1) {
			let coordFounded = false;

			for (const coord of shipTraces) {
				// Check if the absolute difference between the coordinates is within the threshold
				if (
					Math.abs(coord.Latitude - e.position[1]) <= threshold &&
					Math.abs(coord.Longitude - e.position[0]) <= threshold
				) {
					setFooterData(coord);
					setCurrentCoord(coord);
					coordFounded = true;
					break;
					// return coord; // Return the matching record
				}
				if (!coordFounded) {
					setFooterData(null);
				}
			}
		}
	}

	const memoizedMarkerRender = useMemo(
		() => (
			<AzureMapFeature
				key={notification.guid}
				id={notification.toString()}
				type="Point"
				coordinate={
					new data.Position(
						notification?.longtitude ?? 0,
						notification?.latitude ?? 0,
					)
				}
				properties={{
					title: "Pin",
					icon: "pin-round-blue",
				}}
			/>
		),
		[notification],
	);

	const handlePopupEvent: IAzureMapPopupEvent = {
		eventName: "close",
		callback: (e: any) => {
			setPopUpVisible(false);
		},
	};

	const memoizedMapPopup = React.useMemo(
		() => (
			<AzureMapPopup
				isVisible={popupVisible}
				options={{
					position: new data.Position(
						notification?.longtitude ?? 0,
						notification?.latitude ?? 0,
					),
					pixelOffset: [0, -30],
				}}
				popupContent={<NotificationPopUp notification={notification} />}
				events={[handlePopupEvent]}
			/>
		),
		[popupVisible],
	);

	const memoizedHtmlMarkerRender = React.useMemo(
		() => (
			<AzureMapHtmlMarker
				key={"asd"}
				isPopupVisible={currentCoord != null}
				markerContent={
					<div
						style={{
							width: 16,
							height: 16,
							background: "black",
							borderRadius: "50%",
							border: "2px solid white",
						}}
					>
						
					</div>
				}
				options={{
					position: new data.Position(
						currentCoord?.Longitude ?? 0,
						currentCoord?.Latitude ?? 0,
					),
					pixelOffset: [0, 8],
					visible: currentCoord != null,
				}}

				// events={eventToMarker}
			/>
		),
		[currentCoord, isMapReady],
	);

	useEffect(() => {
		if (isMapReady && mapRef) {
			const features: any[] = [];

			for (let i = 1; i < shipTraces.length; i++) {
				const currentPoint = shipTraces[i];
				const previousPoint = shipTraces[i - 1];

				const coordinates: [number, number][] = [
					[previousPoint.Longitude, previousPoint.Latitude],
					[currentPoint.Longitude, currentPoint.Latitude],
				];

				const lineString: any = {
					type: "LineString",
					coordinates: coordinates,
				};

				const feature: any = {
					type: "Feature",
					properties: {
						myColor: currentPoint.LineColor,
					},
					geometry: lineString,
					id: i,
				};

				features.push(feature);
			}

			const data = {
				type: "FeatureCollection",
				features: features,
			};

			const dataSource = new source.DataSource(" 123 asd");
			mapRef.sources.add(dataSource);
			dataSource.add(data);

			const lineLayer = new layer.LineLayer(dataSource, "123", {
				strokeColor: ["get", "myColor"],
				strokeWidth: 5,
				visible: true,
			});

			mapRef.layers.add(lineLayer);
		}
	}, [isMapReady]);

	useEffect(() => {
		if (isMapReady && mapRef) {
			const convertedList: { [key: string]: { lat: number; long: number }[] } =
				{};
			ecaBorders.forEach((obj: ECABorder) => {
				if (!convertedList[obj.code]) {
					convertedList[obj.code] = [];
				}
				convertedList[obj.code].push({
					lat: obj.latitude,
					long: obj.longitude,
				});
			});
			// dataSource.add({});

			for (const typeid in convertedList) {
				const dataSource = new source.DataSource(typeid);

				const coordinates = convertedList[typeid].map((x: any) => {
					return new data.Position(x.long, x.lat);
				});
				coordinates.push(
					new data.Position(
						convertedList[typeid][0].long,
						convertedList[typeid][0].lat,
					),
				);

				dataSource.add(new data.Feature(new data.Polygon([coordinates])));

				const res = mapRef.sources.getById(typeid);
				if (res == null) {
					mapRef.sources.add(dataSource);
				}

				const polygon = new layer.PolygonLayer(dataSource, typeid, {
					fillColor: "green",
					fillOpacity: 0.1,
					strokeColor: "red",
					strokeWidth: 5,
					visible: ecaVisible,
				});

				const res1 = mapRef.layers.getLayerById(typeid);
				if (res1 != null) {
					mapRef.layers.remove(typeid);
				}

				mapRef.layers.add(polygon);
			}
		}
	}, [isMapReady, ecaVisible]);

	return (
		<AzureMap
			styles={{ width: "640px", height: "640px" }}
			options={option}
			events={{ click: getCoordinates }}
			controls={azureMapControls}
			LoaderComponent={NotificationMapLoader}

			// styleOptions={{ style: mapType }}
		>
			<AzureMapDataSourceProvider
				id={"markersExample AzureMapDataSourceProvider"}
				options={{ cluster: true, clusterRadius: 2 }}
			>
				<AzureMapLayerProvider
					id={"markersExample AzureMapLayerProvider"}
					// options={layerOptions}
					type={"SymbolLayer"}
					events={{
						click: showPopUp,
					}}
				/>
				{memoizedMarkerRender}
			</AzureMapDataSourceProvider>
			<AzureMapDataSourceProvider id={"AzureMapDataSourceProvidere_MapPopup"}>
				<AzureMapLayerProvider
					id={"markersExample AzureMapLayerProvider 234"}
					//options={layerOptions}
					// options={{
					//     strokeWidth: 5,
					//     color: "#000000"
					// }}
					type={"SymbolLayer"}
				/>
				{memoizedHtmlMarkerRender}
			</AzureMapDataSourceProvider>
			{/* <AzureMapDataSourceProvider id={'DataSource Provider1222'}>
                <AzureMapLayerProvider
                    id={'shape AzureMapLayerProvider1162'}
                    type={"LineLayer"} />
                {memomizedLine}
            </AzureMapDataSourceProvider> */}
			<AzureMapDataSourceProvider id={"AzureMapDataSourceProvider_MapPopup"}>
				<AzureMapLayerProvider
					id={"AzureMapLayerProvider_MapPopup"}
					type={"SymbolLayer"}
				/>
				{memoizedMapPopup}
			</AzureMapDataSourceProvider>
		</AzureMap>
	);
};

export default NotificationMap;
