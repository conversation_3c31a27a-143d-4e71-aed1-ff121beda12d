import { Moment } from "moment";
import { FuelTypeDto } from "../../fuelType/dto/fuelTypeDto";
import { GetShipOutput } from "../../ship/dto/getShipOutput";

export interface GetBunkeringNotesOutput {
	portOfBunkering: Port;
	creationTime: Date;
	fuelTypeLoaded: FuelTypeDto;
	quantityLoaded: string;
	density: string;
	supplier: string;
	portName: string;
	viscosity: number;
	loadedInto: string;
	date: Moment;
	id: number;
	source: "Web" | "Mobile";
	fileUrls: { url: string; name: string }[];
	co2Factor: number;
	fuelLCV: number;
	ship: GetShipOutput;
}

export interface Port {
	id: number;
	name: string;
	code: string;
	countryCode: string;
}
