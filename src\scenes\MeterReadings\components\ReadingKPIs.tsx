import { L } from "../../../lib/abpUtility";
import { Errors } from "../../../services/meterReading/dto/errors";
import { renderValidationValue } from "../../renderUtils";

const ReadingKPIs = ({ auditMetrics }: { auditMetrics: Errors }) => {
	return (
		<div>
			<div>
				{auditMetrics.overwritten && (
					<div
						style={{
							color: "red",
							display: "flex",
							justifyContent: "space-between",
							maxHeight: "fit-content",
						}}
					>
						{L("Edited by SBU")}
						<span style={{ color: "red" }}>
							{renderValidationValue(auditMetrics.overwritten)}
						</span>
					</div>
				)}
				<div style={{ display: "flex", justifyContent: "space-between" }}>
					{L("Fewer digits than previous")}
					<span>{renderValidationValue(auditMetrics.digits)}</span>
				</div>
				<div style={{ display: "flex", justifyContent: "space-between" }}>
					{L("Lower Than Previous")}
					<span>{renderValidationValue(auditMetrics.lower)}</span>
				</div>
				<div style={{ display: "flex", justifyContent: "space-between" }}>
					{L("AI Discrepancy")}
					<span>{renderValidationValue(auditMetrics.aiDiscrepancy)}</span>
				</div>
				<div style={{ display: "flex", justifyContent: "space-between" }}>
					{L("Edited by OBU")}
					<span>{renderValidationValue(auditMetrics.resultEdited)}</span>
				</div>
				<div style={{ display: "flex", justifyContent: "space-between" }}>
					{L("Checked by SBU")}
					<span>{renderValidationValue(auditMetrics.checked)}</span>
				</div>
			</div>
		</div>
	);
};

export default ReadingKPIs;
