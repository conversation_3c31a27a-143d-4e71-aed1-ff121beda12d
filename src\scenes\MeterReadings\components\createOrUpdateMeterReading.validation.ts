import { ValidationRule } from "@ant-design/compatible/lib/form";
import { L } from "../../../lib/abpUtility";

const rules: { [key: string]: ValidationRule[] } = {
	shipName: [{ required: true, message: L("PleaseSelectShip") }],
	voyageNumber: [{ required: true, message: L("PleaseInputVoyageNumber") }],
	value: [{ required: true, message: L("PleaseInputValue") }],
	meterBarcode: [{ required: true, message: L("PleaseInputBarcode") }],
	rollover: [
		{
			validator(rule, value, callback, source, options) {
				if (!value || value === "") return Promise.resolve();
				if (
					Number.isNaN(value) || // use type coercion to parse the _entirety_ of the string (`parseFloat` alone does not do this)...
					Number.isNaN(Number.parseInt(value))
				) {
					return Promise.reject("Must be a valid number");
				}

				if (Number.parseInt(value) < 0) {
					return Promise.reject("Must be a non negative value");
				}

				return Promise.resolve();
			},
		},
	],
};

export default rules;
