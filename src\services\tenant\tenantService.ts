import { EntityDto } from "../../services/dto/entityDto";
import { PagedResultDto } from "../../services/dto/pagedResultDto";
import Endpoint from "../endpoints";
import http from "../httpService";
import { PagedTenantResultRequestDto } from "./dto/PagedTenantResultRequestDto";
import CreateTenantInput from "./dto/createTenantInput";
import CreateTenantOutput from "./dto/createTenantOutput";
import { GetAllTenantOutput } from "./dto/getAllTenantOutput";
import GetTenantOutput from "./dto/getTenantOutput";
import UpdateTenantInput from "./dto/updateTenantInput";
import UpdateTenantOutput from "./dto/updateTenantOutput";

class TenantService {
	public async create(
		createTenantInput: CreateTenantInput,
	): Promise<CreateTenantOutput> {
		const result = await http.post(Endpoint.Tenant.Create, createTenantInput);
		return result.data.result;
	}

	public async delete(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.Tenant.Delete, {
			params: entityDto,
		});
		return result.data;
	}

	public async get(entityDto: EntityDto): Promise<GetTenantOutput> {
		const result = await http.get(Endpoint.Tenant.Get, { params: entityDto });
		return result.data.result;
	}

	public async getAll(
		pagedFilterAndSortedRequest: PagedTenantResultRequestDto,
	): Promise<PagedResultDto<GetAllTenantOutput>> {
		const result = await http.get(Endpoint.Tenant.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}

	public async update(
		updateTenantInput: UpdateTenantInput,
	): Promise<UpdateTenantOutput> {
		const result = await http.put(Endpoint.Tenant.Update, updateTenantInput);
		return result.data.result;
	}
}

export default new TenantService();
