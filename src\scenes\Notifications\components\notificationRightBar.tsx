import { Switch, Typography } from "antd";
import React from "react";

export interface NotificationRightBarProps {
	ecaVisible: boolean;
	setEcaVisible: React.Dispatch<any>;
}

const NotificationRightBar: React.FC<NotificationRightBarProps> = ({
	ecaVisible,
	setEcaVisible,
}) => {
	const { Text } = Typography;

	const onChange = (checked: boolean) => {
		setEcaVisible(checked);
	};

	return (
		<>
			<Text code>ECA zones</Text>
			<Switch defaultChecked={ecaVisible} onChange={onChange} />
		</>
	);
};

export default NotificationRightBar;
