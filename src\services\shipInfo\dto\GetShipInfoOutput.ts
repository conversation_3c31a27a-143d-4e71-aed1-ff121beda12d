export interface GetShipInfoOutput {
	ship: {
		shipName: string;
		imoNumber: string;
	};
	payload: string;
	shipId: number;
	mmsi: string;
	timeStamp: Date;
	latitude: number;
	longitude: number;
	course: number;
	sog: number;
	heading: number;
	distanceRemaining: number;
	estimatedTimeOfArrival: Date;
	timeBetweenRows: number;
	minSOG: number;
	maxSOG: number;
	avgSOG: number;
	sogAmplitude: number;
	aisDistancePoints: number;
	aisDistancePointsDirectLine: number;
	status: number;
	lastModificationTime: Date;
	lastModifierUserId: number;
	creationTime: Date;
	creatorUserId: number;
	id: number;
}
