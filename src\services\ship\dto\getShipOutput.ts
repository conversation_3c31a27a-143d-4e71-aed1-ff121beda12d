export interface GetShipOutput {
	id: number;
	shipName: string;
	MMSI: string;
	imoNumber: number;
	shipEmail: string;
	captainName: string;
	chiefEngineersName: string;
	engineRoomUnmannedHoursFrom: string[];
	engineRoomUnmannedHoursTo: string[];
	scrubber: string;
	lastModificationTime: Date;
	lastModifierUserId: number;
	creatorUserId: number;
	callSign: string;
	currentVoyageNumber: string;
	secretForToken: string;
	isSynchToGs: boolean;
	mmsi: number;
	cargoUnit: string;
	type: string;
	sdwt: number;
	gt: number;
	settings: {
		arrivalAndAnchorage: boolean;
		enteringECA: boolean;
		noonReportAt6: boolean;
		noonReportAt12: boolean;
		noonReportAt18: boolean;
	};
}
