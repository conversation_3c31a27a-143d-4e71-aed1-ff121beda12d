server {

  listen 80;

  location / {
    root   /usr/share/nginx/html;
    index  index.html index.htm;
    try_files $uri $uri/ /index.html;
  }

   location /.well-known/apple-app-site-association {
        default_type application/json;
        root /usr/share/nginx/html;
    }

  error_page   500 502 503 504  /50x.html;

  location = /50x.html {
    root   /usr/share/nginx/html;
  }

  location /b2c {
    root   /usr/share/nginx/html;
    index  index.html index.htm;
    try_files $uri $uri/ /index.html;
    add_header Access-Control-Allow-Origin *;
  }

}