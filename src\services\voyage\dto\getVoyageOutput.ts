import { Moment } from "moment";
import { AuditedEntityDto } from "../../dto/auditedEntityDto";
import { VoyageInitializationSource } from "./voyageInitializationSource";
import { VoyageStatus } from "./voyageStatus";

export interface GetVoyageOutput extends AuditedEntityDto, IVoyageCommonProps {}

export interface IVoyageCommonProps {
	voyageNumber: string;
	qrCode: string;
	secretPin: string;
	destinationFrom: string;
	destinationTo: string;
	planedStart: Moment;
	planedEnd: Moment;
	start: Moment | null;
	end: Moment | null;
	status: VoyageStatus;
	isArchived: boolean;
	isActive: boolean;
	shipId: number;
	deviceId: string;
	initializationSource: VoyageInitializationSource;
	shipName: string;
	callSign: string;
}
