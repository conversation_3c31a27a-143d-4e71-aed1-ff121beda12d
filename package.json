{"name": "reactjs", "version": "4.7.1", "private": true, "dependencies": {"@ant-design/compatible": "^1.1.2", "@ant-design/icons": "^4.8.3", "@aspnet/signalr": "^1.1.4", "@azure/storage-blob": "^12.1.1", "@craco/craco": "5.6.3", "@fluentui/react-icons": "^2.0.221", "@fortawesome/fontawesome-svg-core": "^1.2.28", "@fortawesome/free-solid-svg-icons": "^5.13.0", "@fortawesome/react-fontawesome": "^0.1.9", "abp-web-resources": "^5.1.1", "antd": "4.19", "axios": "^0.19.2", "classnames": "^2.2.6", "craco-antd": "^2.0.0", "eslint-webpack-plugin": "^4.2.0", "famfamfam-flags": "^1.0.0", "less-loader": "^5.0.0", "lodash": "^4.17.15", "mobx": "^5.15.6", "mobx-react": "^6.2.5", "moment": "^2.24.0", "moment-timezone": "^0.5.27", "query-string": "^6.9.0", "react": "^16.12.0", "react-azure-maps": "^0.4.2", "react-document-title": "^2.0.3", "react-dom": "^16.12.0", "react-loadable": "^5.5.0", "react-pdf": "^4.1.0", "react-router-dom": "^5.1.2", "react-scripts": "^5.0.1", "recharts": "^1.8.5", "tslib": "^2.6.2"}, "scripts": {"start": "craco --openssl-legacy-provider start", "build": "craco --openssl-legacy-provider build", "test": "craco test", "eject": "react-scripts eject", "check": "npx @biomejs/biome check  --formatter-enabled=true --linter-enabled=false --changed --organize-imports-enabled=true --write  ./src"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/classnames": "^2.2.9", "@types/jest": "^24.0.23", "@types/moment": "^2.13.0", "@types/moment-timezone": "^0.5.12", "@types/node": "^12.12.14", "@types/react": "^16.9.13", "@types/react-document-title": "^2.0.3", "@types/react-dom": "^16.9.4", "@types/react-loadable": "^5.5.2", "@types/react-pdf": "^4.0.6", "@types/react-router-dom": "^5.1.3", "@types/recharts": "^1.8.3", "copy-webpack-plugin": "^5.0.5", "lefthook": "^1.8.4", "ts-import-plugin": "^1.6.1", "typescript": "^5.7.3"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}