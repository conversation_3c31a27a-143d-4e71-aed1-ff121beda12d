import * as React from "react";

import { Form } from "@ant-design/compatible";
import "@ant-design/compatible/assets/index.css";

import { Input, Modal, Tabs } from "antd";

import { FormComponentProps } from "@ant-design/compatible/lib/form";
import CheckboxGroup from "antd/lib/checkbox/Group";
import FormItem from "antd/lib/form/FormItem";
import ComponentLayout from "../../../../components/Layout/ComponentLayout";
import { L } from "../../../../lib/abpUtility";
import { GetAllPermissionsOutput } from "../../../../services/role/dto/getAllPermissionsOutput";
import RoleStore from "../../../../stores/roleStore";
import { FormWidths } from "../../../ViewSettingsConsts";
import rules from "./createOrUpdateRole.validation";

const TabPane = Tabs.TabPane;

export interface ICreateOrUpdateRoleProps extends FormComponentProps {
	roleStore: RoleStore;
	visible: boolean;
	onCancel: () => void;
	modalType: string;
	onOk: () => void;
	permissions: GetAllPermissionsOutput[];
}

class CreateOrUpdateRole extends React.Component<ICreateOrUpdateRoleProps> {
	state = {
		confirmDirty: false,
	};

	render() {
		const { permissions } = this.props;

		const options = permissions.map((x: GetAllPermissionsOutput) => {
			return { label: x.displayName, value: x.name };
		});

		const { getFieldDecorator } = this.props.form;

		return (
			<Modal
				visible={this.props.visible}
				cancelText={L("Cancel")}
				okText={L("OK")}
				onCancel={this.props.onCancel}
				title={L("Role")}
				onOk={this.props.onOk}
			>
				<Form layout="vertical">
					<Tabs defaultActiveKey={"role"} size={"small"} tabBarGutter={64}>
						<TabPane tab={L("RoleDetails")} key={"role"}>
							<FormItem
								label={L("RoleName")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("name", { rules: rules.name })(
									<Input style={{ width: FormWidths.wide }} />,
								)}
							</FormItem>
							<FormItem
								label={L("DisplayName")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("displayName", { rules: rules.displayName })(
									<Input style={{ width: FormWidths.wide }} />,
								)}
							</FormItem>
							<FormItem
								label={L("Description")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("description")(
									<Input style={{ width: FormWidths.wide }} />,
								)}
							</FormItem>
						</TabPane>
						<TabPane tab={L("RolePermission")} key={"permission"}>
							<FormItem {...ComponentLayout.tailFormItemLayout}>
								{getFieldDecorator("grantedPermissions", {
									valuePropName: "value",
								})(<CheckboxGroup options={options} />)}
							</FormItem>
						</TabPane>
					</Tabs>
				</Form>
			</Modal>
		);
	}
}

export default Form.create<ICreateOrUpdateRoleProps>()(CreateOrUpdateRole);
