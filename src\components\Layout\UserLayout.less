@import '~antd/lib/style/themes/default.less';

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: auto;
  background: @layout-body-background;
}

.lang {
  text-align: right;
  width: 100%;
  height: 40px;
  line-height: 44px;
  :global(.ant-dropdown-trigger) {
    margin-right: 24px;
  }
}

.content {
  padding: 32px 0;
  flex: 1;
}

@media (min-width: @screen-md-min) {
  .container {
    background-repeat: no-repeat;
    background-size: 100%;
  }

  .content {
    padding: 72px 0 24px 0;
  }
}

.top {
  text-align: center;
}

.header {
  height: 44px;
  line-height: 44px;
  a {
    text-decoration: none;
  }
}

.logo {
  height: 44px;
  vertical-align: top;
  margin-right: 16px;
}

.welcome{
  -webkit-box-shadow: -1px 5px 13px -2px rgba(66, 68, 90, 1);
  -moz-box-shadow: -1px 5px 13px -2px rgba(66, 68, 90, 1);
  box-shadow: -1px 5px 13px -2px rgba(66, 68, 90, 1);
  background:white;
  padding:40px; 
  border-radius: 20px;
  display: flex;
  gap: 8px;
  align-items: center;
  width: fit-content;
}

.title {
  font-size: 33px;
  color: @heading-color;
  font-family: 'Myriad Pro', 'Helvetica Neue', Arial, Helvetica, sans-serif;
  font-weight: 600;
  position: relative;
  top: 2px;
}

.desc {
  font-size: @font-size-base;
  color: @text-color-secondary;
  margin-top: 12px;
  margin-bottom: 40px;
}
