import { EntityDto } from "../dto/entityDto";
import { PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoints";
import http from "../httpService";
import { CreateOrUpdateMeterConfigurationInput } from "./dto/CreateOrUpdateMeterConfigurationInput";
import { PagedFilterAndSortedRequestMeterConfiguration } from "./dto/PagedFilterAndSortedRequestMeterConfiguration";
import { UpdateMeterConfigurationInput } from "./dto/UpdateMeterConfigurationInput";

class MeterConfigurationService {
	public async create(
		createMeterConfigurationInput: CreateOrUpdateMeterConfigurationInput,
	) {
		const result = await http.post(
			Endpoint.MeterConfiguration.Create,
			createMeterConfigurationInput,
		);
		return result.data.result;
	}

	public async update(
		updateMeterConfigurationInput: UpdateMeterConfigurationInput,
	) {
		const result = await http.put(
			Endpoint.MeterConfiguration.Update,
			updateMeterConfigurationInput,
		);
		return result.data.result;
	}

	public async delete(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.MeterConfiguration.Delete, {
			params: entityDto,
		});
		return result.data;
	}

	public async get(
		entityDto: EntityDto,
	): Promise<CreateOrUpdateMeterConfigurationInput> {
		const result = await http.get(Endpoint.MeterConfiguration.Get, {
			params: entityDto,
		});
		return result.data.result;
	}

	public async getAll(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequestMeterConfiguration,
	): Promise<PagedResultDto<CreateOrUpdateMeterConfigurationInput>> {
		const result = await http.get(Endpoint.MeterConfiguration.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}

	public async metersForShip(shipId: number, meterType: string) {
		const result = await http.get(Endpoint.MeterConfiguration.MetersForShip, {
			params: { shipId, meterType },
		});
		return result.data.result;
	}

	public async getMeterNames() {
		const result = await http.get(Endpoint.MeterConfiguration.GetMeterNames);
		return result.data.result;
	}

	public async getFilters(
		filters: { keyword: string; searchColumn: string },
		property: string,
	): Promise<string[]> {
		const result = await http.get(Endpoint.MeterConfiguration.Filters, {
			params: { ...filters, propertyName: property },
		});
		return result.data.result;
	}
}

export default new MeterConfigurationService();
