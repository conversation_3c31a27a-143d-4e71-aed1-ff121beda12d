import { PagedFilterAndSortedRequest } from "../dto/pagedFilterAndSortedRequest";
import { PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoints";
import http from "../httpService";
import { GetIntegrationOutput } from "./dto/getIntegrationOutput";

class IntegrationService {
	public async getAll(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequest,
	): Promise<PagedResultDto<GetIntegrationOutput>> {
		const result = await http.get(Endpoint.Integration.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}
}

export default new IntegrationService();
