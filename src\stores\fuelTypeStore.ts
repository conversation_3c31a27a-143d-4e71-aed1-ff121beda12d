import { action, observable } from "mobx";
import { EntityDto } from "../services/dto/entityDto";
import type { PagedResultDto } from "../services/dto/pagedResultDto";
import type { FuelTypeDto } from "../services/fuelType/dto/fuelTypeDto";
import fuelTypeService from "../services/fuelType/fuelTypeService";
import type { PagedMeterResultRequestFuelTypeDto } from "../services/fuelType/dto/pagedFilterAndSortedRequestFuelType";

class FuelTypeStore {
	@observable fuelTypes: PagedResultDto<FuelTypeDto> = {
		items: [],
		totalCount: 0,
	};
	@observable fuelType!: FuelTypeDto;
	@observable editFuelType!: FuelTypeDto;
	@observable filters: string[] = [];

	@action
	async create(createFuelTypeInput: FuelTypeDto) {
		const result = await fuelTypeService.create(createFuelTypeInput);
		this.fuelTypes.items.push(result);
	}

	@action
	async getAll(pagedFilterAndSortedRequest: PagedMeterResultRequestFuelTypeDto) {
		const result = await fuelTypeService.getAll(pagedFilterAndSortedRequest);
		this.fuelTypes = result;
	}

	@action
	async createFuelType() {
		this.editFuelType = {
			id: 0,
			type: "",
			densityValue: 0,
			lcv: 0,
			co2Factor: 0,
			n2oFactor: 0,
			ch4Factor: 0,
			co2Emissions: 0,
			cslip: 0,
		};
	}

	@action
	async get(entityDto: EntityDto) {
		const result = await fuelTypeService.get(entityDto);
		this.editFuelType = result;
	}

		@action
		async getFilters(
			filters: { keyword: string; searchColumn: string },
			property: string,
		) {
			const result = await fuelTypeService.getFilters(filters, property);
	
			this.filters = result;
		}

	@action
	async update(updateFuelType: FuelTypeDto) {
		const result = await fuelTypeService.update(updateFuelType);
		this.editFuelType = result;
	}

	async delete(entityDto: EntityDto) {
		await fuelTypeService.delete(entityDto);
		this.fuelTypes.items = this.fuelTypes.items.filter(
			(x: FuelTypeDto) => x.id !== entityDto.id,
		);
	}
}

export default FuelTypeStore;
