import { action, observable } from "mobx";
import { EntityDto } from "../services/dto/entityDto";
import type { PagedResultDto } from "../services/dto/pagedResultDto";
import type { CreateOrUpdatePassageInput } from "../services/passages/dto/createOrUpdatePassageInput";
import { GetPassagesOutput } from "../services/passages/dto/getPassagesOutput";
import type { PagedPassagesResultRequestDto } from "../services/passages/dto/pagedPassagesResultRequestDto";
import type { UpdatePassageInput } from "../services/passages/dto/updatePassageInput";
import passagesService from "../services/passages/passagesService";

class PassagesStore {
	@observable passages!: PagedResultDto<GetPassagesOutput>;

	@action
	async create(createOrUpdatePassageInput: CreateOrUpdatePassageInput) {
		const result = await passagesService.create(createOrUpdatePassageInput);
		this.passages.items.push(result);
	}

	@action
	async update(updatePassages: UpdatePassageInput) {
		const result = await passagesService.update(updatePassages);
		this.passages.items = this.passages.items.map((x: GetPassagesOutput) => {
			if (x.id === updatePassages.id) x = result;
			return x;
		});
	}

	@action
	async delete(entityDto: EntityDto) {
		await passagesService.delete(entityDto);
		this.passages.items = this.passages.items.filter(
			(x: GetPassagesOutput) => x.id !== entityDto.id,
		);
	}

	@action
	async getAll(pagedFilterAndSortedRequest: PagedPassagesResultRequestDto) {
		const result = await passagesService.getAll(pagedFilterAndSortedRequest);
		this.passages = result;
	}
}

export default PassagesStore;
