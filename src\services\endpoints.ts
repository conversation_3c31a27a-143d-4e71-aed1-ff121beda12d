// biome-ignore lint/complexity/noStaticOnlyClass: <explanation>
export default class Endpoint {
	static Account: AccountEnpoints = {
		IsTenantAvailable: "api/services/app/Account/IsTenantAvailable",
		Register: "api/services/app/Account/Register",
		RequestResetPassword: "api/services/app/Account/RequestResetPassword",
		ResetPassword: "api/services/app/Account/ResetPassword",
	};
	static Chat = {
		GetLastChatInfo: "api/services/app/Chat/GetLastChatInfo",
		SendMessage: "api/services/app/Chat/SendMessage",
		StartThread: "api/services/app/Chat/StartThread",
	} as const;
	static Tenant: TenantsEndpoints = {
		Create: "api/services/app/Tenant/Create",
		Delete: "api/services/app/Tenant/Delete",
		Update: "api/services/app/Tenant/Update",
		Get: "api/services/app/Tenant/Get",
		GetAll: "api/services/app/Tenant/GetAll",
	};
	static MeterReading = {
		Get: "api/services/app/ReadAtSeaMeterReading/Get",
		GetAll: "api/services/app/ReadAtSeaMeterReading/GetAll",
		Filters: "api/services/app/ReadAtSeaMeterReading/GetFilters",
		Create: "api/services/app/ReadAtSeaMeterReading/Create",
		Update: "api/services/app/ReadAtSeaMeterReading/Update",
		Delete: "api/services/app/ReadAtSeaMeterReading/Delete",
		GetMovingAverage: "api/services/app/ReadAtSeaMeterReading/GetMovingAverage",
		GetTenantSummary: "api/services/app/ReadAtSeaMeterReading/GetTenantSummary",
		GetFaultLogs: "api/services/app/ReadAtSeaMeterReading/GetFaultLogs",
		GetFaopPerformance:
			"api/services/app/ReadAtSeaMeterReading/GetFaopPerformance",
		GetLastMeterReadings:
			"api/services/app/ReadAtSeaMeterReading/GetLastMeterReadings",
		UpdateValue: "api/services/app/ReadAtSeaMeterReading/UpdateValue",
		DeleteRange: "api/services/app/MeterReading/DeleteRange",
		GetMeterReadingCountPerDayForAllShips:
			"api/services/app/ReadAtSeaMeterReading/GetMeterReadingCountPerDateForAllShips",
		GetMeterReadingCountForGivenPeriodAndShip:
			"api/services/app/MeterReading/GetMeterReadingCountForGivenPeriodAndShip",
		GetMeterReadingValidationTypes:
			"api/services/app/MeterReading/GetMeterReadingValidationTypes",
		SendCsv: "api/services/app/MeterReading/SendCsv",
		SendAllCsv: "api/services/app/MeterReading/SendAllCsv",
		GetReadingGsStatuses: "/api/services/app/MeterReading/GetReadingGsStatuses",
		ReSendToGs: "/api/services/app/MeterReading/ReSendToGs",
		Download: "/api/services/app/ReadAtSeaMeterReading/Download",
		ToggleStatus: "/api/services/app/ReadAtSeaMeterReading/ToggleStatus",
		GetAllIntegrationSyncMeterReading:
			"/api/services/app/MeterReading/GetAllIntegrationSyncMeterReadingForMeterReading",
		GetScanTriggerReasons:
			"/api/services/app/MeterReading/GetScanTriggerReasons",
	} as const;
	static Meter: any = {
		Get: "api/services/app/Meter/Get",
		GetAll: "api/services/app/Meter/GetAll",
		Create: "api/services/app/Meter/Create",
		Update: "api/services/app/Meter/Update",
		Delete: "api/services/app/Meter/Delete",
		GetMeterTypes: "api/services/app/Meter/GetMeterTypes",
		GetMeasureTypes: "api/services/app/Meter/GetMeasureTypes",
		DownloadQrCodeLink: "api/services/app/Meter/DownloadQrCodeLink",
		GetConsumerTypes: "/api/services/app/Meter/GetConsumerTypes",
		GetFuelTypes: "/api/services/app/Meter/GetFuelTypes",
		GetMeasureUnits: "/api/services/app/Meter/GetMeasureUnits",
		GetMeterNameAndUuids: "/api/services/app/Meter/GetMeterNameAndUuids",
		GetAllForShip: "api/services/app/Meter/getAllForShip",
		GetMeterValidationRules: "/api/services/app/Meter/GetMeterValidationRules",
	};
	static MeterConfiguration = {
		GetAllByShipUuid: "api/services/app/ReadAtSeaMeter/GetAllByShipUuid",
		MetersForShip: "api/services/app/ReadAtSeaMeter/MetersForShip",
		GetMeterNames: "api/services/app/ReadAtSeaMeter/GetMeterNames",
		Get: "api/services/app/ReadAtSeaMeter/Get",
		GetAll: "api/services/app/ReadAtSeaMeter/GetAll",
		Filters: "api/services/app/ReadAtSeaMeter/GetFilters",
		Create: "api/services/app/ReadAtSeaMeter/Create",
		Update: "api/services/app/ReadAtSeaMeter/Update",
		Delete: "api/services/app/ReadAtSeaMeter/Delete",
	} as const;
	static MeterReadingSet: any = {
		ExportPerformanceReport:
			"api/services/app/ReadAtSeaMeterReadingSet/ExportPerformanceReport",
		ExportEmissionReport:
			"api/services/app/ReadAtSeaMeterReadingSet/ExportEmissionReport",
		GetEmissionReport:
			"api/services/app/ReadAtSeaMeterReadingSet/GetEmissionReport",
		GetPerformanceReport:
			"api/services/app/ReadAtSeaMeterReadingSet/GetPerformanceReport",
		GetMeterNames: "api/services/app/ReadAtSeaMeter/GetMeterNames",
		Get: "api/services/app/ReadAtSeaMeterReadingSet/Get",
		GetAll: "api/services/app/ReadAtSeaMeterReadingSet/GetAll",
		Create: "api/services/app/ReadAtSeaMeterReadingSet/Create",
		Update: "api/services/app/ReadAtSeaMeterReadingSet/Update",
		Delete: "api/services/app/ReadAtSeaMeterReadingSet/Delete",
	};
	static Cargo = {
		Get: "api/services/app/Cargo/Get",
		GetGroupByCounterparty: "api/services/app/Cargo/GetGroupByCounterparty",
		GetAll: "api/services/app/Cargo/GetAll",
		Filters: "api/services/app/Cargo/GetFilters",
		Create: "api/services/app/Cargo/Create",
		Update: "api/services/app/Cargo/Update",
		Delete: "api/services/app/Cargo/Delete",
	} as const;
	static Passage: any = {
		Get: "api/services/app/ReadAtSeaPassage/Get",
		GetAll: "api/services/app/ReadAtSeaPassage/GetAll",
		Create: "api/services/app/ReadAtSeaPassage/Create",
		Update: "api/services/app/ReadAtSeaPassage/Update",
		Delete: "api/services/app/ReadAtSeaPassage/Delete",
	};
	static Port = {
		GetAll: "api/services/app/ReadAtSeaPorts/GetAll",
		GetByCode: "api/services/app/ReadAtSeaPorts/GetByCode",
	} as const;
	static Ship = {
		Get: "api/services/app/ReadAtSeaShip/Get",
		GetAll: "api/services/app/ReadAtSeaShip/GetAll",
		Create: "api/services/app/ReadAtSeaShip/Create",
		Update: "api/services/app/ReadAtSeaShip/Update",
		Filters: "api/services/app/ReadAtSeaShip/GetFilters",
		Delete: "api/services/app/ReadAtSeaShip/Delete",
		GetShipNames: "api/services/app/ReadAtSeaShip/GetShipNames",
		GetAllWithIdAndName: "api/services/app/Ship/GetAllWithIdAndName",
	} as const;
	static ShipInfo: any = {
		Get: "api/services/app/ReadAtSeaShipInfo/Get",
		GetAll: "api/services/app/ReadAtSeaShipInfo/GetAll",
		Create: "api/services/app/ReadAtSeaShipInfo/Create",
		Update: "api/services/app/ReadAtSeaShipInfo/Update",
		Delete: "api/services/app/ReadAtSeaShipInfo/Delete",
	};
	static ShipRental = {
		Get: "api/services/app/ReadAtSeaShipRental/Get",
		GetRentalGroupyByCounterparty:
			"api/services/app/ReadAtSeaShipRental/GetRentalGroupyByCounterparty",
		GetAll: "api/services/app/ReadAtSeaShipRental/GetAll",
		Create: "api/services/app/ReadAtSeaShipRental/Create",
		Update: "api/services/app/ReadAtSeaShipRental/Update",
		Filters: "api/services/app/ReadAtSeaShipRental/GetFilters",
		Delete: "api/services/app/ReadAtSeaShipRental/Delete",
	} as const;
	static DataSource: any = {
		Get: "api/services/app/ReadAtSeaDataSource/Get",
		GetAll: "api/services/app/ReadAtSeaDataSource/GetAll",
		Create: "api/services/app/ReadAtSeaDataSource/Create",
		Update: "api/services/app/ReadAtSeaDataSource/Update",
		Delete: "api/services/app/ReadAtSeaDataSource/Delete",
	};
	static DataProvider: any = {
		Get: "api/services/app/ReadAtSeaDataProvider/Get",
		GetAll: "api/services/app/ReadAtSeaDataProvider/GetAll",
		Create: "api/services/app/ReadAtSeaDataProvider/Create",
		Update: "api/services/app/ReadAtSeaDataProvider/Update",
		Delete: "api/services/app/ReadAtSeaDataProvider/Delete",
	};
	static Events: any = {
		GetCargo: "api/services/app/ReadAtSeaEvents/GetCargo",
		Get: "api/services/app/ReadAtSeaEvents/Get",
		GetAll: "api/services/app/ReadAtSeaEvents/GetAll",
		Create: "api/services/app/ReadAtSeaEvents/Create",
		Update: "api/services/app/ReadAtSeaEvents/Update",
		Delete: "api/services/app/ReadAtSeaEvents/Delete",
		RequestMeterReadings:
			"api/services/app/ReadAtSeaEvents/RequestMeterReading",
	};
	static Notifications = {
		GetNotificationsSummary:
			"api/services/app/ReadAtSeaEvents/GetNotificationSummary",
		Get: "api/services/app/ReadAtSeaEvents/Get",
		GetAll: "api/services/app/ReadAtSeaEvents/GetAll",
		Filters: "api/services/app/ReadAtSeaEvents/GetFilters",
		Create: "api/services/app/ReadAtSeaEvents/Create",
		Update: "api/services/app/ReadAtSeaEvents/Update",
		UpdateCargo: "api/services/app/ReadAtSeaEvents/UpdateCargo",
		Delete: "api/services/app/ReadAtSeaEvents/Delete",
		ShipTrace: "api/services/app/ReadAtSeaEvents/ShipTrace",
		GetTitles: "api/services/app/ReadAtSeaEvents/GetTitles",
	} as const;

	static AIS: any = {
		ECABorders: "api/services/app/AIS/GetEcaBorders",
	};

	static ReadAtSeaInfo = {
		GetShipLastPositions: "api/services/app/ReadAtSeaShipInfo/GetLastPositions",
		GetShipLastPosition: "api/services/app/ReadAtSeaShipInfo/GetLastPosition",
	} as const;

	static AnswerForEvents: any = {
		Get: "api/services/app/ReadAtSeaAnswerForEvents/Get",
		GetAll: "api/services/app/ReadAtSeaAnswerForEvents/GetAll",
		Create: "api/services/app/ReadAtSeaAnswerForEvents/Create",
		Update: "api/services/app/ReadAtSeaAnswerForEvents/Update",
		Delete: "api/services/app/ReadAtSeaAnswerForEvents/Delete",
	};
	static Device: any = {
		Get: "api/services/app/Device/Get",
		GetAll: "api/services/app/Device/GetAll",
		Create: "api/services/app/Device/Create",
		Update: "api/services/app/Device/Update",
		Delete: "api/services/app/Device/Delete",
		GetDeviceStatuses: "api/services/app/Device/GetDeviceStatuses",
	};
	static ConsumptionConsumer: any = {
		Get: "api/services/app/ConsumptionConsumer/Get",
		GetAll: "api/services/app/ConsumptionConsumer/GetAll",
		Create: "api/services/app/ConsumptionConsumer/Create",
		Update: "api/services/app/ConsumptionConsumer/Update",
		Delete: "api/services/app/ConsumptionConsumer/Delete",
		GetAllForShip: "api/services/app/ConsumptionConsumer/GetAllForShip",
		GetConsumerTypes: "api/services/app/ConsumptionConsumer/GetConsumerTypes",
		GetMeasureUnits: "/api/services/app/ConsumptionConsumer/GetMeasureUnits",
		GetNames: "/api/services/app/ConsumptionConsumer/GetNames",
	};
	static ConsumptionResult = {
		Get: "api/services/app/ConsumptionResult/Get",
		GetAll: "api/services/app/ConsumptionResult/GetAll",
		Filters: "api/services/app/ConsumptionResult/GetFilters",
		Create: "api/services/app/ConsumptionResult/Create",
		Update: "api/services/app/ConsumptionResult/Update",
		Delete: "api/services/app/ConsumptionResult/Delete",
		ReSendToGs: "/api/services/app/ConsumptionResult/ReSendToGs",
		SendCsv: "api/services/app/ConsumptionResult/SendCsv",
		SendAllCsv: "api/services/app/ConsumptionResult/SendAllCsv",
	} as const;
	static ConsumptionCalculation: any = {
		CalculateResults:
			"api/services/app/ConsumptionCalculation/CalculateResults",
		SaveAutoCalculationResults:
			"api/services/app/ConsumptionCalculation/SaveAutoCalculationResults",
		GetConfig: "api/services/app/ConsumptionCalculation/GetConfig",
		SetGroupingPeriod:
			"api/services/app/ConsumptionCalculation/SetGroupingPeriod",
	};
	static FastField: any = {
		Send: "api/services/app/FastField/Send",
	};
	static Voyage: any = {
		Get: "api/services/app/Voyage/Get",
		GetAll: "api/services/app/Voyage/GetAll",
		Create: "api/services/app/Voyage/Create",
		Update: "api/services/app/Voyage/Update",
		Delete: "api/services/app/Voyage/Delete",
		GetVoyageStatuses: "api/services/app/Voyage/GetVoyageStatuses",
		DownloadQrCodeLink: "api/services/app/Voyage/DownloadQrCodeLink",
		GetAllWithIdAndNumber: "api/services/app/Voyage/GetAllWithIdAndNumber",
		GetVoyageInitializationSources:
			"api/services/app/Voyage/GetVoyageInitializationSources",
	};
	static MailRecipient: any = {
		Get: "api/services/app/MailRecipientService/Get",
		GetAll: "api/services/app/MailRecipientService/GetAll",
		Create: "/api/services/app/MailRecipientService/Create",
		Update: "api/services/app/MailRecipientService/Update",
		Delete: "api/services/app/MailRecipientService/Delete",
		GetMailRecipientLevels:
			"api/services/app/MailRecipientService/GetMailRecipientLevels",
	};
	static Draft: any = {
		Get: "api/services/app/Draft/Get",
		GetAll: "api/services/app/Draft/GetAll",
		Delete: "api/services/app/Draft/Delete",
		ReSendToGs: "/api/services/app/Draft/ReSendToGs",
		SendCsv: "api/services/app/Draft/SendCsv",
		SendAllCsv: "api/services/app/Draft/SendAllCsv",
	};

	static Customer: any = {
		GetByTenantId: "api/services/app/Customer/GetByTenantId",
		GetAll: "api/services/app/Customer/GetAll",
		Create: "/api/services/app/Customer/Create",
		Update: "api/services/app/Customer/Update",
		DeleteByTenant: "api/services/app/Customer/DeleteByTenant",
	};

	static BunkeringNotes = {
		Get: "api/services/app/ReadAtSeaBunkeringNotes/Get",
		GetAll: "api/services/app/ReadAtSeaBunkeringNotes/GetAll",
		Filters: "api/services/app/ReadAtSeaBunkeringNotes/GetFilters",
		Create: "api/services/app/ReadAtSeaBunkeringNotes/Create",
		Update: "api/services/app/ReadAtSeaBunkeringNotes/Update",
		Delete: "api/services/app/ReadAtSeaBunkeringNotes/Delete",
	} as const;

	static Ocr = {
		AnalyzeFile: "api/services/app/OCR/AnalyzeFile",
		ChangeStatus: "api/services/app/OCR/ChangeStatus",
		GetAllByStatus: "api/services/app/OCR/GetAllByStatus",
		Get: "api/services/app/OCR/Get",
		GetAll: "api/services/app/OCR/GetAll",
		CreateMany: "api/services/app/OCR/CreateMany",
		Update: "api/services/app/OCR/Update",
		Delete: "api/services/app/OCR/Delete",
		VerifyFile: "api/services/app/OCR/VerifyFile",
		DenyFile: "api/services/app/OCR/DenyFile",
		MoveFile: "api/services/app/OCR/MoveFile",
	} as const;

	static Storage = {
		GetSassTokenForMeter:
			"api/services/app/StorageService/GetSassTokenForMeter",
		GetSasTokenForOCR: "api/services/app/StorageService/GetSasTokenForOCR",
		UploadBunkeringNotes:
			"api/services/app/StorageService/UploadBunkeringDocument",
	} as const;

	static Integration: any = {
		GetAll: "api/services/app/Integration/GetAll",
	};

	static Integrator: any = {
		GetAll: "api/services/app/Integrator/GetAll",
	};

	static FuelType = {
		Create: "api/services/app/FuelType/Create",
		Get: "api/services/app/FuelType/Get",
		GetAll: "api/services/app/FuelType/GetAll",
		Filters: "api/services/app/FuelType/GetFilters",
		Update: "api/services/app/FuelType/Update",
		Delete: "api/services/app/FuelType/Delete",
	} as const;

	static User = {
		Create: "api/services/app/User/Create",
		Update: "api/services/app/User/Update",
		Delete: "api/services/app/User/Delete",
		GetRoles: "api/services/app/User/GetRoles",
		ChangeLanguage: "api/services/app/User/ChangeLanguage",
		Get: "api/services/app/User/Get",
		GetAll: "api/services/app/User/GetAll",
		GetCharteres: "api/services/app/User/GetCharterers",
	} as const;
}

interface BaseEnpoints {
	Create: string;
	Delete: string;
	Update: string;
	Get: string;
	GetAll: string;
}

interface AccountEnpoints {
	IsTenantAvailable: string;
	Register: string;
	RequestResetPassword: string;
	ResetPassword: string;
}
interface TenantsEndpoints extends BaseEnpoints {}
