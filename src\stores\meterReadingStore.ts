import { action, observable } from "mobx";

import moment from "moment-timezone";
import type Dictionary from "../services/dictionary";
import { EntityDto } from "../services/dto/entityDto";
import type {
	ListResultDto,
	PagedResultDto,
} from "../services/dto/pagedResultDto";
import type { PagedMeterReadingResultRequestDto } from "../services/meterReading/dto/PagedMeterReadingResultRequestDto";
import type { CreateOrUpdateMeterReadingInput } from "../services/meterReading/dto/createOrUpdateMeterReadingInput";
import { GetFaopPerformance } from "../services/meterReading/dto/getFaopPerformance";
import { GetFaultLogs } from "../services/meterReading/dto/getFaultLogs";
import { GetLastMeterReadings } from "../services/meterReading/dto/getLastMeterReadings";
import { GetMeterReadingOutput } from "../services/meterReading/dto/getMeterReadingOutput";
import type { GetMovingAverageOutput } from "../services/meterReading/dto/getMovingAverageOutput";
import { MeterReadingCountPerDayForShipOutDto } from "../services/meterReading/dto/meterReadingCountPerDayForShipOutDto";
import { SyncInformationMeterReadingDto } from "../services/meterReading/dto/syncInformationMeterReadingDto";
import type { TenantSummary } from "../services/meterReading/dto/tenantSummary";
import type { UpdateValue } from "../services/meterReading/dto/updateValue";
import meterReadingService from "../services/meterReading/meterReadingService";
import { GetRoles } from "../services/user/dto/getRolesOuput";
import utils from "../utils/utils";

class MeterReadingStore {
	@observable meterReadings!: PagedResultDto<GetMeterReadingOutput>;
	@observable editMeterReader!: CreateOrUpdateMeterReadingInput;
	@observable roles: GetRoles[] = [];
	@observable filters: string[] = [];
	@observable staticCountPerDays: MeterReadingCountPerDayForShipOutDto[] = [];
	@observable shipStatisticCountInPeriod!: number;
	@observable meterReadingValidationTypes!: { [key: number]: string };
	@observable readingGsStatuses!: Dictionary<number, string>;
	@observable scanTriggerReasons!: Dictionary<number, string>;
	@observable
	syncInformationMeterReadings!: PagedResultDto<SyncInformationMeterReadingDto>;
	@observable movingAverage!: GetMovingAverageOutput;
	@observable lastReadings!: Array<GetLastMeterReadings>;
	@observable faopPerformance!: Array<GetFaopPerformance>;
	@observable faultLogs!: Array<GetFaultLogs>;
	@observable tenantSummary!: TenantSummary;
	@observable fileUrl!: { [key: string]: string };
	@observable meterNames!: { guid: string; name: string }[];

	@action
	async create(createMeterReadingInput: CreateOrUpdateMeterReadingInput) {
		const result = await meterReadingService.create(createMeterReadingInput);
		this.meterReadings.items.push(result);
	}

	@action
	async updateValue(updateMeterReadingInput: UpdateValue) {
		await meterReadingService.updateValue(updateMeterReadingInput);
	}

	@action
	async update(updateMeterReadingInput: CreateOrUpdateMeterReadingInput) {
		const result = await meterReadingService.update(updateMeterReadingInput);
		this.meterReadings.items = this.meterReadings.items.map(
			(x: GetMeterReadingOutput) => {
				if (x.id === updateMeterReadingInput.id) x = result;
				return x;
			},
		);
	}

	@action
	async delete(entityDto: EntityDto) {
		await meterReadingService.delete(entityDto);
		this.meterReadings.items = this.meterReadings.items.filter(
			(x: GetMeterReadingOutput) => x.id !== entityDto.id,
		);
	}

	@action
	async getFilters(
		filters: { keyword: string; searchColumn: string },
		property: string,
	) {
		const result = await meterReadingService.getFilters(filters, property);

		this.filters = result;
	}

	@action
	async deleteRange(entitiesDto: ListResultDto<EntityDto>) {
		await meterReadingService.deleteRange(entitiesDto);
		entitiesDto.items.forEach(
			(entityDto: EntityDto) =>
				(this.meterReadings.items = this.meterReadings.items.filter(
					(x: GetMeterReadingOutput) => x.id !== entityDto.id,
				)),
		);
	}

	@action
	async get(entityDto: EntityDto) {
		const result = await meterReadingService.get(entityDto);
		updateLocalTime(result);
		this.editMeterReader = result;
	}

	@action
	async getMeterReadingValidationTypes() {
		const result = await meterReadingService.getMeterReadingValidationTypes();
		this.meterReadingValidationTypes = result;
	}

	@action
	async getMovingAverage() {
		const result = await meterReadingService.getMovingAverage();
		this.movingAverage = result;
	}

	@action
	async getFaopPerformance(daysPeriod: number) {
		const result = await meterReadingService.getFaopPerformance(daysPeriod);
		this.faopPerformance = result;
	}

	@action
	async getFaultLogs(daysPeriod: number) {
		const result = await meterReadingService.getFaultLogs(daysPeriod);
		this.faultLogs = result;
	}

	@action
	async getLastMeterReadings() {
		const result = await meterReadingService.getLastMeterReadings();
		this.lastReadings = result;
	}

	@action
	async getTenantSummary() {
		const result = await meterReadingService.getTenantSummary();
		this.tenantSummary = result;
	}

	@action
	async getMeterNames() {
		const result = await meterReadingService.getMeterNames();
		this.meterNames = result;
	}

	@action
	async createMeterReading() {
		this.editMeterReader = {
			auditMetrics: {
				consumption: false,
				digits: false,
				fuelType: false,
				lower: false,
				overwritten: false,
				aiDiscrepancy: false,
				resultEdited: false,
				checked: false,
			},
			aiValue: "",
			id: 0,
			value: "",
			reasonForEvent: "",
			manualValue: "",
			callSign: "",
			voyageNumber: "",
			meterBarcode: "",
			fullImageUrl: "",
			readingImageUrl: "",
			roleNames: [],
			version: "",
			deviceId: "",
			localTime: moment(),
			timeZone: "",
			creationTime: moment(),
			creatorUserId: 0,
			lastModificationTime: moment(),
			lastModifierUserId: 0,
			unixTime: 0,
			validation: 0,
			isMadeManually: false,
			consumerType: 0,
			measureType: 0,
			vesselOperator: "",
			meterName: "",
			uuid: "",
			meterInfromation: "",
			fuelType: "",
			measureUnit: 0,
			readingGsStatus: 0,
			meterUuid: "",
			fileUrl: "",
			editValue: "",
			isFuelChanged: false,
			scanTriggerReason: 0,
			vesselStatus: "",
			scrubber: false,
			meter: {
				name: "",
				meterType: "",
				unitOfMeasure: "",
				measurementType: "",
				digitsCount: 0,
				decimalPointPosition: 0,
				flowDirection: "",
				scheduledReadings: {
					0: "",
				},
				ship: {
					shipName: "",
					imoNumber: "",
				},
			},
		};
		this.roles = [];
	}

	@action
	async getAll(pagedFilterAndSortedRequest: PagedMeterReadingResultRequestDto) {
		const result = await meterReadingService.getAll(
			pagedFilterAndSortedRequest,
		);
		result.items.forEach((x) => updateLocalTime(x));
		this.meterReadings = result;
	}

	@action
	async getAllIntegrationSyncMeterReading(entityDto: EntityDto) {
		const result =
			await meterReadingService.getAllIntegrationSyncMeterReading(entityDto);
		this.syncInformationMeterReadings = result;
	}

	@action
	async loadStatisticCountPerDays() {
		const result =
			await meterReadingService.getMeterReadingCountPerDayForAllShips();
		this.staticCountPerDays = result;
	}

	@action
	async getReadingGsStatuses() {
		const result = await meterReadingService.getReadingGsStatuses();
		this.readingGsStatuses = result;
	}
	@action
	async getScanTriggerReasons() {
		const result = await meterReadingService.getScanTriggerReasons();
		this.scanTriggerReasons = result;
	}

	@action
	async download(from?: string, to?: string) {
		const result = await meterReadingService.download(from, to);
		utils.downloadURI(result, "EmissionReport.xlsx");
	}

	@action
	async toggleStatus(id: EntityDto["id"]) {
		await meterReadingService.toggleStatus(id);
	}

	@action
	async resendToGs(entities: ListResultDto<EntityDto>) {
		const result: Array<GetMeterReadingOutput> =
			await meterReadingService.resendToGs(entities);

		this.meterReadings.items = this.meterReadings.items.map((item) => {
			if (result.map((x) => x.id).includes(item.id)) {
				item.readingGsStatus =
					result.find((x) => x.id === item.id)?.readingGsStatus ??
					item.readingGsStatus;
			}
			return item;
		});
	}
}

const updateLocalTime = (meterReading: GetMeterReadingOutput) => {
	const unixTime = moment(meterReading.unixTime);
	const utcTime = moment.tz(unixTime, "Europe/London");
	const localTime = utcTime.clone().tz(meterReading.timeZone);
	meterReading.localTime = localTime;
};

export default MeterReadingStore;
