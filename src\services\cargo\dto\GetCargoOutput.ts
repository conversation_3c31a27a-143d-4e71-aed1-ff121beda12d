import { Moment } from "moment";

export interface GetCargoOutput {
	id: number;
	creationTime: Date;
	dateOfTransport: Moment;
	quantity: number;
	status: string;
	cargoUnit: number;
	units: string;
	counterparty: {
		id: number;
		userName: string;
		name: string;
		surname: string;
		emailAddress: string;
		isActive: boolean;
		fullName: string;
		rank: string;
		lastLoginTime: Date;
		creationTime: Date;
		roleNames: string[];
	};
	ship: {
		id: number;
		shipName: string;
		mmsi: string;
		imoNumber: string;
	};
}
