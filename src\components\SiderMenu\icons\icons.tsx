import * as React from "react";

export const dashboardIcon = () => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M8.38835 0.543673C9.31937 -0.181225 10.6806 -0.181224 11.6117 0.543674L19.1116 6.38327C19.6749 6.82186 20 7.46755 20 8.14789V18.2047C20 19.1962 19.1294 20 18.0556 20H14.1667C13.0928 20 12.2222 19.1962 12.2222 18.2047V12.5622C12.2222 12.4205 12.0979 12.3057 11.9444 12.3057H8.05555C7.90214 12.3057 7.77777 12.4205 7.77777 12.5622V18.2047C7.77777 19.1962 6.90722 20 5.83333 20H1.94444C0.870558 20 0 19.1962 0 18.2047V8.14789C0 7.46755 0.325057 6.82186 0.888353 6.38327L8.38835 0.543673ZM10.5372 1.72009C10.2269 1.47846 9.77312 1.47846 9.46279 1.72009L1.96278 7.55968C1.77502 7.70588 1.66667 7.92111 1.66667 8.14789V18.2047C1.66667 18.3463 1.79103 18.4611 1.94444 18.4611H5.83333C5.98674 18.4611 6.11111 18.3463 6.11111 18.2047V12.5622C6.11111 11.5706 6.98166 10.7668 8.05555 10.7668H11.9444C13.0183 10.7668 13.8889 11.5706 13.8889 12.5622V18.2047C13.8889 18.3463 14.0133 18.4611 14.1667 18.4611H18.0556C18.209 18.4611 18.3333 18.3463 18.3333 18.2047V8.14789C18.3333 7.92111 18.225 7.70588 18.0372 7.55968L10.5372 1.72009Z"
		/>
	</svg>
);

export const mapIcon = () => (
	<svg
		width="18"
		height="18"
		viewBox="0 0 18 18"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M14.1478 1.71429C12.9213 1.71429 12.0099 2.64309 12.0099 3.68944C12.0099 3.95046 12.1195 4.31075 12.3686 4.75572C12.6109 5.18846 12.9454 5.63153 13.2984 6.03959C13.5982 6.38606 13.8999 6.6942 14.1478 6.93391C14.3957 6.6942 14.6973 6.38606 14.9971 6.03959C15.3502 5.63153 15.6847 5.18846 15.927 4.75572C16.1761 4.31075 16.2857 3.95046 16.2857 3.68944C16.2857 2.64309 15.3743 1.71429 14.1478 1.71429ZM14.1478 8.09524C13.5963 8.75137 13.5961 8.75126 13.596 8.75114L13.5922 8.74792L13.5838 8.74081L13.5547 8.71586C13.5299 8.69457 13.4948 8.66404 13.4508 8.62505C13.3628 8.54713 13.2389 8.43509 13.0909 8.29514C12.796 8.01628 12.4003 7.62156 12.002 7.16124C11.6063 6.70387 11.1921 6.1635 10.8728 5.59314C10.5603 5.03501 10.2956 4.37252 10.2956 3.68944C10.2956 1.60732 12.066 0 14.1478 0C16.2295 0 18 1.60732 18 3.68944C18 4.37252 17.7353 5.03501 17.4228 5.59314C17.1035 6.1635 16.6892 6.70387 16.2935 7.16124C15.8953 7.62156 15.4996 8.01628 15.2047 8.29514C15.0567 8.43509 14.9327 8.54713 14.8448 8.62505C14.8007 8.66404 14.7656 8.69457 14.7409 8.71586L14.7118 8.74081L14.7034 8.74792L14.7008 8.75011L14.6999 8.75085C14.6998 8.75097 14.6993 8.75137 14.1478 8.09524ZM14.1478 8.09524L14.6999 8.75085C14.3811 9.01887 13.9148 9.01916 13.596 8.75114L14.1478 8.09524ZM0.837358 3.00666C1.48166 2.2711 2.43907 1.80952 3.49986 1.80952H7.90437C8.37776 1.80952 8.76152 2.19328 8.76152 2.66667C8.76152 3.14005 8.37776 3.52381 7.90437 3.52381H3.49986C3.24996 3.52381 3.01342 3.57233 2.79919 3.65935L14.781 15.3569C14.9058 15.1237 14.9754 14.8615 14.9754 14.5882V10.7563C14.9754 10.2829 15.3591 9.89916 15.8325 9.89916C16.3059 9.89916 16.6897 10.2829 16.6897 10.7563V14.5882C16.6897 16.4997 15.0951 18 13.1898 18H3.49985C1.59456 18 0 16.4997 0 14.5882V5.22129C0 4.37364 0.317967 3.59962 0.837358 3.00666ZM13.2764 16.2838L8.37236 11.4961L3.41928 16.284C3.44596 16.2852 3.47282 16.2857 3.49985 16.2857H13.1898C13.2189 16.2857 13.2478 16.2851 13.2764 16.2838ZM1.90976 15.3589L7.1452 10.298L1.72801 5.00937C1.71894 5.07884 1.71429 5.14957 1.71429 5.22129V14.5882C1.71429 14.8624 1.78427 15.1253 1.90976 15.3589ZM14.1478 2.84081C14.6212 2.84081 15.0049 3.22457 15.0049 3.69795V3.75238C15.0049 4.22577 14.6212 4.60952 14.1478 4.60952C13.6744 4.60952 13.2906 4.22577 13.2906 3.75238V3.69795C13.2906 3.22457 13.6744 2.84081 14.1478 2.84081Z"
		/>
	</svg>
);

export const meterConfigurationIcon = (
	props?: React.SVGProps<SVGSVGElement>,
) => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
		{...props}
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M5.93413 14.0659C6.22703 14.3588 6.22703 14.8336 5.93413 15.1265C5.64124 15.4194 5.16637 15.4194 4.87347 15.1265C2.04217 12.2952 2.04217 7.70478 4.87347 4.87348C6.71833 3.02862 9.30991 2.38674 11.6723 2.94459C12.0755 3.03978 12.3251 3.44375 12.2299 3.84687C12.1347 4.25 11.7308 4.49963 11.3276 4.40444C9.44997 3.96106 7.39622 4.47205 5.93413 5.93414C3.68862 8.17965 3.68862 11.8203 5.93413 14.0659ZM15.8879 7.1415C16.2789 7.00477 16.7067 7.21089 16.8435 7.60189C17.7333 10.1463 17.1624 13.0907 15.1265 15.1265C14.8336 15.4194 14.3588 15.4194 14.0659 15.1265C13.773 14.8336 13.773 14.3588 14.0659 14.0659C15.6791 12.4526 16.1344 10.1183 15.4276 8.09704C15.2908 7.70604 15.4969 7.27824 15.8879 7.1415ZM13.8791 4.66732C14.1062 4.47297 14.439 4.46653 14.6734 4.65195C14.9078 4.83738 14.9781 5.16278 14.8412 5.42842L14.7119 5.67862C14.6295 5.83801 14.5113 6.06624 14.3681 6.34179C14.0818 6.89278 13.6954 7.63339 13.2955 8.39123C12.8959 9.14854 12.4815 9.92535 12.1395 10.5479C11.9686 10.8589 11.8142 11.1344 11.6879 11.3509C11.5703 11.5524 11.4548 11.7421 11.3688 11.8508C10.7263 12.6629 9.54711 12.8004 8.735 12.1579C7.92288 11.5154 7.78538 10.3362 8.42788 9.52412C8.51387 9.41543 8.67195 9.25932 8.84093 9.09862C9.02262 8.92581 9.25522 8.71214 9.51854 8.47435C10.0457 7.9983 10.7063 7.41631 11.3514 6.85315C11.9969 6.28961 12.6288 5.74321 13.0991 5.33783C13.3343 5.1351 13.5292 4.96755 13.6654 4.85065L13.8791 4.66732ZM20 10C20 15.5228 15.5228 20 10 20C4.47715 20 0 15.5228 0 10C0 4.47715 4.47715 0 10 0C15.5228 0 20 4.47715 20 10ZM1.5 10C1.5 14.6944 5.30558 18.5 10 18.5C14.6944 18.5 18.5 14.6944 18.5 10C18.5 5.30558 14.6944 1.5 10 1.5C5.30558 1.5 1.5 5.30558 1.5 10Z"
		/>
	</svg>
);

export const controlBoardIcon = (props?: React.SVGProps<SVGSVGElement>) => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			d="M16.3889 0C18.3833 0 20 1.61675 20 3.61111V16.3889C20 18.3833 18.3833 20 16.3889 20H3.61111C1.61675 20 0 18.3833 0 16.3889V3.61111C0 1.61675 1.61675 0 3.61111 0H16.3889ZM18.3333 6.11111H1.66667V16.3889C1.66667 17.4628 2.53722 18.3333 3.61111 18.3333H16.3889C17.4628 18.3333 18.3333 17.4628 18.3333 16.3889V6.11111ZM16.3889 1.66667H3.61111C2.53722 1.66667 1.66667 2.53722 1.66667 3.61111V4.44444H18.3333V3.61111C18.3333 2.53722 17.4628 1.66667 16.3889 1.66667Z"
			fill="currentColor"
		/>
		<path
			fill-rule="evenodd"
			fill="currentColor"
			clip-rule="evenodd"
			d="M13.7502 9.96856C13.4091 10.5837 12.7532 11 12 11C11.2468 11 10.5909 10.5837 10.2498 9.96856C10.1699 9.98908 10.0862 10 10 10H4C3.44772 10 3 9.55228 3 9C3 8.44772 3.44772 8 4 8H10C10.0862 8 10.1699 8.01092 10.2498 8.03144C10.5909 7.41634 11.2468 7 12 7C12.7532 7 13.4091 7.41634 13.7502 8.03144C13.8301 8.01092 13.9138 8 14 8H16C16.5523 8 17 8.44772 17 9C17 9.55228 16.5523 10 16 10H14C13.9138 10 13.8301 9.98908 13.7502 9.96856Z"
		/>
		<path
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M6.24975 14.0314C6.59087 13.4163 7.24681 13 8 13C8.75319 13 9.40913 13.4163 9.75025 14.0314C9.83008 14.0109 9.91376 14 10 14L16 14C16.5523 14 17 14.4477 17 15C17 15.5523 16.5523 16 16 16L10 16C9.91376 16 9.83008 15.9891 9.75025 15.9686C9.40913 16.5837 8.75319 17 8 17C7.24681 17 6.59087 16.5837 6.24975 15.9686C6.16992 15.9891 6.08624 16 6 16L4 16C3.44771 16 3 15.5523 3 15C3 14.4477 3.44772 14 4 14L6 14C6.08624 14 6.16992 14.0109 6.24975 14.0314Z"
			fill="currentColor"
		/>
	</svg>
);

export const meterReadingsIcon = (props?: React.SVGProps<SVGSVGElement>) => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
		{...props}
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M11.0661 17.6334C15.3919 17.3859 18.8235 13.7996 18.8235 9.41176C18.8235 4.86354 15.1365 1.17647 10.5882 1.17647C6.20041 1.17647 2.61408 4.60806 2.36658 8.93389C1.94234 9.14948 1.54506 9.41048 1.18112 9.71047C1.17803 9.61128 1.17647 9.5117 1.17647 9.41176C1.17647 4.21379 5.39026 0 10.5882 0C15.7862 0 20 4.21379 20 9.41176C20 14.6097 15.7862 18.8235 10.5882 18.8235C10.4883 18.8235 10.3887 18.822 10.2895 18.8189C10.5895 18.4549 10.8505 18.0577 11.0661 17.6334ZM13.4893 3.59604C13.3713 3.89875 13.0303 4.04853 12.7276 3.93058C10.6177 3.10848 8.13095 3.55024 6.42879 5.2524C5.57664 6.10455 5.0406 7.15311 4.82066 8.25235C4.39908 8.28284 3.9887 8.35372 3.59336 8.46113C3.79312 6.98322 4.46097 5.55645 5.5969 4.42051C7.64092 2.3765 10.6256 1.84894 13.1547 2.83438C13.4575 2.95233 13.6072 3.29334 13.4893 3.59604ZM9.02451 9.4182C9.86823 10.0145 10.5629 10.8077 11.0417 11.7309C11.4148 11.6554 11.7662 11.4592 12.0326 11.1473C12.1173 11.0481 12.2326 10.8738 12.3503 10.6885C12.4767 10.4892 12.6317 10.2355 12.8033 9.9491C13.1469 9.37564 13.564 8.65965 13.9666 7.96148C14.3695 7.26282 14.759 6.57988 15.0477 6.07176C15.192 5.81765 15.3112 5.60716 15.3944 5.46016L15.5248 5.2294C15.6629 4.98438 15.6082 4.67586 15.3944 4.4932C15.1805 4.31054 14.8672 4.30487 14.6468 4.47965L14.4393 4.64455C14.3071 4.74969 14.1179 4.9004 13.8895 5.08278C13.4329 5.44747 12.8193 5.93909 12.1924 6.44636C11.5658 6.95327 10.924 7.47736 10.4114 7.90651C10.1553 8.12087 9.92906 8.31363 9.75208 8.46975C9.58749 8.61494 9.4333 8.75619 9.3486 8.85538C9.20238 9.0266 9.09463 9.21772 9.02451 9.4182ZM16.4041 6.51084C16.7068 6.39289 17.0478 6.54267 17.1657 6.84538C18.1511 9.37448 17.6236 12.3592 15.5796 14.4032C15.3499 14.6329 14.9774 14.6329 14.7477 14.4032C14.518 14.1735 14.518 13.801 14.7477 13.5713C16.4499 11.8691 16.8916 9.3824 16.0695 7.2725C15.9516 6.96979 16.1014 6.62878 16.4041 6.51084ZM8.23537 19.1078C7.36476 19.6895 6.3412 20 5.29412 20C3.89003 20 2.54345 19.4422 1.55061 18.4494C0.557771 17.4565 0 16.11 0 14.7059C0 13.6588 0.310494 12.6352 0.89222 11.7646C1.47394 10.894 2.30077 10.2155 3.26815 9.81476C4.23552 9.41406 5.29999 9.30922 6.32695 9.51349C7.35391 9.71777 8.29723 10.222 9.03762 10.9624C9.77802 11.7028 10.2822 12.6461 10.4865 13.6731C10.6908 14.7 10.5859 15.7645 10.1852 16.7319C9.78455 17.6992 9.10598 18.5261 8.23537 19.1078ZM5.71006 11.937C5.59975 11.8267 5.45013 11.7647 5.29412 11.7647C5.13811 11.7647 4.98849 11.8267 4.87817 11.937C4.76786 12.0473 4.70588 12.1969 4.70588 12.3529V14.1176H2.94118C2.78517 14.1176 2.63555 14.1796 2.52523 14.2899C2.41492 14.4003 2.35294 14.5499 2.35294 14.7059C2.35294 14.8619 2.41492 15.0115 2.52523 15.1218C2.63555 15.2321 2.78517 15.2941 2.94118 15.2941H4.70588V17.0588C4.70588 17.2148 4.76786 17.3645 4.87817 17.4748C4.98849 17.5851 5.13811 17.6471 5.29412 17.6471C5.45013 17.6471 5.59975 17.5851 5.71006 17.4748C5.82038 17.3645 5.88235 17.2148 5.88235 17.0588V15.2941H7.64706C7.80307 15.2941 7.95269 15.2321 8.063 15.1218C8.17332 15.0115 8.23529 14.8619 8.23529 14.7059C8.23529 14.5499 8.17332 14.4003 8.063 14.2899C7.95269 14.1796 7.80307 14.1176 7.64706 14.1176H5.88235V12.3529C5.88235 12.1969 5.82038 12.0473 5.71006 11.937Z"
		/>
	</svg>
);

export const shipsIcon = (props?: React.SVGProps<SVGSVGElement>) => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
		{...props}
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M6.75 0C6.33579 0 6 0.335786 6 0.75V3H3.75C3.33579 3 3 3.33579 3 3.75V8.51446L1.53948 9.04925C1.34238 9.12142 1.18453 9.2729 1.10431 9.46686C1.02408 9.66083 1.02881 9.87955 1.11734 10.0699L3.45174 15.0879C3.61725 15.0333 3.79292 15.0029 3.97409 15.0002C4.53245 14.9919 5.0463 15.2489 5.37649 15.6692L2.82163 10.1772L9.40921 7.76503C9.79306 7.62447 10.214 7.62273 10.599 7.7601L17.3744 10.1776L14.7524 15.5228C15.0809 15.189 15.5388 14.9917 16.0306 15.0003C16.2378 15.0039 16.4377 15.0438 16.6233 15.1146L19.0911 10.0838C19.1852 9.89198 19.193 9.66911 19.1126 9.47115C19.0321 9.27319 18.8711 9.11895 18.6698 9.04714L17 8.45135V3.75C17 3.33579 16.6642 3 16.25 3H14V0.75C14 0.335786 13.6642 0 13.25 0H6.75ZM12.5 3H7.5V1.5H12.5V3ZM15.5 4.5V7.91615L11.1031 6.34733C10.3881 6.09222 9.60632 6.09545 8.89345 6.35648L4.5 7.96521V4.5H15.5ZM16.7267 16.5635L16.7242 16.5548C16.6372 16.2324 16.3473 16.006 16.0131 16.0001C15.6782 15.9942 15.3801 16.2112 15.2826 16.5314L15.2822 16.5327L15.2791 16.5419C15.2753 16.5534 15.2686 16.573 15.2589 16.5997C15.2394 16.653 15.2079 16.7335 15.1635 16.832C15.0736 17.0309 14.9354 17.2925 14.7423 17.5496C14.3553 18.065 13.803 18.5 13 18.5C12.1969 18.5 11.6442 18.0649 11.2568 17.5494C11.0635 17.2922 10.9251 17.0306 10.8352 16.8317C10.7906 16.7332 10.7592 16.6527 10.7396 16.5993C10.7298 16.5727 10.7231 16.553 10.7193 16.5415L10.7162 16.5321C10.7161 16.5319 10.7162 16.5322 10.7162 16.5321C10.62 16.216 10.328 15.9995 9.99751 16C9.66708 16.0005 9.37595 16.2171 9.28052 16.5333L9.28022 16.5343L9.27724 16.5434C9.27346 16.5549 9.26681 16.5745 9.25715 16.6011C9.23777 16.6544 9.20655 16.7348 9.16233 16.8332C9.07299 17.032 8.93541 17.2933 8.74285 17.5502C8.3573 18.0647 7.80552 18.5 7 18.5C6.1944 18.5 5.6422 18.0646 5.25625 17.55C5.0635 17.293 4.92576 17.0317 4.83629 16.8329C4.792 16.7344 4.76074 16.654 4.74132 16.6007C4.73164 16.5741 4.72498 16.5545 4.72119 16.543L4.71753 16.5317C4.6203 16.2121 4.32321 15.9951 3.9889 16.0001C3.65496 16.005 3.36471 16.2301 3.27662 16.552L3.27395 16.561C3.27084 16.5713 3.2651 16.5896 3.25646 16.6149C3.23914 16.6656 3.21049 16.7432 3.16851 16.8386C3.08376 17.0312 2.94932 17.2859 2.75227 17.5373C2.36808 18.0273 1.75644 18.5 0.75 18.5C0.335786 18.5 0 18.8358 0 19.25C0 19.6642 0.335786 20 0.75 20C2.32356 20 3.33692 19.2227 3.93273 18.4627C3.95592 18.4332 3.9785 18.4036 4.00047 18.3741C4.01866 18.3994 4.03725 18.4247 4.05625 18.45C4.6078 19.1854 5.5556 20 7 20C8.44448 20 9.39197 19.1853 9.94319 18.4498C9.96257 18.4239 9.98152 18.3981 10.0001 18.3723C10.0188 18.3983 10.038 18.4245 10.0577 18.4506C10.6097 19.1851 11.5576 20 13 20C14.4424 20 15.3901 19.185 15.9418 18.4504C15.9605 18.4253 15.9789 18.4004 15.9969 18.3754C16.0183 18.4042 16.0403 18.4331 16.0629 18.462C16.6583 19.2234 17.6722 20 19.25 20C19.6642 20 20 19.6642 20 19.25C20 18.8358 19.6642 18.5 19.25 18.5C18.2378 18.5 17.6267 18.0266 17.2446 17.538C17.0483 17.2871 16.9149 17.0328 16.8309 16.8405C16.7893 16.7453 16.761 16.6678 16.7439 16.6173C16.7354 16.5921 16.7298 16.5738 16.7267 16.5635Z"
		/>
	</svg>
);

export const shipInfoIcon = () => (
	<svg
		width="20"
		height="18"
		viewBox="0 0 20 18"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M4.94118 0.675C4.94118 0.302208 5.21771 0 5.55882 0H10.9118C11.2529 0 11.5294 0.302208 11.5294 0.675V2.7H13.3824C13.7235 2.7 14 3.00221 14 3.375V7.60622L15.3751 8.14242C15.5409 8.20705 15.6735 8.34587 15.7398 8.52403C15.806 8.7022 15.7996 8.90278 15.7221 9.07544L13.6898 13.6032C13.5369 13.5394 13.3723 13.5035 13.2017 13.5002C12.7966 13.4925 12.4196 13.6701 12.149 13.9705L14.3083 9.1598L8.7286 6.98409C8.41154 6.86046 8.06487 6.86202 7.74876 6.98852L2.32369 9.15945L4.4277 14.1023C4.15578 13.724 3.7326 13.4927 3.27278 13.5002C3.12358 13.5026 2.97891 13.53 2.84261 13.5792L0.920165 9.06288C0.847253 8.89159 0.843359 8.69475 0.909429 8.52018C0.975499 8.34561 1.10549 8.20928 1.26781 8.14433L2.47059 7.66301V3.375C2.47059 3.00221 2.74712 2.7 3.08824 2.7H4.94118V0.675ZM6.17647 2.7H10.2941V1.35H6.17647V2.7ZM12.7647 7.12454V4.05H3.70588V7.16869L7.32401 5.72083C7.91109 5.48591 8.55489 5.48299 9.14372 5.7126L12.7647 7.12454ZM13.7728 14.8993L13.7749 14.9072C13.7774 14.9164 13.7821 14.9329 13.7891 14.9555C13.8032 15.0011 13.8265 15.0707 13.8608 15.1564C13.9299 15.3295 14.0398 15.5584 14.2014 15.7842C14.5161 16.224 15.0194 16.65 15.8529 16.65C16.1941 16.65 16.4706 16.9522 16.4706 17.325C16.4706 17.6978 16.1941 18 15.8529 18C14.5536 18 13.7186 17.3011 13.2283 16.6158C13.2097 16.5898 13.1916 16.5638 13.1739 16.5379C13.1591 16.5603 13.144 16.5828 13.1285 16.6053C12.6742 17.2665 11.8938 18 10.7059 18C9.51806 18 8.73738 17.2666 8.28279 16.6055C8.26662 16.582 8.25081 16.5585 8.23534 16.5351C8.22008 16.5583 8.20447 16.5815 8.18851 16.6048C7.73456 17.2668 6.95428 18 5.76471 18C4.5752 18 3.79466 17.2669 3.34044 16.605C3.3248 16.5822 3.30949 16.5594 3.2945 16.5367C3.27641 16.5632 3.25782 16.5898 3.23872 16.6165C2.74806 17.3004 1.91352 18 0.617647 18C0.27653 18 0 17.6978 0 17.325C0 16.9522 0.27653 16.65 0.617647 16.65C1.44648 16.65 1.95018 16.2246 2.26658 15.7835C2.42885 15.5573 2.53957 15.3281 2.60936 15.1547C2.64393 15.0689 2.66753 14.999 2.68179 14.9534C2.68891 14.9307 2.69363 14.9141 2.6962 14.9049L2.69839 14.8968C2.77094 14.6071 3.00997 14.4045 3.28497 14.4001C3.56029 14.3956 3.80496 14.5909 3.88503 14.8786L3.88804 14.8887C3.89116 14.8991 3.89664 14.9167 3.90462 14.9406C3.92061 14.9886 3.94635 15.061 3.98282 15.1496C4.05651 15.3285 4.16994 15.5637 4.32868 15.795C4.64652 16.2581 5.10127 16.65 5.76471 16.65C6.42807 16.65 6.88248 16.2582 7.2 15.7952C7.35857 15.5639 7.47187 15.3288 7.54545 15.1499C7.58187 15.0613 7.60757 14.9889 7.62354 14.941C7.63149 14.9171 7.63697 14.8994 7.64008 14.8891L7.64253 14.8809C7.72112 14.5963 7.96112 14.4004 8.23324 14.4C8.50545 14.3996 8.74587 14.5944 8.82509 14.8789L8.82766 14.8874C8.83081 14.8977 8.83633 14.9154 8.84437 14.9394C8.86048 14.9874 8.8864 15.0598 8.92307 15.1485C8.99716 15.3276 9.11111 15.563 9.27031 15.7945C9.58932 16.2584 10.0445 16.65 10.7059 16.65C11.3672 16.65 11.822 16.2585 12.1407 15.7947C12.2998 15.5632 12.4136 15.3279 12.4876 15.1488C12.5242 15.0602 12.5501 14.9877 12.5661 14.9397C12.5742 14.9157 12.5797 14.8981 12.5828 14.8877L12.5853 14.8795L12.5856 14.8783C12.666 14.59 12.9115 14.3948 13.1873 14.4001C13.4625 14.4054 13.7012 14.6092 13.7728 14.8993ZM18.8018 2.57146C19.3019 2.57146 19.7038 2.98335 19.6915 3.48327L19.5125 10.7798H18.0816L17.912 3.48212C17.9004 2.98264 18.3022 2.57146 18.8018 2.57146ZM18.8234 14.1429C19.4732 14.1429 19.9999 13.5672 19.9999 12.8572C19.9999 12.1471 19.4732 11.5714 18.8234 11.5714C18.1737 11.5714 17.647 12.1471 17.647 12.8572C17.647 13.5672 18.1737 14.1429 18.8234 14.1429Z"
		/>
	</svg>
);

export const voyagesIcon = () => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M6.7074 5.08366C5.68791 4.67741 4.67722 5.68814 5.08349 6.70761L6.68872 10.7357C7.15587 11.908 8.07027 12.8461 9.23014 13.3432L13.5555 15.197C14.5927 15.6415 15.6414 14.5928 15.1969 13.5556L13.3431 9.23027C12.8461 8.07038 11.9078 7.15596 10.7356 6.68882L6.7074 5.08366ZM8.08215 10.1804L6.69209 6.69227L10.1803 8.08226C10.9824 8.40188 11.6243 9.02754 11.9644 9.82115L13.572 13.572L9.82101 11.9645C9.02742 11.6244 8.40178 10.9825 8.08215 10.1804ZM10 0C4.47715 0 0 4.47715 0 10C0 15.5228 4.47715 20 10 20C15.5228 20 20 15.5228 20 10C20 4.47715 15.5228 0 10 0ZM2.24992 9H1.5582C2.01469 5.10482 5.10482 2.01469 9 1.5582V2.25C9 2.66421 9.33579 3 9.75 3C10.1642 3 10.5 2.66421 10.5 2.25V1.51446C14.6282 1.75393 17.9659 4.93955 18.4418 9H17.75C17.3358 9 17 9.33579 17 9.75C17 10.1642 17.3358 10.5 17.75 10.5H18.4855C18.2363 14.7962 14.7962 18.2363 10.5 18.4855V17.75C10.5 17.3358 10.1642 17 9.75 17C9.33579 17 9 17.3358 9 17.75V18.4418C4.93955 17.9659 1.75393 14.6282 1.51446 10.5H2.24992C2.66414 10.5 2.99992 10.1642 2.99992 9.75C2.99992 9.33579 2.66414 9 2.24992 9Z"
		/>
	</svg>
);

export const usersIcon = () => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M15 11.5C15 10.6716 14.3284 10 13.5 10H6.5C5.67157 10 5 10.6716 5 11.5V12C5 13.9714 6.85951 16 10 16C13.1405 16 15 13.9714 15 12V11.5ZM12.75 6.25C12.75 4.73122 11.5188 3.5 10 3.5C8.48122 3.5 7.25 4.73122 7.25 6.25C7.25 7.76878 8.48122 9 10 9C11.5188 9 12.75 7.76878 12.75 6.25ZM20 10C20 15.5228 15.5228 20 10 20C4.47715 20 0 15.5228 0 10C0 4.47715 4.47715 0 10 0C15.5228 0 20 4.47715 20 10ZM18.5 10C18.5 5.30558 14.6944 1.5 10 1.5C5.30558 1.5 1.5 5.30558 1.5 10C1.5 14.6944 5.30558 18.5 10 18.5C14.6944 18.5 18.5 14.6944 18.5 10Z"
		/>
	</svg>
);

export const rolesIcon = () => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M3.33333 4C3.33333 2.61929 4.39932 1.5 5.71429 1.5C7.02925 1.5 8.09524 2.61929 8.09524 4C8.09524 5.38071 7.02925 6.5 5.71429 6.5C4.39932 6.5 3.33333 5.38071 3.33333 4ZM5.71429 0C3.61034 0 1.90476 1.79086 1.90476 4C1.90476 6.20914 3.61034 8 5.71429 8C7.81823 8 9.52381 6.20914 9.52381 4C9.52381 1.79086 7.81823 0 5.71429 0ZM12.8571 5C12.8571 4.17157 13.4967 3.5 14.2857 3.5C15.0747 3.5 15.7143 4.17157 15.7143 5C15.7143 5.82843 15.0747 6.5 14.2857 6.5C13.4967 6.5 12.8571 5.82843 12.8571 5ZM14.2857 2C12.7078 2 11.4286 3.34315 11.4286 5C11.4286 6.65685 12.7078 8 14.2857 8C15.8637 8 17.1429 6.65685 17.1429 5C17.1429 3.34315 15.8637 2 14.2857 2ZM2.14286 10C0.95939 10 0 11.0074 0 12.25V12.5011L3.00862e-06 12.5022L1.32266e-05 12.5048L6.31809e-05 12.5111L0.000334638 12.5277C0.000612338 12.5406 0.00111137 12.5571 0.00199721 12.5771C0.00376764 12.6169 0.00709063 12.6705 0.0133105 12.7358C0.0257253 12.8661 0.0498346 13.045 0.0968099 13.2564C0.190426 13.6776 0.377661 14.2404 0.75412 14.8051C1.53396 15.9749 3.02075 17 5.71429 17C7.01807 17 8.03911 16.7598 8.83411 16.3793C8.69218 15.8851 8.60479 15.366 8.57926 14.8299C7.96505 15.2108 7.05969 15.5 5.71429 15.5C3.40782 15.5 2.39461 14.6501 1.92445 13.9449C1.67591 13.5721 1.55064 13.1974 1.48801 12.9155C1.45686 12.7753 1.44191 12.6612 1.43479 12.5865C1.43125 12.5493 1.42969 12.5222 1.42902 12.5072L1.42857 12.4947V12.25C1.42857 11.8358 1.74837 11.5 2.14286 11.5H9.26877C9.50538 11.0233 9.79663 10.5813 10.1338 10.1831C9.87369 10.0653 9.58696 10 9.28571 10H2.14286ZM20 14.5C20 17.5376 17.6548 20 14.7619 20C11.869 20 9.52381 17.5376 9.52381 14.5C9.52381 11.4624 11.869 9 14.7619 9C17.6548 9 20 11.4624 20 14.5ZM17.9558 12.1464C17.7698 11.9512 17.4683 11.9512 17.2823 12.1464L13.8095 15.7929L12.2415 14.1464C12.0555 13.9512 11.754 13.9512 11.568 14.1464C11.3821 14.3417 11.3821 14.6583 11.568 14.8536L13.4728 16.8536C13.6588 17.0488 13.9603 17.0488 14.1462 16.8536L17.9558 12.8536C18.1417 12.6583 18.1417 12.3417 17.9558 12.1464Z"
		/>
	</svg>
);

export const licenceManagerIcon = () => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M6.75 11.875C8.28619 11.875 9.57345 13.2073 9.91264 14.9979L19.25 15C19.6642 15 20 15.4197 20 15.9375C20 16.4121 19.7178 16.8044 19.3518 16.8664L19.25 16.875L9.91288 16.8758C9.57405 18.6671 8.28655 20 6.75 20C5.21345 20 3.92594 18.6671 3.58712 16.8758L0.75 16.875C0.335786 16.875 0 16.4553 0 15.9375C0 15.4629 0.282154 15.0706 0.648229 15.0086L0.75 15L3.58712 14.9992C3.92594 13.2079 5.21345 11.875 6.75 11.875ZM6.75 13.75C5.98586 13.75 5.33611 14.3622 5.09753 15.2156L5.07696 15.2939L5.03847 15.4792C5.01326 15.627 5 15.7803 5 15.9375C5 16.131 5.02011 16.3187 5.05785 16.4974L5.09766 16.6598L5.12335 16.7458C5.38055 17.5538 6.01191 18.125 6.75 18.125C7.51376 18.125 8.16324 17.5134 8.40212 16.6606L8.44218 16.4973L8.4251 16.5726C8.47381 16.3716 8.5 16.1584 8.5 15.9375C8.5 15.8065 8.49078 15.6781 8.47312 15.5534L8.44309 15.3821L8.42308 15.2941L8.37625 15.128C8.11881 14.3206 7.48771 13.75 6.75 13.75ZM13.25 0C14.7866 0 16.0741 1.3329 16.4129 3.12417L19.25 3.125C19.6642 3.125 20 3.54473 20 4.0625C20 4.53712 19.7178 4.92936 19.3518 4.99144L19.25 5L16.4129 5.00083C16.0741 6.7921 14.7866 8.125 13.25 8.125C11.7134 8.125 10.4259 6.7921 10.0871 5.00083L0.75 5C0.335786 5 0 4.58027 0 4.0625C0 3.58788 0.282154 3.19564 0.648229 3.13356L0.75 3.125L10.0874 3.12291C10.4265 1.33228 11.7138 0 13.25 0ZM13.25 1.875C12.4859 1.875 11.8361 2.4872 11.5975 3.34062L11.577 3.41894L11.5385 3.60421C11.5133 3.752 11.5 3.90531 11.5 4.0625C11.5 4.25604 11.5201 4.44371 11.5579 4.62243L11.5977 4.78484L11.6234 4.87081C11.8805 5.67883 12.5119 6.25 13.25 6.25C14.0138 6.25 14.6632 5.63841 14.9021 4.78564L14.9422 4.62226L14.9251 4.69761C14.9738 4.49661 15 4.28338 15 4.0625C15 3.93147 14.9908 3.80313 14.9731 3.67844L14.9431 3.50707L14.9231 3.41912L14.8763 3.25295C14.6188 2.44558 13.9877 1.875 13.25 1.875Z"
		/>
	</svg>
);

export const noonReports = () => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M16.3889 0C18.3833 0 20 1.61675 20 3.61111V16.3889C20 18.3833 18.3833 20 16.3889 20H3.61111C1.61675 20 0 18.3833 0 16.3889V3.61111C0 1.61675 1.61675 0 3.61111 0H16.3889ZM16.3889 1.66667H3.61111C2.53722 1.66667 1.66667 2.53722 1.66667 3.61111V16.3889C1.66667 17.4628 2.53722 18.3333 3.61111 18.3333H16.3889C17.4628 18.3333 18.3333 17.4628 18.3333 16.3889V3.61111C18.3333 2.53722 17.4628 1.66667 16.3889 1.66667ZM14.7222 8.88889C15.1825 8.88889 15.5556 9.26199 15.5556 9.72222V14.7222C15.5556 15.1825 15.1825 15.5556 14.7222 15.5556H5.27778C4.81754 15.5556 4.44444 15.1825 4.44444 14.7222V9.72222C4.44444 9.26199 4.81754 8.88889 5.27778 8.88889H14.7222ZM13.8889 10.5556H6.11111V13.8889H13.8889V10.5556ZM5.27778 4.72222H14.7222C15.1825 4.72222 15.5556 5.09532 15.5556 5.55556C15.5556 5.97744 15.2421 6.3261 14.8353 6.38128L14.7222 6.38889H5.27778C4.81754 6.38889 4.44444 6.01579 4.44444 5.55556C4.44444 5.13367 4.75795 4.78501 5.1647 4.72983L5.27778 4.72222H14.7222H5.27778Z"
		/>
	</svg>
);

export const vesselOperatorsIcon = () => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M3.36304 4.07675C3.36304 2.66954 4.43852 1.52878 5.76521 1.52878C7.09189 1.52878 8.16737 2.66954 8.16737 4.07675C8.16737 5.48395 7.09189 6.62471 5.76521 6.62471C4.43852 6.62471 3.36304 5.48395 3.36304 4.07675ZM5.76521 0C3.64252 0 1.92174 1.82522 1.92174 4.07675C1.92174 6.32827 3.64252 8.15349 5.76521 8.15349C7.8879 8.15349 9.60868 6.32827 9.60868 4.07675C9.60868 1.82522 7.8879 0 5.76521 0ZM12.9717 5.09593C12.9717 4.25161 13.617 3.56715 14.413 3.56715C15.209 3.56715 15.8543 4.25161 15.8543 5.09593C15.8543 5.94026 15.209 6.62471 14.413 6.62471C13.617 6.62471 12.9717 5.94026 12.9717 5.09593ZM14.413 2.03837C12.821 2.03837 11.5304 3.40729 11.5304 5.09593C11.5304 6.78458 12.821 8.15349 14.413 8.15349C16.005 8.15349 17.2956 6.78458 17.2956 5.09593C17.2956 3.40729 16.005 2.03837 14.413 2.03837ZM2.16195 10.1919C0.967939 10.1919 0 11.2186 0 12.485V12.7409L3.03543e-06 12.7421L1.33444e-05 12.7447L6.37439e-05 12.7511L0.000337619 12.7681C0.000617795 12.7812 0.00112127 12.7981 0.00201501 12.8184C0.00380121 12.859 0.00715381 12.9136 0.0134291 12.9801C0.0259545 13.113 0.0502787 13.2953 0.0976725 13.5107C0.192123 13.9401 0.381026 14.5136 0.76084 15.0892C1.54763 16.2814 3.04766 17.3262 5.76521 17.3262C7.08061 17.3262 8.11075 17.0814 8.91283 16.6936C8.76964 16.1899 8.68146 15.6608 8.65571 15.1144C8.03602 15.5027 7.1226 15.7974 5.76521 15.7974C3.43819 15.7974 2.41595 14.9312 1.9416 14.2125C1.69084 13.8325 1.56446 13.4506 1.50127 13.1633C1.46984 13.0205 1.45476 12.9042 1.44758 12.828C1.444 12.7901 1.44243 12.7625 1.44175 12.7471L1.4413 12.7344V12.485C1.4413 12.0629 1.76395 11.7206 2.16195 11.7206H9.35137C9.59009 11.2348 9.88392 10.7843 10.2241 10.3785C9.96168 10.2584 9.67239 10.1919 9.36846 10.1919H2.16195ZM11.7968 11.1864C12.0992 12.2976 11.4705 13.4528 10.412 13.7306L9.85066 13.8779C9.80751 14.1711 9.78509 14.4719 9.78509 14.7785C9.78509 15.0992 9.80963 15.4137 9.85676 15.7197L10.3751 15.8521C11.4441 16.1251 12.0795 17.2921 11.7683 18.4107L11.5894 19.0541C12.0113 19.4471 12.4922 19.7663 13.0154 19.9933L13.4894 19.4646C14.2471 18.6195 15.5176 18.6197 16.275 19.4651L16.7542 20C17.2765 19.7755 17.7569 19.4594 18.179 19.0696L17.9887 18.3705C17.6863 17.2593 18.3151 16.1042 19.3735 15.8264L19.9344 15.6792C19.9776 15.386 20 15.0851 20 14.7785C20 14.4577 19.9754 14.1432 19.9283 13.8372L19.4104 13.7049C18.3415 13.4318 17.706 12.2649 18.0172 11.1463L18.1961 10.5033C17.7741 10.1101 17.2932 9.79092 16.77 9.56381L16.2961 10.0923C15.5384 10.9375 14.2679 10.9372 13.5105 10.0918L13.0312 9.55686C12.5089 9.78129 12.0285 10.0974 11.6064 10.4871L11.7968 11.1864ZM14.8925 16.3073C14.1232 16.3073 13.4996 15.6228 13.4996 14.7785C13.4996 13.9342 14.1232 13.2497 14.8925 13.2497C15.6618 13.2497 16.2855 13.9342 16.2855 14.7785C16.2855 15.6228 15.6618 16.3073 14.8925 16.3073Z"
		/>
	</svg>
);

export const devicesIcon = () => (
	<svg
		width="16"
		height="20"
		viewBox="-2 0 12 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M9.75 0C10.9926 0 12 1.00736 12 2.25V17.75C12 18.9926 10.9926 20 9.75 20H2.25C1.00736 20 0 18.9926 0 17.75V2.25C0 1.00736 1.00736 0 2.25 0H9.75ZM9.75 1.5H2.25C1.83579 1.5 1.5 1.83579 1.5 2.25V17.75C1.5 18.1642 1.83579 18.5 2.25 18.5H9.75C10.1642 18.5 10.5 18.1642 10.5 17.75V2.25C10.5 1.83579 10.1642 1.5 9.75 1.5ZM7.24887 15.5C7.66308 15.4994 7.99938 15.8347 8 16.2489C8.00062 16.6631 7.66534 16.9994 7.25113 17L4.75113 17.0038C4.33692 17.0044 4.00062 16.6691 4 16.2549C3.99938 15.8407 4.33466 15.5044 4.74887 15.5038L7.24887 15.5Z"
		/>
	</svg>
);

export const ocrIcon = () => (
	<svg
		width="18"
		height="18"
		viewBox="0 0 18 18"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M0 2.53846C0 1.13651 1.13651 0 2.53846 0H6.23077C6.61312 0 6.92308 0.309957 6.92308 0.692308C6.92308 1.07466 6.61312 1.38462 6.23077 1.38462H2.53846C1.90121 1.38462 1.38462 1.90121 1.38462 2.53846V6.23077C1.38462 6.61312 1.07466 6.92308 0.692308 6.92308C0.309957 6.92308 0 6.61312 0 6.23077V2.53846ZM11.0769 0.692308C11.0769 0.309957 11.3869 0 11.7692 0H15.4615C16.8635 0 18 1.13651 18 2.53846V6.23077C18 6.61312 17.69 6.92308 17.3077 6.92308C16.9253 6.92308 16.6154 6.61312 16.6154 6.23077V2.53846C16.6154 1.90121 16.0988 1.38462 15.4615 1.38462H11.7692C11.3869 1.38462 11.0769 1.07466 11.0769 0.692308ZM0 9.00025C0 8.6179 0.309957 8.30795 0.692308 8.30795H17.3077C17.69 8.30795 18 8.6179 18 9.00025C18 9.38261 17.69 9.69256 17.3077 9.69256H0.692308C0.309957 9.69256 0 9.38261 0 9.00025ZM0.692308 11.0769C1.07466 11.0769 1.38462 11.3869 1.38462 11.7692V15.4615C1.38462 16.0988 1.90121 16.6154 2.53846 16.6154H6.23077C6.61312 16.6154 6.92308 16.9253 6.92308 17.3077C6.92308 17.69 6.61312 18 6.23077 18H2.53846C1.13651 18 0 16.8635 0 15.4615V11.7692C0 11.3869 0.309957 11.0769 0.692308 11.0769ZM17.3077 11.0769C17.69 11.0769 18 11.3869 18 11.7692V15.4615C18 16.8635 16.8635 18 15.4615 18H11.7692C11.3869 18 11.0769 17.69 11.0769 17.3077C11.0769 16.9253 11.3869 16.6154 11.7692 16.6154H15.4615C16.0988 16.6154 16.6154 16.0988 16.6154 15.4615V11.7692C16.6154 11.3869 16.9253 11.0769 17.3077 11.0769Z"
		/>
	</svg>
);

export const mailRecipientsIcon = () => (
	<svg
		width="20"
		height="16"
		viewBox="0 0 20 16"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M3.25 0H16.75C18.483 0 19.8992 1.35645 19.9949 3.06558L20 3.25V12.75C20 14.483 18.6435 15.8992 16.9344 15.9949L16.75 16H3.25C1.51697 16 0.100754 14.6435 0.00514483 12.9344L0 12.75V3.25C0 1.51697 1.35645 0.100754 3.06558 0.0051446L3.25 0H16.75H3.25ZM18.5 5.373L10.3493 9.66369C10.1619 9.76233 9.94313 9.77642 9.74676 9.70596L9.65069 9.66369L1.5 5.374V12.75C1.5 13.6682 2.20711 14.4212 3.10647 14.4942L3.25 14.5H16.75C17.6682 14.5 18.4212 13.7929 18.4942 12.8935L18.5 12.75V5.373ZM16.75 1.5H3.25C2.33183 1.5 1.57881 2.20711 1.5058 3.10647L1.5 3.25V3.679L10 8.15246L18.5 3.678V3.25C18.5 2.33183 17.7929 1.57881 16.8935 1.5058L16.75 1.5Z"
		/>
	</svg>
);

export const consumptionFormulasIcon = () => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M4.73684 2.14286C4.73684 1.74837 5.0903 1.42857 5.52632 1.42857H11.5789V5.71429C11.5789 6.76626 12.5215 7.61905 13.6842 7.61905H18.4211V16.9048C18.4211 17.2993 18.0676 17.619 17.6316 17.619H12.6316V19.0476H17.6316C18.9396 19.0476 20 18.0882 20 16.9048V7.29925C20 6.85722 19.8059 6.4333 19.4605 6.12074L13.235 0.488155C12.8895 0.175595 12.421 0 11.9324 0H5.52632C4.21827 0 3.15789 0.95939 3.15789 2.14286V9.52381H4.73684V2.14286ZM13.1579 5.71429V2.43872L17.3046 6.19048H13.6842C13.3935 6.19048 13.1579 5.97728 13.1579 5.71429ZM3.68421 11.1905C3.68421 11.585 3.33075 11.9048 2.89474 11.9048H1.57895V18.5714H2.89474C3.33075 18.5714 3.68421 18.8912 3.68421 19.2857C3.68421 19.6802 3.33075 20 2.89474 20H1.57895C0.706919 20 0 19.3604 0 18.5714V11.9048C0 11.1158 0.706919 10.4762 1.57895 10.4762H2.89474C3.33075 10.4762 3.68421 10.796 3.68421 11.1905ZM8.68421 11.9048C8.2482 11.9048 7.89474 11.585 7.89474 11.1905C7.89474 10.796 8.2482 10.4762 8.68421 10.4762H10C10.872 10.4762 11.5789 11.1158 11.5789 11.9048V18.5714C11.5789 19.3604 10.872 20 10 20H8.68421C8.2482 20 7.89474 19.6802 7.89474 19.2857C7.89474 18.8912 8.2482 18.5714 8.68421 18.5714H10V11.9048H8.68421ZM5.78947 14.0476C6.22549 14.0476 6.57895 14.3674 6.57895 14.7619V18.0952C6.57895 18.4897 6.22549 18.8095 5.78947 18.8095C5.35346 18.8095 5 18.4897 5 18.0952V14.7619C5 14.3674 5.35346 14.0476 5.78947 14.0476ZM5.78947 13.3333C6.22549 13.3333 6.57895 13.0135 6.57895 12.619C6.57895 12.2246 6.22549 11.9048 5.78947 11.9048C5.35346 11.9048 5 12.2246 5 12.619C5 13.0135 5.35346 13.3333 5.78947 13.3333Z"
		/>
	</svg>
);

export const consumptionResultsIcon = () => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M18.4211 17.1429C18.4211 17.4048 18.1853 17.619 17.8947 17.619H11.8608C11.5585 18.1438 11.1755 18.6245 10.7268 19.0476H17.8947C19.0568 19.0476 20 18.1943 20 17.1429V7.45524C20 6.95048 19.7779 6.46571 19.3832 6.10857L13.2474 0.558095C13.2316 0.543855 13.2138 0.531505 13.1961 0.519233C13.183 0.510166 13.17 0.501141 13.1579 0.491429C13.0832 0.430476 13.0095 0.370476 12.9274 0.32C12.9008 0.303754 12.8716 0.290929 12.8427 0.278163C12.8258 0.270723 12.8089 0.263303 12.7926 0.255238C12.7751 0.24628 12.7575 0.237111 12.74 0.22793C12.683 0.198067 12.6256 0.168071 12.5653 0.144762C12.3579 0.0666666 12.1347 0.0276191 11.9084 0.0133333C11.8877 0.0121355 11.8671 0.00959896 11.8465 0.00705651C11.818 0.00353382 11.7893 0 11.76 0H5.26316C4.10105 0 3.15789 0.853333 3.15789 1.90476V9.04587C3.65703 8.85755 4.18609 8.72118 4.73684 8.64424V1.90476C4.73684 1.64286 4.97263 1.42857 5.26316 1.42857H11.5789V5.71429C11.5789 6.76571 12.5221 7.61905 13.6842 7.61905H18.4211V17.1429ZM13.1579 2.49619L17.24 6.19048H13.6842C13.3937 6.19048 13.1579 5.97619 13.1579 5.71429V2.49619Z"
		/>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M11.5789 14.7619C11.5789 17.6548 8.98691 20 5.78947 20C2.59204 20 0 17.6548 0 14.7619C0 11.869 2.59204 9.52381 5.78947 9.52381C8.98691 9.52381 11.5789 11.869 11.5789 14.7619ZM9.31953 12.5204C9.11399 12.3345 8.78075 12.3345 8.57521 12.5204L4.73684 15.9932L3.00374 14.4252C2.7982 14.2392 2.46496 14.2392 2.25942 14.4252C2.05388 14.6112 2.05388 14.9127 2.25942 15.0986L4.36468 17.0034C4.57022 17.1894 4.90346 17.1894 5.109 17.0034L9.31953 13.1939C9.52507 13.0079 9.52507 12.7064 9.31953 12.5204Z"
		/>
	</svg>
);

export const draftsIcon = () => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M15.9083 13.724L15.9198 13.7383C16.2825 14.1372 16.7809 14.3059 17.2727 14.2226V17.75C17.2727 18.9409 16.4317 19.9156 15.3673 19.9948L15.2273 20H4.77273C3.69012 20 2.80396 19.0748 2.73199 17.904L2.72727 17.75L2.72645 14.2216C3.16926 14.2969 3.61761 14.1679 3.96742 13.8503L4.09008 13.725L4.09091 17.75C4.09091 18.1297 4.34741 18.4435 4.68021 18.4932L4.77273 18.5H15.2273C15.5725 18.5 15.8577 18.2178 15.9029 17.8518L15.9091 17.75L15.9083 13.724ZM3.43741 6.96803C3.67947 7.23429 3.70148 7.65096 3.50343 7.94457L3.43741 8.02869L2.32727 9.24836H5.76697C6.09646 9.24831 6.36364 9.58405 6.36364 9.99827C6.36364 10.378 6.13927 10.6918 5.84807 10.7415L5.76712 10.7484H2.32727L3.43741 11.9697C3.67947 12.2359 3.70148 12.6526 3.50343 12.9462L3.43741 13.0303C3.19535 13.2966 2.81656 13.3208 2.54964 13.1029L2.47317 13.0303L0.31884 10.6631C0.12926 10.5375 0 10.2874 0 9.99927C0 9.71126 0.129098 9.46115 0.318532 9.33545L2.47317 6.96803C2.73944 6.67513 3.17114 6.67513 3.43741 6.96803ZM16.5626 6.96511C16.8047 6.69884 17.1834 6.67464 17.4504 6.89249L17.5268 6.96511L19.6815 9.33254C19.8709 9.45823 20 9.70834 20 9.99636C20 10.2485 19.901 10.4715 19.7493 10.6074L19.6812 10.6602L17.5268 13.0274L17.4504 13.1C17.2131 13.2937 16.8874 13.2961 16.648 13.1072L16.5626 13.0274L16.4966 12.9433C16.3205 12.6823 16.3184 12.3241 16.4901 12.0607L16.5626 11.9668L17.6727 10.7454H14.2329L14.1519 10.7386C13.8607 10.6889 13.6364 10.375 13.6364 9.99535C13.6364 9.61565 13.8609 9.30189 14.1521 9.25228L14.233 9.24544H17.6727L16.5626 8.02577L16.4966 7.94165C16.2985 7.64804 16.3205 7.23138 16.5626 6.96511ZM15.2273 0C16.3099 0 17.196 0.925161 17.268 2.09595L17.2727 2.25L17.2725 5.76711C16.8412 5.69883 16.3892 5.82556 16.0343 6.14728L15.9085 6.27429L15.9091 2.25C15.9091 1.8703 15.6526 1.55651 15.3198 1.50685L15.2273 1.5H4.77273C4.42755 1.5 4.14228 1.78215 4.09713 2.14823L4.09091 2.25L4.09067 6.2725L4.08023 6.26092C3.71123 5.85502 3.20606 5.6902 2.72648 5.76645L2.72727 2.25C2.72727 1.05914 3.56833 0.0843551 4.63268 0.00519085L4.77273 0H15.2273Z"
		/>
	</svg>
);

export const integrationsIcon = () => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M0 3.5C0 1.567 1.84353 0 4.11765 0C6.39176 0 8.23529 1.567 8.23529 3.5C8.23529 5.08956 6.98866 6.43162 5.28109 6.85834C6.09034 9.11123 8.55533 10.75 11.4706 10.75H11.8595C12.2634 9.1774 13.9111 8 15.8824 8C18.1565 8 20 9.567 20 11.5C20 13.433 18.1565 15 15.8824 15C13.9111 15 12.2634 13.8226 11.8595 12.25H11.4706C8.84594 12.25 6.50804 11.2063 5 9.58054L5 13.0805C6.85011 13.4239 8.23529 14.8244 8.23529 16.5C8.23529 18.433 6.39176 20 4.11765 20C1.84353 20 0 18.433 0 16.5C0 14.8244 1.38519 13.4239 3.23529 13.0805L3.23529 6.91946C1.38519 6.57612 0 5.17556 0 3.5ZM4.11765 1.5C2.81815 1.5 1.76471 2.39543 1.76471 3.5C1.76471 4.60457 2.81815 5.5 4.11765 5.5C5.41714 5.5 6.47059 4.60457 6.47059 3.5C6.47059 2.39543 5.41714 1.5 4.11765 1.5ZM4.11765 14.5C2.81815 14.5 1.76471 15.3954 1.76471 16.5C1.76471 17.6046 2.81815 18.5 4.11765 18.5C5.41714 18.5 6.47059 17.6046 6.47059 16.5C6.47059 15.3954 5.41714 14.5 4.11765 14.5ZM13.5294 11.5C13.5294 12.6046 14.5829 13.5 15.8824 13.5C17.1818 13.5 18.2353 12.6046 18.2353 11.5C18.2353 10.3954 17.1818 9.5 15.8824 9.5C14.5829 9.5 13.5294 10.3954 13.5294 11.5Z"
		/>
	</svg>
);

export const fuelTypesIcon = () => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M6.99994 0.714286C6.99994 0.319797 7.33573 0 7.74994 0C8.16416 0 8.49994 0.319797 8.49994 0.714286V1.91939C8.95062 1.95772 9.39025 2.14115 9.73522 2.46969L13.2708 5.83687C14.0518 6.58072 14.0518 7.78675 13.2708 8.53061L9.02812 12.5712C8.24707 13.3151 6.98074 13.3151 6.19969 12.5712L2.66416 9.20404C1.88311 8.46019 1.88311 7.25416 2.66416 6.5103L6.9068 2.46969C6.93713 2.4408 6.9682 2.41303 6.99994 2.38639V0.714286ZM6.99994 5V4.40129L4.11052 7.15312H12.3555C12.348 7.04165 12.2995 6.93221 12.2101 6.84702L8.67456 3.47984C8.62326 3.43099 8.56359 3.39497 8.49994 3.37178V5C8.49994 5.39449 8.16416 5.71429 7.74994 5.71429C7.33573 5.71429 6.99994 5.39449 6.99994 5ZM4.132 8.58169L7.26035 11.5611C7.45561 11.747 7.77219 11.747 7.96746 11.5611L11.0958 8.58169H4.132ZM4.58573 12.381H2.5C1.11929 12.381 0 13.4469 0 14.7619V17.619C0 18.934 1.11929 20 2.5 20H17.5C18.8807 20 20 18.934 20 17.619V14.7619C20 13.6048 19.1333 12.6404 17.9844 12.4256C17.9945 12.5279 18 12.6322 18 12.738C18 13.1202 17.9349 13.4985 17.8115 13.8566C18.2113 13.9813 18.5 14.3395 18.5 14.7619V17.619C18.5 18.145 18.0523 18.5714 17.5 18.5714H2.5C1.94772 18.5714 1.5 18.145 1.5 17.619V14.7619C1.5 14.2359 1.94772 13.8095 2.5 13.8095H6.3345C6.02991 13.6728 5.74449 13.4845 5.49269 13.2447L4.58573 12.381ZM12 12.7381C12 12.2682 12.1541 11.8121 12.3418 11.4226C12.5325 11.027 12.7798 10.6553 13.0158 10.3424C13.2531 10.0275 13.4888 9.75908 13.6647 9.56964C13.7502 9.47758 13.9062 9.32066 13.9642 9.26232L13.9697 9.25683C14.2626 8.97788 14.7374 8.97788 15.0303 9.25683L15.3353 9.56964C15.5112 9.75908 15.7469 10.0275 15.9842 10.3424C16.2202 10.6553 16.4675 11.027 16.6582 11.4226C16.8459 11.8121 17 12.2682 17 12.7381C17 14.0623 15.9711 15.2381 14.5 15.2381C13.0289 15.2381 12 14.0623 12 12.7381ZM14.7658 11.1755C14.676 11.0564 14.5858 10.9444 14.5 10.8423C14.4142 10.9444 14.324 11.0564 14.2342 11.1755C14.0327 11.4429 13.8425 11.7333 13.7051 12.0186C13.5647 12.3098 13.5 12.5532 13.5 12.7381C13.5 13.3965 13.9809 13.8095 14.5 13.8095C15.0191 13.8095 15.5 13.3965 15.5 12.7381C15.5 12.5532 15.4353 12.3098 15.2949 12.0186C15.1575 11.7333 14.9673 11.4429 14.7658 11.1755Z"
		/>
	</svg>
);

export const emissionCalculationIcon = () => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M4.52381 2.85714C3.60334 2.85714 2.85714 3.60333 2.85714 4.52381V5.47619C2.85714 6.39666 3.60333 7.14286 4.52381 7.14286H9.7619C10.6824 7.14286 11.4286 6.39666 11.4286 5.47619V4.52381C11.4286 3.60333 10.6824 2.85714 9.7619 2.85714H4.52381ZM4.28571 4.52381C4.28571 4.39231 4.39231 4.28571 4.52381 4.28571H9.7619C9.8934 4.28571 10 4.39231 10 4.52381V5.47619C10 5.60769 9.8934 5.71429 9.7619 5.71429H4.52381C4.39231 5.71429 4.28571 5.60769 4.28571 5.47619V4.52381ZM7.14286 8.33333C6.48538 8.33333 5.95238 8.86633 5.95238 9.52381C5.95238 10.1813 6.48538 10.7143 7.14286 10.7143C7.80034 10.7143 8.33333 10.1813 8.33333 9.52381C8.33333 8.86633 7.80034 8.33333 7.14286 8.33333ZM5.95238 12.8571C5.95238 12.1997 6.48538 11.6667 7.14286 11.6667C7.80034 11.6667 8.33333 12.1997 8.33333 12.8571C8.33333 13.5146 7.80034 14.0476 7.14286 14.0476C6.48538 14.0476 5.95238 13.5146 5.95238 12.8571ZM3.80952 8.33333C3.15204 8.33333 2.61905 8.86633 2.61905 9.52381C2.61905 10.1813 3.15204 10.7143 3.80952 10.7143C4.46701 10.7143 5 10.1813 5 9.52381C5 8.86633 4.46701 8.33333 3.80952 8.33333ZM2.61905 12.8571C2.61905 12.1997 3.15204 11.6667 3.80952 11.6667C4.46701 11.6667 5 12.1997 5 12.8571C5 13.5146 4.46701 14.0476 3.80952 14.0476C3.15204 14.0476 2.61905 13.5146 2.61905 12.8571ZM10.4762 8.33333C9.81871 8.33333 9.28571 8.86633 9.28571 9.52381C9.28571 10.1813 9.81871 10.7143 10.4762 10.7143C11.1337 10.7143 11.6667 10.1813 11.6667 9.52381C11.6667 8.86633 11.1337 8.33333 10.4762 8.33333ZM9.28571 12.8571C9.28571 12.1997 9.81871 11.6667 10.4762 11.6667C10.9979 11.6667 11.4412 12.0023 11.6021 12.4693C11.2394 12.9283 10.952 13.4494 10.7583 14.014C10.6679 14.036 10.5734 14.0476 10.4762 14.0476C9.81871 14.0476 9.28571 13.5146 9.28571 12.8571ZM12.8571 2.98771V11.3233C13.2928 11.0393 13.7733 10.8183 14.2857 10.6734V2.98771C14.2857 1.33764 12.9481 0 11.298 0H2.98771C1.33764 0 0 1.33764 0 2.98771V13.679C0 15.329 1.33764 16.6667 2.98771 16.6667H10.5626C10.5058 16.3578 10.4762 16.0395 10.4762 15.7143C10.4762 15.5538 10.4834 15.3949 10.4975 15.2381H2.98771C2.12662 15.2381 1.42857 14.54 1.42857 13.679V2.98771C1.42857 2.12662 2.12662 1.42857 2.98771 1.42857H11.298C12.1591 1.42857 12.8571 2.12662 12.8571 2.98771ZM2.98766 17.6191H10.8333C11.0372 18.1412 11.3225 18.6226 11.6735 19.0477L11.6665 19.0477H5.00739C3.9842 19.0477 3.09847 18.4611 2.66741 17.6063C2.77304 17.6148 2.87984 17.6191 2.98766 17.6191ZM15.7143 10.4762C16.0395 10.4762 16.3578 10.5058 16.6666 10.5625L16.6667 5.00001C16.6667 3.9768 16.08 3.09063 15.2246 2.65962C15.2335 2.76783 15.238 2.87728 15.238 2.98779V4.98659L15.2381 4.99998L15.2381 10.4975C15.3949 10.4834 15.5538 10.4762 15.7143 10.4762ZM15.7143 11.4286C13.3474 11.4286 11.4286 13.3474 11.4286 15.7143C11.4286 18.0812 13.3474 20 15.7143 20C18.0812 20 20 18.0812 20 15.7143C20 13.3474 18.0812 11.4286 15.7143 11.4286ZM16.049 13.4708L16.0514 13.4732L17.9558 15.3776C18.1417 15.5635 18.1417 15.865 17.9558 16.051C17.7698 16.237 17.4683 16.237 17.2823 16.051L16.1905 14.9591V17.619C16.1905 17.882 15.9773 18.0952 15.7143 18.0952C15.4513 18.0952 15.2381 17.882 15.2381 17.619V14.9591L14.1462 16.051C13.9603 16.237 13.6588 16.237 13.4728 16.051C13.2868 15.865 13.2868 15.5635 13.4728 15.3776L15.3776 13.4728C15.4232 13.4272 15.4758 13.3927 15.532 13.3695C15.5873 13.3465 15.6479 13.3337 15.7114 13.3333L15.7143 13.3333L15.7171 13.3333C15.7807 13.3337 15.8412 13.3465 15.8966 13.3695C15.9519 13.3924 16.0038 13.4261 16.049 13.4708Z"
		/>
	</svg>
);

export const dataProviderIcon = () => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M0 3.80952C0 3.15186 0.3319 2.57775 0.81498 2.11452C1.2941 1.65509 1.95053 1.27376 2.70177 0.96907C4.20605 0.35896 6.22882 0 8.4211 0C10.6133 0 12.6361 0.35896 14.1403 0.96907C14.8916 1.27376 15.548 1.65509 16.0271 2.11452C16.5102 2.57775 16.8421 3.15186 16.8421 3.80952V8.8922C16.3636 8.6865 15.8282 8.5714 15.2632 8.5714V6.08802C14.9253 6.29792 14.5467 6.48516 14.1403 6.64998C12.6361 7.26009 10.6133 7.61905 8.4211 7.61905C6.22882 7.61905 4.20605 7.26009 2.70177 6.64998C2.2954 6.48516 1.91677 6.29792 1.57895 6.08802V15.2381C1.57895 15.4352 1.67415 15.6753 1.96342 15.9527C2.25666 16.2339 2.71785 16.5195 3.34765 16.775C4.60546 17.2851 6.39848 17.619 8.4211 17.619C8.7967 17.619 9.1643 17.6075 9.5222 17.5855C9.6067 18.0699 9.8027 18.5429 10.1187 18.9743C9.569 19.0225 9.0009 19.0476 8.4211 19.0476C6.22882 19.0476 4.20605 18.6887 2.70177 18.0786C1.95053 17.7739 1.2941 17.3925 0.81498 16.9331C0.3319 16.4699 0 15.8958 0 15.2381V3.80952ZM1.57895 3.80952C1.57895 4.00658 1.67415 4.24675 1.96342 4.52413C2.25666 4.80532 2.71785 5.09096 3.34765 5.3464C4.60546 5.85654 6.39848 6.19048 8.4211 6.19048C10.4436 6.19048 12.2366 5.85654 13.4945 5.3464C14.1243 5.09096 14.5854 4.80532 14.8787 4.52413C15.168 4.24675 15.2632 4.00658 15.2632 3.80952C15.2632 3.61247 15.168 3.3723 14.8787 3.09491C14.5854 2.81373 14.1243 2.52809 13.4945 2.27265C12.2366 1.76251 10.4436 1.42857 8.4211 1.42857C6.39848 1.42857 4.60546 1.76251 3.34765 2.27265C2.71785 2.52809 2.25666 2.81373 1.96342 3.09491C1.67415 3.3723 1.57895 3.61247 1.57895 3.80952ZM17.8947 11.9048C17.8947 13.2197 16.7165 14.2857 15.2632 14.2857C13.8098 14.2857 12.6316 13.2197 12.6316 11.9048C12.6316 10.5898 13.8098 9.5238 15.2632 9.5238C16.7165 9.5238 17.8947 10.5898 17.8947 11.9048ZM20 17.0238C20 18.5062 18.6466 20 15.2632 20C11.8797 20 10.5263 18.5118 10.5263 17.0238V16.9257C10.5263 15.9933 11.3618 15.2381 12.3923 15.2381H18.134C19.1646 15.2381 20 15.9933 20 16.9257V17.0238Z"
		/>
	</svg>
);

export const consumptionCalculationIcon = () => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M4.84712 2.8595C3.86086 2.8595 3.06134 3.6063 3.06134 4.52754V5.4807C3.06134 6.40194 3.86086 7.14874 4.84712 7.14874H10.4596C11.4458 7.14874 12.2454 6.40194 12.2454 5.4807V4.52754C12.2454 3.6063 11.4458 2.8595 10.4596 2.8595H4.84712ZM4.59201 4.52754C4.59201 4.39593 4.70623 4.28925 4.84712 4.28925H10.4596C10.6005 4.28925 10.7147 4.39593 10.7147 4.52754V5.4807C10.7147 5.61231 10.6005 5.719 10.4596 5.719H4.84712C4.70623 5.719 4.59201 5.61231 4.59201 5.4807V4.52754ZM7.65335 8.3402C6.94888 8.3402 6.37779 8.87364 6.37779 9.53166C6.37779 10.1897 6.94888 10.7231 7.65335 10.7231C8.35782 10.7231 8.92891 10.1897 8.92891 9.53166C8.92891 8.87364 8.35782 8.3402 7.65335 8.3402ZM6.37779 12.8677C6.37779 12.2097 6.94888 11.6763 7.65335 11.6763C8.35782 11.6763 8.92891 12.2097 8.92891 12.8677C8.92891 13.5258 8.35782 14.0592 7.65335 14.0592C6.94888 14.0592 6.37779 13.5258 6.37779 12.8677ZM4.08179 8.3402C3.37731 8.3402 2.80623 8.87364 2.80623 9.53166C2.80623 10.1897 3.37731 10.7231 4.08179 10.7231C4.78626 10.7231 5.35734 10.1897 5.35734 9.53166C5.35734 8.87364 4.78626 8.3402 4.08179 8.3402ZM2.80623 12.8677C2.80623 12.2097 3.37731 11.6763 4.08179 11.6763C4.78626 11.6763 5.35734 12.2097 5.35734 12.8677C5.35734 13.5258 4.78626 14.0592 4.08179 14.0592C3.37731 14.0592 2.80623 13.5258 2.80623 12.8677ZM11.2249 8.3402C10.5204 8.3402 9.94935 8.87364 9.94935 9.53166C9.94935 10.1897 10.5204 10.7231 11.2249 10.7231C11.9294 10.7231 12.5005 10.1897 12.5005 9.53166C12.5005 8.87364 11.9294 8.3402 11.2249 8.3402ZM9.94935 12.8677C9.94935 12.2097 10.5204 11.6763 11.2249 11.6763C11.7768 11.6763 12.2468 12.0036 12.4245 12.4618L11.8355 13.9141C11.6541 14.0066 11.4461 14.0592 11.2249 14.0592C10.5204 14.0592 9.94935 13.5258 9.94935 12.8677ZM13.776 2.99017V10.4848H15.3067V2.99017C15.3067 1.33875 13.8735 0 12.1055 0H3.20124C1.43324 0 0 1.33875 0 2.99017V13.6902C0 15.3417 1.43324 16.6804 3.20124 16.6804H10.7134L11.2934 15.2507H3.20124C2.27861 15.2507 1.53067 14.552 1.53067 13.6902V2.99017C1.53067 2.12837 2.27861 1.42975 3.20124 1.42975H12.1055C13.0281 1.42975 13.776 2.12837 13.776 2.99017ZM3.20118 17.6336H10.3268L10.2567 17.8065C10.0724 18.2608 10.3974 18.7615 10.8765 18.7615H12.3665C12.5948 18.7615 12.7913 18.8767 12.9118 19.0488C12.776 19.0585 12.6388 19.0634 12.5003 19.0634H5.36526C4.26895 19.0634 3.31991 18.4763 2.85805 17.6208C2.97122 17.6293 3.08566 17.6336 3.20118 17.6336ZM16.3271 10.4848H17.8577L17.8578 5.00413C17.8578 3.98007 17.2292 3.09318 16.3127 2.66181C16.3222 2.77011 16.3271 2.87965 16.3271 2.99026V4.9907L16.3272 5.00411L16.3271 10.4848ZM14.3834 17.7492C14.4639 17.4484 14.2204 17.157 13.8884 17.157H12.7561C12.392 17.157 12.1451 16.811 12.2851 16.4971L14.4111 11.7313C14.4903 11.5537 14.6761 11.438 14.882 11.438H18.426C18.7743 11.438 19.0202 11.7567 18.91 12.0653L18.3373 13.6702C18.2272 13.9788 18.4731 14.2975 18.8214 14.2975H19.2331C19.9278 14.2975 20.2631 15.0923 19.7553 15.535L14.7832 19.8697C14.4116 20.1936 13.8168 19.8663 13.94 19.4057L14.3834 17.7492Z"
		/>
	</svg>
);

export const bunkerDeliveryNoteIcon = () => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M8.50856e-05 3.20754C9.61861e-05 1.43606 1.39661 0 3.1193 0H13.0276C14.7503 0 16.1468 1.43607 16.1468 3.20756V7.73587C16.1468 8.25689 15.7361 8.67927 15.2294 8.67927C14.7228 8.67927 14.312 8.25689 14.312 7.73587V3.20756C14.312 2.47812 13.737 1.8868 13.0276 1.8868H3.1193C2.40996 1.8868 1.83492 2.47812 1.83492 3.20755L1.83483 16.7924C1.83483 17.5219 2.40986 18.1132 3.11921 18.1132H8.6237C9.13038 18.1132 9.54112 18.5356 9.54112 19.0566C9.54112 19.5776 9.13038 20 8.6237 20H3.1192C1.3965 20 -1.10457e-05 18.5639 0 16.7924L8.50856e-05 3.20754ZM3.85349 5.47171C3.85349 4.95069 4.26423 4.52831 4.7709 4.52831H11.3763C11.883 4.52831 12.2937 4.95069 12.2937 5.47171C12.2937 5.99274 11.883 6.41511 11.3763 6.41511H4.7709C4.26423 6.41511 3.85349 5.99274 3.85349 5.47171ZM3.85349 8.86795C3.85349 8.34692 4.26423 7.92455 4.7709 7.92455H11.3763C11.883 7.92455 12.2937 8.34692 12.2937 8.86795C12.2937 9.38897 11.883 9.81135 11.3763 9.81135H4.7709C4.26423 9.81135 3.85349 9.38897 3.85349 8.86795ZM3.85349 12.2642C3.85349 11.7432 4.26423 11.3208 4.7709 11.3208H8.0736C8.58027 11.3208 8.99101 11.7432 8.99101 12.2642C8.99101 12.7852 8.58027 13.2076 8.0736 13.2076H4.7709C4.26423 13.2076 3.85349 12.7852 3.85349 12.2642ZM14.312 12.2642C14.312 11.7432 14.7228 11.3208 15.2294 11.3208H17.4561C18.8611 11.3208 20 12.492 20 13.9367C20 15.3814 18.8611 16.5526 17.4561 16.5526H13.0332L13.717 17.2591C14.0744 17.6284 14.073 18.2257 13.7139 18.5932C13.3547 18.9608 12.7739 18.9594 12.4164 18.5901L10.1756 16.2747C9.81951 15.9068 9.81936 15.3122 10.1753 14.9441L12.3148 12.7311C12.6721 12.3616 13.2529 12.3599 13.6122 12.7272C13.9715 13.0946 13.9732 13.6919 13.616 14.0614L13.0316 14.6658H17.4561C17.8477 14.6658 18.1652 14.3394 18.1652 13.9367C18.1652 13.534 17.8477 13.2076 17.4561 13.2076H15.2294C14.7228 13.2076 14.312 12.7852 14.312 12.2642Z"
		/>
	</svg>
);

export const dataSourceIcon = () => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M0 4V16C0 16.6906 0.331898 17.2934 0.814984 17.7798C1.29411 18.2622 1.95054 18.6626 2.70178 18.9825C4.20606 19.6231 6.22883 20 8.42107 20C8.95883 20 9.4864 19.9773 9.99862 19.9336C9.31976 19.5859 8.7352 19.0933 8.29076 18.4995C6.32154 18.4848 4.57839 18.1378 3.34766 17.6137C2.71785 17.3455 2.25666 17.0456 1.96342 16.7503C1.67415 16.4591 1.57895 16.2069 1.57895 16V6.39242C1.91678 6.61282 2.29541 6.80942 2.70178 6.98248C4.20606 7.6231 6.22883 8 8.42107 8C10.6133 8 12.6361 7.6231 14.1404 6.98248C14.5467 6.80942 14.9254 6.61282 15.2632 6.39242V11H16.0527C16.3213 11 16.585 11.0201 16.8421 11.0589V4C16.8421 3.30945 16.5102 2.70664 16.0271 2.22025C15.548 1.73784 14.8916 1.33745 14.1404 1.01752C12.6361 0.376905 10.6133 0 8.42107 0C6.22883 0 4.20606 0.376905 2.70178 1.01752C1.95054 1.33745 1.29411 1.73784 0.814984 2.22025C0.331899 2.70664 0 3.30945 0 4ZM1.57895 4C1.57895 3.79309 1.67415 3.54091 1.96342 3.24966C2.25666 2.95441 2.71785 2.65449 3.34766 2.38628C4.60547 1.85063 6.39849 1.5 8.42107 1.5C10.4436 1.5 12.2367 1.85063 13.4945 2.38628C14.1243 2.65449 14.5855 2.95441 14.8787 3.24966C15.168 3.54091 15.2632 3.79309 15.2632 4C15.2632 4.20691 15.168 4.45909 14.8787 4.75034C14.5855 5.04559 14.1243 5.3455 13.4945 5.61372C12.2367 6.14937 10.4436 6.5 8.42107 6.5C6.39849 6.5 4.60547 6.14937 3.34766 5.61372C2.71785 5.3455 2.25666 5.04559 1.96342 4.75034C1.67415 4.45909 1.57895 4.20691 1.57895 4ZM20 15.75C20 13.6789 18.2327 12 16.0527 12L15.9455 12.0068C15.5602 12.0565 15.2632 12.3703 15.2632 12.75C15.2632 13.1642 15.6166 13.5 16.0527 13.5L16.2148 13.5052C17.4472 13.5844 18.4211 14.5591 18.4211 15.75C18.4211 16.9926 17.3607 18 16.0527 18L16.049 18.0046L15.9419 18.0119C15.5568 18.0633 15.2614 18.3783 15.2632 18.758C15.2652 19.1722 15.6203 19.5065 16.0563 19.5046V19.5L16.2656 19.4948C18.3449 19.3913 20 17.7543 20 15.75ZM13.1579 12.75C13.1579 12.3358 12.8045 12 12.3684 12L12.1588 12.0052C10.0762 12.1087 8.42107 13.7457 8.42107 15.75C8.42107 17.8211 10.1884 19.5 12.3684 19.5L12.4756 19.4932C12.8609 19.4435 13.1579 19.1297 13.1579 18.75C13.1579 18.3358 12.8045 18 12.3684 18L12.2063 17.9948C10.9739 17.9156 10 16.9409 10 15.75C10 14.5074 11.0604 13.5 12.3684 13.5L12.4756 13.4932C12.8609 13.4435 13.1579 13.1297 13.1579 12.75ZM16.8421 15.75C16.8421 15.3358 16.4887 15 16.0527 15H12.3684L12.2613 15.0068C11.876 15.0565 11.579 15.3703 11.579 15.75C11.579 16.1642 11.9324 16.5 12.3684 16.5H16.0527L16.1598 16.4932C16.5451 16.4435 16.8421 16.1297 16.8421 15.75Z"
		/>
	</svg>
);

export const eventsIcon = () => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M16.3889 0C18.3833 0 20 1.61675 20 3.61111V16.3889C20 18.3833 18.3833 20 16.3889 20H3.61111C1.61675 20 0 18.3833 0 16.3889V3.61111C0 1.61675 1.61675 0 3.61111 0H16.3889ZM18.3333 6.11111H1.66667V16.3889C1.66667 17.4628 2.53722 18.3333 3.61111 18.3333H16.3889C17.4628 18.3333 18.3333 17.4628 18.3333 16.3889V6.11111ZM16.3889 1.66667H3.61111C2.53722 1.66667 1.66667 2.53722 1.66667 3.61111V4.44444H18.3333V3.61111C18.3333 2.53722 17.4628 1.66667 16.3889 1.66667Z"
		/>
	</svg>
);

export const answersForEventsIcon = () => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M14.75 0C16.5449 0 18 1.45507 18 3.25V9.02182C17.5368 8.72526 17.0335 8.48584 16.5 8.3135V5.5H1.5V14.75C1.5 15.7165 2.2835 16.5 3.25 16.5H8.3135C8.48584 17.0335 8.72526 17.5368 9.02182 18H3.25C1.45507 18 0 16.5449 0 14.75V3.25C0 1.45507 1.45507 0 3.25 0H14.75ZM14.75 1.5H3.25C2.2835 1.5 1.5 2.2835 1.5 3.25V4H16.5V3.25C16.5 2.2835 15.7165 1.5 14.75 1.5ZM20 14.5C20 17.5376 17.5376 20 14.5 20C11.4624 20 9 17.5376 9 14.5C9 11.4624 11.4624 9 14.5 9C17.5376 9 20 11.4624 20 14.5ZM14.5 11C14.2239 11 14 11.2239 14 11.5V15.5C14 15.7761 14.2239 16 14.5 16C14.7761 16 15 15.7761 15 15.5V11.5C15 11.2239 14.7761 11 14.5 11ZM14.5 18.125C14.8452 18.125 15.125 17.8452 15.125 17.5C15.125 17.1548 14.8452 16.875 14.5 16.875C14.1548 16.875 13.875 17.1548 13.875 17.5C13.875 17.8452 14.1548 18.125 14.5 18.125Z"
		/>
	</svg>
);

export const notificationsIcons = () => (
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M20 5.2381C20 2.34518 17.408 0 14.2105 0C11.0131 0 8.42105 2.34518 8.42105 5.2381C8.42105 8.13102 11.0131 10.4762 14.2105 10.4762C17.408 10.4762 20 8.13102 20 5.2381ZM14.7375 5.7143L14.738 8.09859C14.738 8.36158 14.5024 8.57478 14.2117 8.57478C13.921 8.57478 13.6854 8.36158 13.6854 8.09859L13.6849 5.7143H11.0485C10.7578 5.7143 10.5222 5.5011 10.5222 5.2381C10.5222 4.97511 10.7578 4.76191 11.0485 4.76191H13.6847L13.6842 2.38025C13.6842 2.11726 13.9199 1.90406 14.2105 1.90406C14.5012 1.90406 14.7368 2.11726 14.7368 2.38025L14.7373 4.76191H17.3716C17.6623 4.76191 17.8979 4.97511 17.8979 5.2381C17.8979 5.5011 17.6623 5.7143 17.3716 5.7143H14.7375ZM17.3684 12.381V10.7312C17.9484 10.4577 18.4797 10.1112 18.9474 9.70517V16.9048C18.9474 18.5553 17.5195 19.904 15.7204 19.9951L15.5263 20H3.42105C1.59681 20 0.106057 18.7081 0.00541562 17.0804L0 16.9048V5.95238C0 4.30188 1.42785 2.9531 3.22692 2.86204L3.42105 2.85714H7.8928C7.68466 3.30874 7.53394 3.78742 7.44889 4.28571H3.42105C2.45455 4.28571 1.6619 4.95915 1.58505 5.81569L1.57895 5.95238V12.381H6.31579C6.71547 12.381 7.04578 12.6497 7.09806 12.9983L7.10526 13.0952C7.10526 14.2787 8.16564 15.2381 9.47368 15.2381C10.7272 15.2381 11.7533 14.357 11.8366 13.242L11.8421 13.0952C11.8421 12.7336 12.1391 12.4348 12.5245 12.3875L12.6316 12.381H17.3684ZM1.57895 13.8095V16.9048C1.57895 17.7792 2.32327 18.4964 3.26997 18.5659L3.42105 18.5714H15.5263C16.4928 18.5714 17.2855 17.898 17.3623 17.0415L17.3684 16.9048V13.8095H13.3421C12.9899 15.3793 11.4997 16.5752 9.68452 16.6617L9.47368 16.6667C7.63471 16.6667 6.08945 15.5289 5.65092 13.989L5.60528 13.8095H1.57895Z"
		/>
	</svg>
);

export const checkMarkIcon = (
	<svg
		width="14"
		height="14"
		viewBox="0 0 20 21"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			d="M7.55176 18.7852L18.4265 2.00001"
			stroke="#2FAE62"
			strokeWidth="3"
			strokeLinecap="round"
		/>
		<path
			d="M7.55176 18.7852L2.00008 10.4678"
			stroke="#2FAE62"
			strokeWidth="3"
			strokeLinecap="round"
		/>
	</svg>
);

export const redCrossIcon = (
	<svg
		width="15"
		height="18"
		viewBox="0 0 15 18"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			d="M1 16.6611L14.1252 1.33741"
			stroke="#E4151A"
			stroke-width="1.5"
			stroke-linecap="round"
		/>
		<path
			d="M14.1252 16.6611L1.00008 1.33741"
			stroke="#E4151A"
			stroke-width="1.5"
			stroke-linecap="round"
		/>
	</svg>
);

export const shipRentalIcon = () => (
	<svg
		width="24"
		height="24"
		viewBox="0 0 24 24"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M5.44158 15.6133H4.11011L4.11011 21.3867L5.44158 21.3867L5.44158 15.6133ZM4.11011 14.6133C3.55782 14.6133 3.11011 15.061 3.11011 15.6133V21.3867C3.11011 21.939 3.55782 22.3867 4.11011 22.3867H5.44158C5.85569 22.3867 6.21101 22.135 6.36288 21.7762H8.63762L12.3682 22.1493C13.6412 22.2766 14.9219 21.998 16.027 21.3534L20.1857 18.9274C20.9464 18.4837 21.1762 17.4907 20.6876 16.7579C20.2597 16.116 19.4209 15.8933 18.731 16.2382L18.4568 16.3753L14.6059 17.8194C14.3206 16.9567 13.5076 16.3343 12.5493 16.3343H11.3487C11.0943 16.3343 10.8417 16.2933 10.6004 16.2128L9.24969 15.7626C8.3175 15.4519 7.34701 15.2738 6.36669 15.2329C6.21701 14.8693 5.85919 14.6133 5.44158 14.6133H4.11011ZM8.6875 20.7762H8.66256H6.44158V16.2376C7.28888 16.2834 8.12701 16.4425 8.93346 16.7113L10.2842 17.1615C10.6274 17.2759 10.9869 17.3343 11.3487 17.3343H12.5493C13.1814 17.3343 13.696 17.8373 13.7145 18.4649L11.184 18.0432C11.0116 18.0144 10.8371 18 10.6623 18H9.50174C9.43321 17.9647 9.35546 17.9448 9.27305 17.9448H8.05207C7.77593 17.9448 7.55207 18.1686 7.55207 18.4448V18.5552C7.55207 18.8314 7.77593 19.0552 8.05207 19.0552H9.27305C9.35546 19.0552 9.43321 19.0353 9.50174 19H10.6623C10.782 19 10.9015 19.0099 11.0196 19.0296L13.5851 19.4572C14.045 19.5338 14.4727 19.2752 14.6404 18.8745L18.8325 17.3024L18.8571 17.2932L18.8806 17.2815L19.1782 17.1327C19.417 17.0132 19.7074 17.0904 19.8556 17.3126C20.0247 17.5663 19.9452 17.91 19.6818 18.0637L15.5231 20.4896C14.6002 21.0279 13.5308 21.2606 12.4677 21.1542L8.71231 20.7787L8.6875 20.7762Z"
		/>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M8.79357 1.57448C8.79357 1.2572 9.05077 1 9.36805 1H14.3469C14.6641 1 14.9213 1.2572 14.9213 1.57448V3.29791H16.6448C16.962 3.29791 17.2192 3.55511 17.2192 3.87239V7.47348L18.4983 7.92984C18.6524 7.98484 18.7758 8.10298 18.8374 8.25461C18.899 8.40624 18.893 8.57696 18.821 8.72391L17.1946 12.0393C17.3741 12.1152 17.5 12.2929 17.5 12.5C17.5 12.7761 17.2761 13 17 13H8.315L8.31598 13.0021C8.31543 13.0014 8.31487 13.0007 8.31432 13H7C6.72386 13 6.5 12.7761 6.5 12.5C6.5 12.3604 6.55718 12.2342 6.64938 12.1435L5.0536 8.71321C4.98579 8.56743 4.98216 8.39991 5.04362 8.25133C5.10507 8.10276 5.22597 7.98674 5.37695 7.93145L6.49566 7.52182V3.87239C6.49566 3.55511 6.75286 3.29791 7.07014 3.29791H8.79357V1.57448ZM7.8498 12H15.9342L17.506 8.7957L12.3163 6.944C12.0214 6.83878 11.6989 6.84012 11.4049 6.94778L6.35903 8.79541L7.8498 12ZM9.94252 3.29791H13.7724V2.14895H9.94252V3.29791ZM16.0703 7.06353V4.44686H7.64462V7.10111L11.0099 5.86888C11.5559 5.66893 12.1547 5.66645 12.7024 5.86187L16.0703 7.06353Z"
		/>
	</svg>
);

export const passagesIcon = () => (
	<svg
		width="23"
		height="19"
		style={{ marginRight: 7 }}
		viewBox="0 0 23 19"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M14.0256 12.5747V0.512604H17.8529C19.0193 0.512604 19.9417 0.767753 20.6203 1.27805C21.3044 1.78835 21.6465 2.45286 21.6465 3.27158C21.6465 3.95571 21.453 4.55013 21.0661 5.05482C20.6791 5.55951 20.1436 5.9184 19.4595 6.13149V6.16513C20.2894 6.26046 20.9511 6.56889 21.4446 7.0904C21.9437 7.60631 22.1932 8.27923 22.1932 9.10916C22.1932 10.141 21.7866 10.9765 20.9735 11.6158C20.1604 12.2551 19.1342 12.5747 17.8949 12.5747H14.0256ZM16.0192 2.1192V5.55109H17.3145C18.0099 5.55109 18.5538 5.38847 18.9464 5.06323C19.3445 4.73237 19.5436 4.26694 19.5436 3.66692C19.5436 2.63511 18.8538 2.1192 17.4744 2.1192H16.0192ZM16.0192 7.15769V10.9765H17.7267C18.4725 10.9765 19.0473 10.8055 19.4511 10.4634C19.8604 10.1157 20.0651 9.63909 20.0651 9.03346C20.0651 7.78295 19.1987 7.15769 17.4659 7.15769H16.0192Z"
		/>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M12.3603 12.5747H10.1649L9.07982 9.50447H4.33573L3.2927 12.5747H1.10571L5.62269 0.512573H7.87697L12.3603 12.5747ZM8.54989 7.87264L6.87601 3.06126C6.82554 2.90424 6.77226 2.6519 6.71619 2.30422H6.68254C6.63207 2.62386 6.57599 2.87621 6.51431 3.06126L4.85724 7.87264H8.54989Z"
		/>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M5.52663 17.2402C5.27773 18.2506 4.36553 19 3.27832 19C1.99954 19 0.962891 17.9633 0.962891 16.6845C0.962891 15.4058 1.99954 14.3691 3.27832 14.3691C4.37795 14.3691 5.29855 15.1357 5.5349 16.1636H17.5307C17.7671 15.1357 18.6877 14.3691 19.7873 14.3691C21.0661 14.3691 22.1027 15.4058 22.1027 16.6845C22.1027 17.9633 21.0661 19 19.7873 19C18.7001 19 17.7879 18.2506 17.539 17.2402H5.52663Z"
		/>
	</svg>
);

export const cargoIcon = () => (
	<svg
		width="22"
		height="23"
		viewBox="0 0 22 23"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M10.592 4.03523C10.5879 4.01181 10.5858 3.98771 10.5858 3.96311V0.414056C10.5858 0.185379 10.7711 0 10.9998 0C11.2285 0 11.4139 0.185379 11.4139 0.414056V3.96311C11.4139 3.98763 11.4117 4.01165 11.4077 4.035C11.8602 4.20107 12.1832 4.6358 12.1832 5.14596C12.1832 5.27776 12.1616 5.40451 12.1218 5.52291C12.2471 5.50432 12.3793 5.54324 12.4757 5.63968L18.3908 11.5548C18.4205 11.5844 18.4447 11.6175 18.4635 11.6526H19.8726C20.526 11.6526 21.0556 12.1823 21.0556 12.8357V21.1168C21.0556 21.7702 20.526 22.2998 19.8726 22.2998H2.12735C1.47399 22.2998 0.944336 21.7702 0.944336 21.1168V12.8357C0.944336 12.1823 1.47399 11.6526 2.12735 11.6526H3.53639C3.55518 11.6174 3.57941 11.5844 3.60909 11.5547L9.52417 5.63961C9.62071 5.54308 9.75305 5.50418 9.87845 5.5229C9.83868 5.40451 9.81713 5.27775 9.81713 5.14596C9.81713 4.63603 10.1398 4.20146 10.592 4.03523ZM10.2184 6.03393C10.2008 6.10401 10.1646 6.17037 10.1097 6.22518L4.68227 11.6526H17.3176L11.8902 6.22524C11.8354 6.17049 11.7992 6.10421 11.7815 6.03422C11.5731 6.21769 11.2996 6.32898 11.0001 6.32898C10.7005 6.32898 10.4269 6.21758 10.2184 6.03393ZM19.8726 12.4808H2.12735C1.93134 12.4808 1.77245 12.6397 1.77245 12.8357V21.1168C1.77245 21.3128 1.93134 21.4717 2.12735 21.4717H19.8726C20.0686 21.4717 20.2275 21.3128 20.2275 21.1168V12.8357C20.2275 12.6397 20.0686 12.4808 19.8726 12.4808ZM4.67074 18.7508C4.67074 18.9795 4.85612 19.1648 5.0848 19.1648C5.31347 19.1648 5.49885 18.9795 5.49885 18.7508V15.2017C5.49885 14.9731 5.31347 14.7877 5.0848 14.7877C4.85612 14.7877 4.67074 14.9731 4.67074 15.2017L4.67074 18.7508ZM7.45096 19.1648C7.22229 19.1648 7.03691 18.9795 7.03691 18.7508V15.2017C7.03691 14.9731 7.22229 14.7877 7.45096 14.7877C7.67964 14.7877 7.86502 14.9731 7.86502 15.2017V18.7508C7.86502 18.9795 7.67964 19.1648 7.45096 19.1648ZM9.40307 18.7508C9.40307 18.9795 9.58845 19.1648 9.81713 19.1648C10.0458 19.1648 10.2312 18.9795 10.2312 18.7508V15.2017C10.2312 14.9731 10.0458 14.7877 9.81713 14.7877C9.58845 14.7877 9.40307 14.9731 9.40307 15.2017V18.7508ZM12.1828 19.1648C11.9542 19.1648 11.7688 18.9795 11.7688 18.7508V15.2017C11.7688 14.9731 11.9542 14.7877 12.1828 14.7877C12.4115 14.7877 12.5969 14.9731 12.5969 15.2017V18.7508C12.5969 18.9795 12.4115 19.1648 12.1828 19.1648ZM14.135 18.7508C14.135 18.9795 14.3203 19.1648 14.549 19.1648C14.7777 19.1648 14.9631 18.9795 14.9631 18.7508V15.2017C14.9631 14.9731 14.7777 14.7877 14.549 14.7877C14.3203 14.7877 14.135 14.9731 14.135 15.2017V18.7508ZM16.9152 19.1648C16.6865 19.1648 16.5011 18.9795 16.5011 18.7508V15.2017C16.5011 14.9731 16.6865 14.7877 16.9152 14.7877C17.1439 14.7877 17.3292 14.9731 17.3292 15.2017V18.7508C17.3292 18.9795 17.1439 19.1648 16.9152 19.1648ZM11.355 5.14596C11.355 5.34197 11.1962 5.50086 11.0001 5.50086C10.8041 5.50086 10.6452 5.34197 10.6452 5.14596C10.6452 4.94995 10.8041 4.79105 11.0001 4.79105C11.1962 4.79105 11.355 4.94995 11.355 5.14596Z"
		/>
	</svg>
);

export const emissionReportIcon = () => (
	<svg
		width="21"
		height="21"
		viewBox="0 0 21 21"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M5.5 10C8.53757 10 11 12.4624 11 15.5C11 18.5376 8.53757 21 5.5 21C2.46243 21 0 18.5376 0 15.5C0 12.4624 2.46243 10 5.5 10ZM15.7488 0C16.9915 0 17.9988 1.00736 17.9988 2.25V17.75C17.9988 18.9926 16.9915 20 15.7488 20L10.1896 20.0008C10.616 19.5566 10.98 19.052 11.2672 18.5011L15.7488 18.5C16.163 18.5 16.4988 18.1642 16.4988 17.75V2.25C16.4988 1.83579 16.163 1.5 15.7488 1.5H5.25C4.83579 1.5 4.5 1.83579 4.5 2.25L4.50009 9.07643C3.97687 9.15722 3.47425 9.3004 3 9.49813V2.25C3 1.00736 4.00736 0 5.25 0H15.7488ZM5.5 17.88C5.15509 17.88 4.87549 18.1596 4.87549 18.5045C4.87549 18.8494 5.15509 19.129 5.5 19.129C5.84491 19.129 6.12451 18.8494 6.12451 18.5045C6.12451 18.1596 5.84491 17.88 5.5 17.88ZM5.50004 12.0031C5.25458 12.0031 5.05044 12.18 5.0081 12.4132L5.00004 12.5031V16.5006L5.0081 16.5905C5.05044 16.8237 5.25458 17.0006 5.50004 17.0006C5.7455 17.0006 5.94965 16.8237 5.99199 16.5905L6.00004 16.5006V12.5031L5.99199 12.4132C5.94965 12.18 5.7455 12.0031 5.50004 12.0031ZM19.75 13.0019C20.1297 13.0019 20.4435 13.2841 20.4932 13.6502L20.5 13.7519V15.25C20.5 15.6297 20.2178 15.9435 19.8518 15.9932L19.75 16H19V13.0019H19.75ZM19.75 9.00194C20.1297 9.00194 20.4435 9.28409 20.4932 9.65017L20.5 9.75194V11.25C20.5 11.6297 20.2178 11.9435 19.8518 11.9932L19.75 12H19V9.00194H19.75ZM19.75 5.00194C20.1297 5.00194 20.4435 5.28409 20.4932 5.65017L20.5 5.75194V7.25C20.5 7.6297 20.2178 7.94349 19.8518 7.99315L19.75 8H19V5.00194H19.75ZM14.2488 3C14.663 3 14.9988 3.33579 14.9988 3.75V6.2485C14.9988 6.66272 14.663 6.9985 14.2488 6.9985H6.75C6.33579 6.9985 6 6.66272 6 6.2485V3.75C6 3.33579 6.33579 3 6.75 3H14.2488ZM13.4988 4.5H7.5V5.4985H13.4988V4.5Z"
		/>
	</svg>
);

export const performanceReportIcon = () => (
	<svg
		width="21"
		height="21"
		viewBox="0 0 21 21"
		fill="currentColor"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			fill="currentColor"
			clipRule="evenodd"
			d="M5.5 10C8.53757 10 11 12.4624 11 15.5C11 18.5376 8.53757 21 5.5 21C2.46243 21 0 18.5376 0 15.5C0 12.4624 2.46243 10 5.5 10ZM15.7488 0C16.9915 0 17.9988 1.00736 17.9988 2.25V17.75C17.9988 18.9926 16.9915 20 15.7488 20L10.1896 20.0008C10.616 19.5566 10.98 19.052 11.2672 18.5011L15.7488 18.5C16.163 18.5 16.4988 18.1642 16.4988 17.75V2.25C16.4988 1.83579 16.163 1.5 15.7488 1.5H5.25C4.83579 1.5 4.5 1.83579 4.5 2.25L4.50009 9.07643C3.97687 9.15722 3.47425 9.3004 3 9.49813V2.25C3 1.00736 4.00736 0 5.25 0H15.7488ZM6.50356 12.0021H5.00572C4.79088 12.0021 4.60006 12.1393 4.5317 12.343L3.52554 15.3409C3.41682 15.6649 3.65785 16 3.99956 16H4.80169L4.02544 18.3394C3.8391 18.901 4.61055 19.2636 4.92405 18.7618L7.42105 14.7649C7.6291 14.4319 7.38967 14 6.997 14H6.5516L6.98001 12.6537C7.08263 12.3312 6.84197 12.0021 6.50356 12.0021ZM19.75 13.0019C20.1297 13.0019 20.4435 13.2841 20.4932 13.6502L20.5 13.7519V15.25C20.5 15.6297 20.2178 15.9435 19.8518 15.9932L19.75 16H19V13.0019H19.75ZM19.75 9.00194C20.1297 9.00194 20.4435 9.28409 20.4932 9.65017L20.5 9.75194V11.25C20.5 11.6297 20.2178 11.9435 19.8518 11.9932L19.75 12H19V9.00194H19.75ZM19.75 5.00194C20.1297 5.00194 20.4435 5.28409 20.4932 5.65017L20.5 5.75194V7.25C20.5 7.6297 20.2178 7.94349 19.8518 7.99315L19.75 8H19V5.00194H19.75ZM14.2488 3C14.663 3 14.9988 3.33579 14.9988 3.75V6.2485C14.9988 6.66272 14.663 6.9985 14.2488 6.9985H6.75C6.33579 6.9985 6 6.66272 6 6.2485V3.75C6 3.33579 6.33579 3 6.75 3H14.2488ZM13.4988 4.5H7.5V5.4985H13.4988V4.5Z"
		/>
	</svg>
);
