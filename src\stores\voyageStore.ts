//@ts-nocheck
import { action, observable } from "mobx";

import moment from "moment";
import type Dictionary from "../services/dictionary";
import { EntityDto } from "../services/dto/entityDto";
import type { PagedResultDto } from "../services/dto/pagedResultDto";
import { GetRoles } from "../services/user/dto/getRolesOuput";
import type { PagedVoyageResultRequestDto } from "../services/voyage/dto/PagedVoyageResultRequestDto";
import type { CreateOrUpdateVoyageInput } from "../services/voyage/dto/createOrUpdateVoyageInput";
import { GetVoyageOutput } from "../services/voyage/dto/getVoyageOutput";
import { VoyageInitializationSource } from "../services/voyage/dto/voyageInitializationSource";
import voyageService from "../services/voyage/voyageService";
import ShipStore from "./shipStore";

class VoyageStore {
	@observable voyages!: PagedResultDto<GetVoyageOutput>;
	@observable editVoyage!: CreateOrUpdateVoyageInput;
	@observable roles: GetRoles[] = [];
	@observable voyageStatuses!: Dictionary<number, string>;
	qrCodeLink!: string;
	@observable allVoyages!: GetVoyageOutput[];
	@observable voyageInitializationSources!: Dictionary<number, string>;

	shipStore: ShipStore = new ShipStore();

	@action
	async create(createVoyageInput: CreateOrUpdateVoyageInput) {
		const result = await voyageService.create(createVoyageInput);
		this.voyages.items.push(result);
	}

	@action
	async update(updateVoyageInput: CreateOrUpdateVoyageInput) {
		const result = await voyageService.update(updateVoyageInput);
		this.voyages.items = this.voyages.items.map((x: GetVoyageOutput) => {
			if (x.id === updateVoyageInput.id) x = result;
			return x;
		});
	}

	@action
	async delete(entityDto: EntityDto) {
		await voyageService.delete(entityDto);
		this.voyages.items = this.voyages.items.filter(
			(x: GetVoyageOutput) => x.id !== entityDto.id,
		);
	}

	@action
	async get(entityDto: EntityDto) {
		const result = await voyageService.get(entityDto);
		this.editVoyage = result;
		this.editVoyage.planedStart = moment(result.planedStart);
		this.editVoyage.start = result.start ? moment(result.start) : null;
		this.editVoyage.planedEnd = moment(result.planedEnd);
		this.editVoyage.end = result.end ? moment(result.end) : null;
	}

	@action
	async createVoyage() {
		await this.shipStore.getAllWithIdAndName();
		const shipId = this.shipStore.allShips[0]?.id;

		this.editVoyage = {
			roleNames: [],
			id: 0,
			voyageNumber: "",
			qrCode: "",
			secretPin: "",
			destinationFrom: "",
			destinationTo: "",
			planedStart: moment(),
			planedEnd: moment(),
			start: null,
			end: null,
			status: 0,
			isArchived: false,
			isActive: false,
			shipId: shipId,
			deviceId: "",
			initializationSource: VoyageInitializationSource.Server,
			callSign: "",
			shipName: "",
		};
		this.roles = [];
	}

	@action
	async getAll(pagedFilterAndSortedRequest: PagedVoyageResultRequestDto) {
		const result = await voyageService.getAll(pagedFilterAndSortedRequest);
		this.voyages = result;
	}

	@action
	async getVoyageStatuses() {
		const result = await voyageService.voyageStatuses();
		this.voyageStatuses = result;
	}

	@action
	async downloadQrCodeLink(entityDto: EntityDto) {
		const result = await voyageService.downloadQrCodeLink(entityDto);
		this.qrCodeLink = result;
	}

	@action
	async getAllWithIdAndNumber() {
		const result = await voyageService.getAllWithIdAndNumber();
		this.allVoyages = result?.items;
	}

	@action
	async getVoyageInitializationSources() {
		const result = await voyageService.voyageInitializationSources();
		this.voyageInitializationSources = result;
	}
}

export default VoyageStore;
