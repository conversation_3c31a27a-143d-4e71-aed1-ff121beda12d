export interface SingleNotification {
	consumptionResult: {
		consumption24h: string;
		consumptionPerHour: string;
		consumptionTotal: string;
		name: string;
	};
	guid: string;
	creationTime: Date;
	title: string;
	description: string;
	statuses: {
		status: string;
		creationTime: Date;
	}[];
	longtitude: number;
	latitude: number;
	fileUrl: string;
	shipName: string;
	shipImo: string;
	cargoQuantity: number;
	eventId: number;
	cancelled: Date;
	completed: Date;
	received: Date;
	sent: Date;
}
