import Endpoint from "../endpoints";
import http from "../httpService";
import { GetLastChatInfoOutput } from "./dto/getLastChatInfoOutput";
import { SendMessageOutput } from "./dto/sendMessageOutput";

class ChatService {
	public async getLastChatInfo(): Promise<GetLastChatInfoOutput> {
		const result = await http.get(Endpoint.Chat.GetLastChatInfo);
		return result.data.result;
	}

	public async sendMessage(
		threadId: string,
		prompt: string,
	): Promise<SendMessageOutput[]> {
		const result = await http.post(
			Endpoint.Chat.SendMessage,
			{ prompt },
			{ params: { threadId } },
		);
		return result.data.result;
	}

	public async startThread(): Promise<string> {
		const result = await http.post(Endpoint.Chat.StartThread);
		return result.data.result;
	}
}

export default new ChatService();
