import { EntityDto } from "../dto/entityDto";
import { PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoints";
import http from "../httpService";
import { CreateOrUpdateShipInfoInput } from "./dto/CreateOrUpdateShipInfoInput";
import { PagedFilterAndSortedRequestShipInfo } from "./dto/PagedFilterAndSortedRequestShipInfo";
import { UpdateShipInfoInput } from "./dto/updateShipInfoInput";

class ShipInfoService {
	public async create(createShipInfoInput: CreateOrUpdateShipInfoInput) {
		const result = await http.post(
			Endpoint.ShipInfo.Create,
			createShipInfoInput,
		);
		return result.data.result;
	}

	public async update(updateShipInfoInput: UpdateShipInfoInput) {
		const result = await http.put(
			Endpoint.ShipInfo.Update,
			updateShipInfoInput,
		);
		return result.data.result;
	}

	public async delete(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.ShipInfo.Delete, {
			params: entityDto,
		});
		return result.data;
	}

	public async get(entityDto: EntityDto): Promise<CreateOrUpdateShipInfoInput> {
		const result = await http.get(Endpoint.ShipInfo.Get, { params: entityDto });
		return result.data.result;
	}

	public async getAll(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequestShipInfo,
	): Promise<PagedResultDto<CreateOrUpdateShipInfoInput>> {
		const result = await http.get(Endpoint.ShipInfo.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}
}

export default new ShipInfoService();
