# Docker
# Build and push an image to Azure Container Registry
# https://docs.microsoft.com/azure/devops/pipelines/languages/docker

trigger:
- master

resources:
- repo: self

variables:
  # Container registry service connection established during pipeline creation
  dockerRegistryServiceConnection: '************************************'
  imageRepository: 'trust-frontend'
  containerRegistry: 'asoft.azurecr.io'
  dockerfilePath: '$(Build.SourcesDirectory)/dockerfile'
  tag: 'latest'

stages:
- stage: Build
  displayName: Build and push stage
  jobs:  
  - job: Build
    displayName: Build
    pool: 'Default'
    steps:
    - task: Docker@2
      displayName: Build and push an image to container registry
      inputs:
        command: buildAndPush
        repository: $(imageRepository)
        dockerfile: $(dockerfilePath)
        containerRegistry: $(dockerRegistryServiceConnection)
        tags: |
          $(tag)
    - task: AzureAppServiceManage@0
      inputs:
        azureSubscription: 'Microsoft Partner Network(2c8e77de-a5f5-45a5-aa37-b3344b8f50ea)'
        Action: 'Restart Azure App Service'
        ResourceGroupName: 'SODO'
        WebAppName: 'sodo'