import { PlusOutlined, SettingOutlined } from "@ant-design/icons";

import {
	<PERSON><PERSON>,
	Card,
	Col,
	Dropdown,
	Menu,
	Modal,
	Row,
	Select,
	Table,
} from "antd";
import { inject, observer } from "mobx-react";

import { WrappedFormUtils } from "@ant-design/compatible/lib/form/Form";
import { SorterResult, TablePaginationConfig } from "antd/lib/table/interface";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import { L, isGranted } from "../../lib/abpUtility";
import { EntityDto } from "../../services/dto/entityDto";
import { GetUserOutput } from "../../services/user/dto/getUserOutput";
import Stores from "../../stores/storeIdentifier";
import UserStore from "../../stores/userStore";
import utils from "../../utils/utils";
import { ModalType } from "../ModalConsts";
import { getTablePaginationOptions, renderCheckboxValue } from "../renderUtils";
import CreateOrUpdateUser from "./components/createOrUpdateUser";
import { ColumnProps } from "antd/lib/table";

export interface IUserProps {
	userStore: UserStore;
}

export interface IUserState {
	modalVisible: boolean;
	maxResultCount: number;
	skipCount: number;
	userId: number;
	filter: string;
	role: string | undefined;
	sorters: SorterResult<GetUserOutput>[];
	loading: boolean;
}

const confirm = Modal.confirm;
const { Option } = Select;
// const Search = Input.Search;

type PreviousState = {
	role: IUserState["role"];
	sorters: IUserState["sorters"];
};

@inject(Stores.UserStore)
@observer
class User extends AppComponentBase<IUserProps, IUserState> {
	formRef?: WrappedFormUtils;

	state: IUserState = {
		modalVisible: false,
		maxResultCount: 10,
		skipCount: 0,
		userId: 0,
		filter: "",
		role: undefined,
		sorters: [],
		loading: false,
	};

	async componentDidMount() {
		const prevState = this.extractPreviousState();

		this.setState({ ...prevState }, async () => {
			await Promise.all([this.getAll(), this.getRoles()]);
		});
	}

	extractPreviousState(): PreviousState {
		let state: PreviousState = {
			role: undefined,
			sorters: [],
		};

		const prevState =
			utils.getSortAndFilterFromStorage<PreviousState>("user-filters");

		if (prevState)
			state = {
				...prevState,
			};

		utils.removeStateFromStorage("user-filters");

		return state;
	}

	componentWillUnmount(): void {
		const settings: PreviousState = {
			sorters: this.state.sorters,
			role: this.state.role,
		};

		utils.saveSortAndFilterToStorage("user-filters", settings);
	}

	async getAll() {
		this.setState({ loading: true });
		const sortString = utils.getSorterString(this.state.sorters);
		await this.props.userStore.getAll(
			{
				maxResultCount: this.state.maxResultCount,
				skipCount: this.state.skipCount,
				keyword: this.state.filter,
				sorting: sortString,
			},
			this.state.role ?? undefined,
		);
		this.setState({ loading: false });
	}

	async getRoles() {
		await this.props.userStore.getRoles();
	}

	handleTableChange = (
		pagination: TablePaginationConfig,
		_: Partial<Record<keyof GetUserOutput, string[]>>,
		sorter: SorterResult<GetUserOutput> | SorterResult<GetUserOutput>[],
	) => {
		const sorters = utils.getSorters(sorter);
		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				sorters: sorters,
			},
			async () => await this.getAll(),
		);
	};

	Modal = () => {
		this.setState({
			modalVisible: !this.state.modalVisible,
		});
	};

	async createOrUpdateModalOpen(entityDto: EntityDto) {
		if (entityDto.id === 0) {
			await this.props.userStore.createUser();
			await this.props.userStore.getRoles();
		} else {
			await this.props.userStore.get(entityDto);
			await this.props.userStore.getRoles();
		}

		this.setState({ userId: entityDto.id });
		this.Modal();

		if (!this.formRef) return;

		this.formRef.setFieldsValue({
			...this.props.userStore.editUser,
			roleNames: this.props.userStore.editUser.roleNames,
		});
	}

	delete(input: EntityDto) {
		const self = this;
		confirm({
			title: "Do you Want to delete these items?",
			onOk() {
				self.props.userStore.delete(input);
			},
			onCancel() {
				console.log("Cancel");
			},
		});
	}

	handleCreate = () => {
		if (!this.formRef) return;
		const form = this.formRef;

		// biome-ignore lint/suspicious/noExplicitAny: Poor type implementation from library
		form.validateFields(async (err: any, values: any) => {
			if (err) {
				return;
			}
			this.setState({ loading: true });
			try {
				if (this.state.userId === 0) {
					await this.props.userStore.create(values);
				} else {
					await this.props.userStore.update({
						id: this.state.userId,
						...values,
					});
				}
				await this.getAll();
				this.setState({ modalVisible: false });
				form.resetFields();
			} catch (ex) {}

			this.setState({ loading: false });
		});
	};

	// biome-ignore lint/suspicious/noExplicitAny: Poor type implementation from library
	saveFormRef = (formRef: any) => {
		if (!formRef) return;
		this.formRef = formRef.props.form;
	};

	handleSearch = (value: string) => {
		this.setState({ filter: value }, async () => await this.getAll());
	};

	public render() {
		const { users } = this.props.userStore;
		const paginationOptions = getTablePaginationOptions(users?.totalCount);
		const { roles } = this.props.userStore;
		const columns: Array<ColumnProps<GetUserOutput>> = [
			{
				title: L("FullName"),
				dataIndex: "fullName",
				key: "name",
				width: 200,
				render: (text: string) => <div>{text}</div>,
				sorter: { multiple: 1 },
				sortOrder: this.state.sorters.find((x) => x.columnKey === "name")
					?.order,
			},
			{
				title: L("Company"),
				dataIndex: "userName",
				key: "userName",
				width: 200,
				render: (text: string) => <div>{text}</div>,
				sorter: { multiple: 2 },
				sortOrder: this.state.sorters.find((x) => x.columnKey === "userName")
					?.order,
			},
			{
				title: L("EmailAddress"),
				dataIndex: "emailAddress",
				key: "emailAddress",
				width: 200,
				render: (text: string) => <div>{text}</div>,
				sorter: { multiple: 3 },
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "emailAddress",
				)?.order,
			},
			{
				title: L("Type"),
				dataIndex: "type",
				key: "type",
				width: 200,
				render: (text: string) => <div>{text}</div>,
				sorter: { multiple: 4 },
				sortOrder: this.state.sorters.find((x) => x.columnKey === "type")
					?.order,
			},
			{
				title: L("IsActive"),
				dataIndex: "isActive",
				key: "isActive",
				width: 200,
				render: renderCheckboxValue,
			},
			{
				title: L("Actions"),
				fixed: "right" as const,
				width: 150,
				render: (text: string, item: GetUserOutput) => (
					<div>
						<Dropdown
							trigger={["click"]}
							overlay={
								<Menu>
									{isGranted("Pages.Users-Edit") && (
										<Menu.Item
											onClick={() =>
												this.createOrUpdateModalOpen({ id: item.id })
											}
										>
											{L("Edit")}
										</Menu.Item>
									)}
									{isGranted("Pages.Users-Delete") && (
										<Menu.Item onClick={() => this.delete({ id: item.id })}>
											{L("Delete")}
										</Menu.Item>
									)}
								</Menu>
							}
							placement="bottomLeft"
						>
							<Button type="primary" icon={<SettingOutlined />}>
								{L("Actions")}
							</Button>
						</Dropdown>
					</div>
				),
			},
		];

		if (!isGranted("Pages.Users-Edit") && !isGranted("Pages.Users-Delete")) {
			columns.pop();
		}

		return (
			<Card>
				<Row>
					<Col span={11} style={{ display: "flex", justifyContent: "start" }}>
						{roles && (
							<span>
								Filter by role: <br />
								<Select
									showSearch
									value={this.state.role}
									style={{
										width: "200px",
										height: "min-content",
										marginTop: "10px",
									}}
									onChange={(value: string) =>
										this.setState({ role: value }, () => this.getAll())
									}
									placeholder={L("Roles")}
								>
									<Option value={undefined}>{L("All")}</Option>
									{roles.map((role) => (
										<Option key={role.id} value={role.name}>
											{role.displayName}
										</Option>
									))}
								</Select>
							</span>
						)}
					</Col>
					<Col
						span={12}
						style={{
							display: "flex",
							justifyContent: "end",
							alignItems: "end",
						}}
					>
						{isGranted("Pages.Users-Add") && (
							<Button
								type="primary"
								shape="circle"
								icon={<PlusOutlined />}
								style={{
									display: "flex",
									justifyContent: "center",
									alignItems: "center",
								}}
								onClick={() => this.createOrUpdateModalOpen({ id: 0 })}
							/>
						)}
					</Col>
				</Row>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey={(record) => record.id.toString()}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={users === undefined || this.state.loading}
							dataSource={users === undefined ? [] : users.items}
							onChange={this.handleTableChange}
							scroll={{ y: 850 }}
						/>
					</Col>
				</Row>
				<CreateOrUpdateUser
					wrappedComponentRef={this.saveFormRef}
					visible={this.state.modalVisible}
					loading={this.state.loading}
					onCancel={() =>
						this.setState({
							modalVisible: false,
						})
					}
					modalType={
						this.state.userId === 0 ? ModalType.edit : ModalType.create
					}
					onCreate={this.handleCreate}
					roles={this.props.userStore.roles}
				/>
				<Chat />
			</Card>
		);
	}
}

export default User;
