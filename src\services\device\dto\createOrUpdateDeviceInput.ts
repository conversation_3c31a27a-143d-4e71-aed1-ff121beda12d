import { EntityDto } from "../../dto/entityDto";

export interface CreateOrUpdateDeviceInput extends EntityDto {
	rank: string;
	shipName: string;
	shipImo: string;
	activationCode: number;
	codeUsed: boolean;
	codeCreatedAt: Date;
	buildNumber: number;
	status: string;
	ship: {
		shipName: string;
	};
	user: {
		rank: string;
		emailAddress: string;
		creationTime: Date;
		id: number;
		name: string;
		isActive: boolean;
		surname: string;
	};
}
