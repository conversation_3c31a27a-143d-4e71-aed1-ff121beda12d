import "../../Login/index.less";

import * as React from "react";

import { Form } from "@ant-design/compatible";
import "@ant-design/compatible/assets/index.css";

import { FormComponentProps } from "@ant-design/compatible/lib/form";
import { <PERSON><PERSON>, Card, Col, Input, Row } from "antd";
import ComponentLayout from "../../../components/Layout/ComponentLayout";
import PasswordComponentBase from "../../../components/PasswordComponentBase";
import rules from "../../../components/PasswordComponentBase/passwords.validation";
import { L } from "../../../lib/abpUtility";
import accountService from "../../../services/account/accountService";
import { ResetPasswordInput } from "../../../services/account/dto/resetPasswordInput";
const FormItem = Form.Item;

interface IResetPasswordProps extends FormComponentProps {}

interface IResetPasswordState {
	status: ResetPasswordStatus;
	error: string;
}

interface ResetPasswordForm {
	password: string;
	confirmPassword: string;
}

enum ResetPasswordStatus {
	Initial = 1,
	Error = 2,
	Succes = 3,
}

class ResetPassword extends PasswordComponentBase<
	IResetPasswordProps,
	IResetPasswordState
> {
	state = {
		status: ResetPasswordStatus.Initial,
		error: "",
	};
	send = async () => {
		this.props.form.validateFields(
			async (err: unknown, values: ResetPasswordForm) => {
				if (!err) {
					try {
						const parameters = new URLSearchParams(window.location.search);
						const email = parameters.get("email");
						const token = parameters.get("token");
						if (!email || !token) {
							this.setState({
								status: ResetPasswordStatus.Error,
								error: "Invalid state",
							});
							return;
						}

						const resetPasswordInput: ResetPasswordInput = {
							token: token,
							email: email,
							password: values.password,
						};
						await accountService.resetPassword(resetPasswordInput);
						this.setState({ status: ResetPasswordStatus.Succes });
					} catch (err) {
						// biome-ignore lint/suspicious/noExplicitAny: No error type handling implemented
						console.log((err as any).response.data.error.details);
						this.setState({
							status: ResetPasswordStatus.Error,
							// biome-ignore lint/suspicious/noExplicitAny: No error type handling implemented
							error: (err as any).response.data.error.details,
						});
					}
				}
			},
		);
	};

	render() {
		const { getFieldDecorator } = this.props.form;
		return (
			<>
				<Col className="name">
					<Form>
						<Row>
							<Row style={{ marginTop: 100 }}>
								<Col span={8} offset={8}>
									<Card>
										{this.state.status === ResetPasswordStatus.Succes ? (
											<>
												<div>
													{L("Password reset - return to the app and Sign in")}
												</div>
											</>
										) : (
											<>
												<Row>
													<FormItem
														label={L("NewPassword")}
														{...ComponentLayout.formItemLayout}
													>
														{getFieldDecorator("password", {
															rules: rules.password,
														})(<Input.Password />)}
													</FormItem>
													<FormItem
														label={L("ConfirmNewPassword")}
														{...ComponentLayout.formItemLayout}
													>
														{getFieldDecorator("confirmPassword", {
															rules: rules.confirmPassword(
																this.compareToFirstPassword,
															),
														})(<Input.Password />)}
													</FormItem>
												</Row>

												{this.state.status === ResetPasswordStatus.Error &&
													this.state.error
														.split(". ")
														.map((val: string, index: number) => {
															return (
																<>
																	<Row className={"error-message"} key={val}>
																		{val}.
																	</Row>
																</>
															);
														})}
												<Row justify="end">
													<Button
														className={"btn-login"}
														onClick={this.send}
														danger
													>
														{L("SetNewPassword")}
													</Button>
												</Row>
											</>
										)}
									</Card>
								</Col>
							</Row>
						</Row>
					</Form>
				</Col>
			</>
		);
	}
}
export default Form.create()(ResetPassword);
