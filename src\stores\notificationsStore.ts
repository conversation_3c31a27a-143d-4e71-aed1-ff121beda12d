import { action, observable } from "mobx";
import { EntityDto } from "../services/dto/entityDto";
import type { CreateOrUpdateNotificationsInput } from "../services/notifications/dto/CreateOrUpdateNotificationsInput";
import type { GetNotificationsOutput } from "../services/notifications/dto/GetNotificationsOutput";
import type { PagedFilterAndSortedRequestNotifications } from "../services/notifications/dto/PagedFilterAndSortedRequestNotifications";
import { ShipTrace } from "../services/notifications/dto/ShipTrace";
import type { UpdateCargoInput } from "../services/notifications/dto/UpdateCargoInput";
import notificationsService from "../services/notifications/notificationsService";
import { GetRoles } from "../services/user/dto/getRolesOuput";

class NotificationsStore {
	@observable notifications!: GetNotificationsOutput;
	@observable titles!: string[];
	@observable notificationShipTraces!: ShipTrace[];
	@observable editNotification!: CreateOrUpdateNotificationsInput;
	@observable roles: GetRoles[] = [];
	@observable filters: string[] = [];

	@action
	async getNotificationsSummary(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequestNotifications,
		shipId: number,
		fromLastDays?: number,
	) {
		const result = await notificationsService.getNotificationsSummary(
			pagedFilterAndSortedRequest,
			shipId,
			fromLastDays,
		);
		this.notifications = result;
	}

	@action
	async getFilters(
		filters: { keyword: string; searchColumn: string },
		property: string,
		shipId: number,
		fromLastDays?: number,
	) {
		const result = await notificationsService.getFilters(
			filters,
			property,
			shipId,
			fromLastDays,
		);

		this.filters = result;
	}

	@action
	async getTitles(shipId?: number, fromLastDays?: number) {
		const result = await notificationsService.getTitles(shipId, fromLastDays);
		this.titles = result;
	}

	@action
	async getNotificationShipTrace(guid: string) {
		const result = await notificationsService.getShipTrace(guid);
		this.notificationShipTraces = result;
	}

	@action
	async get(entityDto: EntityDto) {
		const result = await notificationsService.get(entityDto);
		this.editNotification = result;
	}

	@action
	async updateCargo(updateCargo: UpdateCargoInput) {
		await notificationsService.updateCargo(updateCargo);
	}
}

export default NotificationsStore;
