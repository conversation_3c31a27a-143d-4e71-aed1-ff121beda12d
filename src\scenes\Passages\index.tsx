import { SearchOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Card, Col, Input, Row, Table } from "antd";
import { ColumnProps } from "antd/lib/table";
import {
	FilterDropdownProps,
	TablePaginationConfig,
	TableRowSelection,
} from "antd/lib/table/interface";
import { inject, observer } from "mobx-react";
import * as React from "react";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import { L } from "../../lib/abpUtility";
import { EntityDto } from "../../services/dto/entityDto";
import { ListResultDto } from "../../services/dto/pagedResultDto";
import { CreateOrUpdatePassageInput } from "../../services/passages/dto/createOrUpdatePassageInput";
import { GetPassagesOutput } from "../../services/passages/dto/getPassagesOutput";
import PassagesStore from "../../stores/passagesStore";
import Stores from "../../stores/storeIdentifier";
import {
	getTablePaginationOptions,
	renderCheckboxCustomValue,
	renderDate,
	renderSearchIcon,
	renderTimeSpan,
} from "../renderUtils";

export interface IPassagesProps {
	passagesStore: PassagesStore;
}

export interface IPassagesState {
	maxResultCount: number;
	skipCount: number;
	sorting: string;
	searchTextInsideTable: Array<{ index: string; searchText: string }>;
	searchTextTable: string;
	searchedColumnTable: string;
	selectedRows: ListResultDto<EntityDto>;
	searchColumn: string;
	loading: boolean;
}

@inject(Stores.PassagesStore)
@observer
class Passages extends AppComponentBase<IPassagesProps, IPassagesState> {
	state = {
		maxResultCount: 10,
		skipCount: 0,
		sorting: "",
		searchTextInsideTable: [{ index: "", searchText: "" }],
		searchTextTable: "",
		searchedColumnTable: "",
		selectedRows: {
			items: [],
		},
		searchColumn: "",
		loading: false,
	};

	async componentDidMount() {
		await this.getAll();
	}

	async getAll() {
		this.setState({ loading: true });
		await this.props.passagesStore.getAll({
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: this.state.sorting,
			searchColumn: this.state.searchColumn,
		});
		this.setState({ loading: false });
	}

	getColumnSearchProps = (
		dataIndex: string,
	): ColumnProps<GetPassagesOutput> => ({
		filterDropdown: (props: FilterDropdownProps) => (
			<div style={{ padding: 8 }}>
				<Input
					ref={(node) => {
						if (!node) return;
					}}
					autoFocus
					placeholder={`Search ${dataIndex}`}
					value={props.selectedKeys ? props.selectedKeys[0] : ""}
					onChange={(e) => {
						if (!props.setSelectedKeys) return;
						props.setSelectedKeys(e.target.value ? [e.target.value] : []);
					}}
					onPressEnter={() =>
						this.handleSearchTable(
							props.selectedKeys || [],
							dataIndex,
							props.confirm,
						)
					}
					style={{ width: 188, marginBottom: 8, display: "block" }}
				/>
				<Button
					type="primary"
					onClick={() =>
						this.handleSearchTable(
							props.selectedKeys || [],
							dataIndex,
							props.confirm,
						)
					}
					icon={<SearchOutlined />}
					size="small"
					style={{ width: 90, marginRight: 8 }}
				>
					Search
				</Button>
				<Button
					onClick={() => this.handleReset(dataIndex, props.clearFilters)}
					size="small"
					style={{ width: 90 }}
				>
					Reset
				</Button>
			</div>
		),
		filterIcon: renderSearchIcon,
	});

	handleSearchTable = (
		selectedKeys: React.Key[],
		dataIndex: string,
		confirm?: () => void,
	) => {
		if (dataIndex === "title") {
			this.setState(
				{ sorting: selectedKeys[0].toString() },
				async () => await this.getAll(),
			);
			return;
		}
		const allSearches = this.state.searchTextInsideTable;
		const filtred = allSearches.filter((x) => x.index !== dataIndex);

		filtred.push({ index: dataIndex, searchText: selectedKeys[0].toString() });
		if (confirm) confirm();
		this.setState({
			searchTextTable: selectedKeys[0].toString(),
			searchedColumnTable: dataIndex,
			searchTextInsideTable: filtred,
		});
	};
	handleTableChange = (pagination: TablePaginationConfig) => {
		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
			},
			async () => await this.getAll(),
		);
	};

	handleReset = (dataIndex: string, clearFilters?: () => void) => {
		const allSearches = this.state.searchTextInsideTable;
		const filtred = allSearches.filter((x) => x.index !== dataIndex);
		if (clearFilters) clearFilters();
		this.setState({
			searchTextTable: "",
			searchTextInsideTable: filtred,
		});
	};

	public render() {
		const { passages } = this.props.passagesStore;
		const paginationOptions = getTablePaginationOptions(passages?.totalCount);

		const columns = [
			{
				title: L("Counterparty"),
				dataIndex: "shipRental.counterParty.name",
				key: "shipRental.counterParty.name",
				width: 180,
				sorter: (a: GetPassagesOutput, b: GetPassagesOutput) =>
					a.shipRental.counterParty?.name?.length -
					b.shipRental.counterParty?.name?.length,
				...this.getColumnSearchProps("shipRental.counterParty.name"),
			},
			{
				title: L("Ship Name"),
				dataIndex: "shipRental.ship.shipName",
				key: "shipRental.ship.shipName",
				width: 180,
				sorter: (a: GetPassagesOutput, b: GetPassagesOutput) =>
					a.shipRental.ship.shipName?.length -
					b.shipRental.ship.shipName?.length,
				...this.getColumnSearchProps("shipRental.ship.shipName"),
			},
			{
				title: L("IMO"),
				dataIndex: "shipRental.ship.imoNumber",
				key: "shipRental.ship.imoNumber",
				width: 180,
				sorter: (a: GetPassagesOutput, b: GetPassagesOutput) =>
					a.shipRental.ship.imoNumber?.length -
					b.shipRental.ship.imoNumber?.length,
				...this.getColumnSearchProps("shipRental.ship.imoNumber"),
			},
			{
				title: L("Departed From"),
				dataIndex: "departedFromPort.name",
				key: "departedFromPort.name",
				width: 200,
				sorter: (a: GetPassagesOutput, b: GetPassagesOutput) =>
					a.departedFromPort.name?.length - b.departedFromPort.name?.length,
				...this.getColumnSearchProps("departedFromPort.name"),
			},
			{
				title: L("Depart Time"),
				dataIndex: "departTime",
				key: "departTime",
				width: 180,
				sorter: (a: GetPassagesOutput, b: GetPassagesOutput) =>
					new Date(a.departTime).getTime() - new Date(b.departTime).getTime(),
				render: (text: string) => renderDate(text),
				...this.getColumnSearchProps("departTime"),
			},
			{
				title: L("Arrived At"),
				dataIndex: "arrivedAtPort.name",
				key: "arrivedAtPort.name",
				width: 180,
				sorter: (a: GetPassagesOutput, b: GetPassagesOutput) =>
					a.arrivedAtPort.name?.length - b.arrivedAtPort.name?.length,
				...this.getColumnSearchProps("arrivedAtPort.name"),
			},
			{
				title: L("Arrival Time"),
				dataIndex: "arrivalTime",
				key: "arrivalTime",
				width: 180,
				sorter: (a: GetPassagesOutput, b: GetPassagesOutput) =>
					new Date(a.arrivalTime).getTime() - new Date(b.arrivalTime).getTime(),
				render: (text: string) => renderDate(text),
				...this.getColumnSearchProps("arrivalTime"),
			},
			{
				title: L("Distance Traveled"),
				dataIndex: "distanceTravelled",
				key: "distanceTravelled",
				width: 180,
				sorter: (a: GetPassagesOutput, b: GetPassagesOutput) =>
					a.distanceTravelled - b.distanceTravelled,
				...this.getColumnSearchProps("distanceTravelled"),
			},
			{
				title: L("Passage Time"),
				dataIndex: "passageTime",
				key: "passageTime",
				width: 180,
				sorter: (a: GetPassagesOutput, b: GetPassagesOutput) =>
					a.passageTime - b.passageTime,
				render: renderTimeSpan,
			},
			{
				title: L("CO2"),
				dataIndex: "cO2",
				key: "cO2",
				width: 150,
				sorter: (a: GetPassagesOutput, b: GetPassagesOutput) => a.cO2 - b.cO2,
				...this.getColumnSearchProps("cO2"),
			},
			{
				title: L("Cargo Quantity"),
				dataIndex: "cargoQuantity",
				key: "cargoQuantity",
				width: 180,
				sorter: (a: GetPassagesOutput, b: GetPassagesOutput) =>
					a.cargoQuantity - b.cargoQuantity,
				...this.getColumnSearchProps("cargoQuantity"),
			},
			{
				title: L("Fuel Consumption"),
				dataIndex: "fuelConsumption",
				key: "fuelConsumption",
				width: 300,
				render: (text: string, item: GetPassagesOutput) => (
					<>
						<div
							style={{
								display: "flex",
								justifyContent: "space-between",
								alignItems: "center",
							}}
						>
							<span style={{ textTransform: "uppercase" }}>Total</span>
							<div>
								{Number.parseFloat(
									item.fuelConsumption.consumptionTotal,
								).toFixed(2)}
							</div>
						</div>
						<div
							style={{
								display: "flex",
								justifyContent: "space-between",
								alignItems: "center",
							}}
						>
							<span style={{ textTransform: "uppercase" }}>Per Hour</span>
							<div>
								{Number.parseFloat(
									item.fuelConsumption.consumptionPerHour,
								).toFixed(2)}
							</div>
						</div>
						<div
							style={{
								display: "flex",
								justifyContent: "space-between",
								alignItems: "center",
							}}
						>
							<span style={{ textTransform: "uppercase" }}>Per 24H</span>
							<div>
								{Number.parseFloat(item.fuelConsumption.consumption24h).toFixed(
									2,
								)}
							</div>
						</div>
					</>
				),
				...this.getColumnSearchProps("fuelConsumption"),
			},
			{
				title: L("Is Active"),
				dataIndex: "isActive",
				key: "isActive",
				render: (value: boolean) =>
					renderCheckboxCustomValue(value, "Yes", "No"),
			},
		];

		const rowSelection: TableRowSelection<GetPassagesOutput> = {
			fixed: true,
			columnWidth: 60,
			onChange: (_, selectedRows: GetPassagesOutput[]) => {
				const selectedRowsItems: EntityDto[] = selectedRows.map((x) => {
					return {
						id: x.id,
					};
				});
				this.setState({
					selectedRows: {
						items: selectedRowsItems,
					},
				});
			},
		};

		return (
			<Card>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey={(record: CreateOrUpdatePassageInput) =>
								record.id.toString()
							}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={this.state.loading}
							dataSource={passages === undefined ? [] : passages.items}
							onChange={this.handleTableChange}
							rowSelection={rowSelection}
							scroll={{ x: true, y: 850 }}
						/>
					</Col>
				</Row>
				<Chat />
			</Card>
		);
	}
}

export default Passages;
