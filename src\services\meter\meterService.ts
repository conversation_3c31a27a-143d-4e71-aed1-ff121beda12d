import { EntityDto } from "../dto/entityDto";
import { PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoints";
import http from "../httpService";
import { PagedMeterResultRequestDto } from "./dto/PagedMeterResultRequestDto";
import { CreateOrUpdateMeterInput } from "./dto/createOrUpdateMeterInput";
import { GetMeterOutput } from "./dto/getMeterOutput";
import { MeterByShipResultRequestDto } from "./dto/meterByShipResultRequestDto";
import { MeterNameAndUuidDto } from "./dto/meterNameAndUuidDto";

class MeterService {
	public async create(createMeterInput: CreateOrUpdateMeterInput) {
		const result = await http.post(Endpoint.Meter.Create, createMeterInput);
		return result.data.result;
	}

	public async update(updateMeterInput: CreateOrUpdateMeterInput) {
		const result = await http.put(Endpoint.Meter.Update, updateMeterInput);
		return result.data.result;
	}

	public async delete(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.Meter.Delete, {
			params: entityDto,
		});
		return result.data;
	}

	public async get(entityDto: EntityDto): Promise<CreateOrUpdateMeterInput> {
		const result = await http.get(Endpoint.Meter.Get, { params: entityDto });
		return result.data.result;
	}

	public async getAll(
		pagedFilterAndSortedRequest: PagedMeterResultRequestDto,
	): Promise<PagedResultDto<GetMeterOutput>> {
		const result = await http.get(Endpoint.Meter.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}

	public async getMeterTypes() {
		const result = await http.get(Endpoint.Meter.GetMeterTypes);
		return result.data.result;
	}

	public async getMeasureTypes() {
		const result = await http.get(Endpoint.Meter.GetMeasureTypes);
		return result.data.result;
	}

	public async getAllForShip(
		meterByShipResultRequestDto: MeterByShipResultRequestDto,
	): Promise<PagedResultDto<GetMeterOutput>> {
		const result = await http.get(Endpoint.Meter.GetAllForShip, {
			params: meterByShipResultRequestDto,
		});
		return result.data.result;
	}

	public async getShips() {
		const result = await http.get(Endpoint.Ship.GetAll);
		return result.data.result;
	}

	public async downloadQrCodeLink(entityDto: EntityDto): Promise<string> {
		const result = await http.post(
			Endpoint.Meter.DownloadQrCodeLink,
			entityDto,
		);
		return result.data.result;
	}

	public async getConsumerTypes() {
		const result = await http.get(Endpoint.Meter.GetConsumerTypes);
		return result.data.result;
	}

	public async getFuelTypes() {
		const result = await http.get(Endpoint.Meter.GetFuelTypes);
		return result.data.result;
	}

	public async getMeasureUnits() {
		const result = await http.get(Endpoint.Meter.GetMeasureUnits);
		return result.data.result;
	}

	public async getMeterNameAndUuids(
		uuids: string[],
	): Promise<MeterNameAndUuidDto[]> {
		const result = await http.get(Endpoint.Meter.GetMeterNameAndUuids, {
			params: { uuids: uuids },
		});
		return result.data.result;
	}

	public async getMeterValidationRules() {
		const result = await http.get(Endpoint.Meter.GetMeterValidationRules);
		return result.data.result;
	}
}

export default new MeterService();
