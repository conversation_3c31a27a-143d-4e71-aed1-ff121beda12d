import { Card, Col, Row } from "antd";
import * as React from "react";
import "./index.less";
import { inject, observer } from "mobx-react";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import {
	meterConfigurationIcon,
	meterReadingsIcon,
	shipsIcon,
} from "../../components/SiderMenu/icons/icons";
// import moment from 'moment';
import { L } from "../../lib/abpUtility";
import { GetDeviceOutput } from "../../services/device/dto/getDeviceOutput";
import DeviceStore from "../../stores/deviceStore";
import MeterReadingStore from "../../stores/meterReadingStore";
import MeterStore from "../../stores/meterStore";
import ShipStore from "../../stores/shipStore";
import Stores from "../../stores/storeIdentifier";
import AllReadingsPerDayLineChart from "./components/LineChartExample";

export interface DeviceStatusItem {
	value: string;
	key: number;
}

export interface GroupedDeviceItem {
	key: any;
	devices: GetDeviceOutput[];
}

export interface GroupedDevices {
	items: GroupedDeviceItem[];
}

export interface IDashboardProps {
	shipStore: ShipStore;
	meterStore: MeterStore;
	meterReadingStore: MeterReadingStore;
	deviceStore: DeviceStore;
}

export interface IDashboardState {
	cardLoading: boolean;
	lineChartLoading: boolean;
	pieChartLoading: boolean;

	deviceStatuses: DeviceStatusItem[];
	devicesGroupedByStatus: GroupedDevices;

	todayCount: number;
	yesterdayCount: number;
	weekAgoCount: number;
}

@inject(Stores.MeterReadingStore)
@observer
export class Dashboard extends AppComponentBase<
	IDashboardProps,
	IDashboardState
> {
	async componentDidMount() {
		await this.props.meterReadingStore.getTenantSummary();

		this.setState({
			cardLoading: false,
			lineChartLoading: false,
			pieChartLoading: false,
		});
	}

	state: IDashboardState = {
		cardLoading: true,
		lineChartLoading: true,
		pieChartLoading: true,

		deviceStatuses: [],
		devicesGroupedByStatus: { items: [] },

		todayCount: 0,
		yesterdayCount: 0,
		weekAgoCount: 0,
	};

	render() {
		const { cardLoading, lineChartLoading } = this.state;

		const { tenantSummary } = this.props.meterReadingStore;

		return (
			<React.Fragment>
				<Row gutter={16} className="dashboardCard-wrapper">
					<Col
						className={"dashboardCard"}
						xs={{ span: 8, offset: 0 }}
						sm={{ span: 8, offset: 0 }}
						md={{ span: 8, offset: 0 }}
						lg={{ span: 8, offset: 0 }}
						xl={{ span: 8, offset: 0 }}
						xxl={{ span: 8, offset: 0 }}
					>
						<Card
							className={"dasboardCard-task"}
							bodyStyle={{
								padding: 1,
								paddingTop: 20,
								justifyContent: "center",
							}}
							loading={cardLoading}
							bordered={false}
						>
							<div className="dashboardCardContainer">
								<p className={"dashboardCardName"}>{L("Registered Ships: ")}</p>
								<p className={"dashboardCardCounter"}>
									{tenantSummary !== undefined ? tenantSummary.shipCount : ""}
								</p>
							</div>
							{shipsIcon({ className: "dashboardCardIcon" })}
						</Card>
					</Col>
					<Col
						className={"dashboardCard"}
						xs={{ span: 8, offset: 0 }}
						sm={{ span: 8, offset: 0 }}
						md={{ span: 8, offset: 0 }}
						lg={{ span: 8, offset: 0 }}
						xl={{ span: 8, offset: 0 }}
						xxl={{ span: 8, offset: 0 }}
					>
						<Card
							className={"dasboardCard-ticket"}
							bodyStyle={{
								padding: 1,
								paddingTop: 20,
								justifyContent: "center",
							}}
							loading={cardLoading}
							bordered={false}
						>
							<div className="dashboardCardContainer">
								<p className={"dashboardCardName"}>{L("Meters: ")} </p>
								<p className={"dashboardCardCounter"}>
									{tenantSummary !== undefined ? tenantSummary.meterCount : ""}
								</p>
							</div>
							{meterConfigurationIcon({ className: "dashboardCardIcon" })}
						</Card>
					</Col>
					<Col
						className={"dashboardCard"}
						xs={{ span: 8, offset: 0 }}
						sm={{ span: 8, offset: 0 }}
						md={{ span: 8, offset: 0 }}
						lg={{ span: 8, offset: 0 }}
						xl={{ span: 8, offset: 0 }}
						xxl={{ span: 8, offset: 0 }}
					>
						<Card
							className={"dasboardCard-comment"}
							bodyStyle={{
								padding: 1,
								paddingTop: 20,
								justifyContent: "center",
							}}
							loading={cardLoading}
							bordered={false}
						>
							<div className="dashboardCardContainer">
								<p className={"dashboardCardName"}>{L("Meters Read: ")}</p>
								<p className={"dashboardCardCounter"}>
									{tenantSummary !== undefined
										? tenantSummary.meterReadingCount
										: ""}
								</p>
							</div>
							{meterReadingsIcon({ className: "dashboardCardIcon" })}
						</Card>
					</Col>
				</Row>
				<p className={"dashboardBoxLabel"}>Readings Statistics</p>
				<Row>
					<Card
						className={"dashboardBox"}
						loading={lineChartLoading}
						bordered={false}
					>
						<AllReadingsPerDayLineChart />
					</Card>
				</Row>
				<Chat />
			</React.Fragment>
		);
	}
}

export default Dashboard;
