import * as abpTypings from "../lib/abp";

import { routers } from "../components/Router/router.config";
import { L } from "../lib/abpUtility";
import { FilterByColumn } from "../models/Sort/SortState";
import AppConsts from "../lib/appconst";
import { SorterResult } from "antd/lib/table/interface";
import moment, { Moment } from "moment";

declare let abp: any;

type CacheKey =
	(typeof AppConsts.sortCacheKeys)[keyof typeof AppConsts.sortCacheKeys];

class Utils {
	loadScript(url: string) {
		const script = document.createElement("script");
		script.type = "text/javascript";
		script.src = url;
		document.body.appendChild(script);
	}

	/**
	 * @param {Array<FilterByColumn>} filters - List of filters to be mapped
	 * @returns {[string, string]} Search column string, Keyword string
	 */
	getFilterStrings(filters: Array<FilterByColumn>): [string, string] {
		const localFilters = filters.sort((a, b) => {
			return a.value.localeCompare(b.value);
		});

		const searchColumnString = localFilters.map((x) => x.column).join(";");
		const keywordString = localFilters.map((x) => x.value).join(";");

		return [searchColumnString, keywordString];
	}

	getSorterString<T>(sorters: SorterResult<T>[]): string {
		return sorters
			.filter((x) => x.columnKey !== undefined)
			.map((x) => `${x.columnKey}${x.order === "descend" ? " DESC" : ""}`)
			.join(";");
	}

	getSorters<T>(
		sorters: SorterResult<T> | SorterResult<T>[],
	): SorterResult<T>[] {
		if (Array.isArray(sorters))
			return sorters
				.filter((x) => x.order)
				.map((x) => {
					return { columnKey: x.columnKey, order: x.order };
				});
		if (sorters.order)
			return [{ columnKey: sorters.columnKey, order: sorters.order }];
		return [];
	}

	insertUrlParam(key: string, value: string) {
		if (window.history.pushState) {
			const searchParams = new URLSearchParams(window.location.search);
			searchParams.set(key, value);
			const newurl = `${window.location.protocol}//${window.location.host}${window.location.pathname}?${searchParams.toString()}`;
			window.history.pushState({ path: newurl }, "", newurl);
		}
	}

	/**
	 * Used for saving sort and filter state in local storage
	 * @param {CacheKey} cacheKey - Unique key of page
	 * @param {Record<string, unknown>} sortAndFilters - Object of sort and filters for page
	 */
	saveSortAndFilterToStorage(
		cacheKey: CacheKey,
		sortAndFilters: Record<string, unknown>,
	) {
		const json = JSON.stringify(sortAndFilters);
		window.localStorage.setItem(cacheKey, json);
	}

	/**
	 * Used for remove sort and filter state from local storage
	 * @param {CacheKey} cacheKey - Unique key of page
	 */
	removeStateFromStorage(cacheKey: CacheKey) {
		window.localStorage.removeItem(cacheKey);
	}

	/**
	 * Used for removing sort and filter state from local storage
	 * @param {CacheKey} cacheKey - Unique key of page
	 */
	getSortAndFilterFromStorage<T = Record<string, unknown>>(
		cacheKey: CacheKey,
	): T | null {
		const json = window.localStorage.getItem(cacheKey);
		if (json === null) {
			console.warn("Did not match any settings from storage");
			return null;
		}

		return JSON.parse(json);
	}

	getUrlParam(key: string) {
		const url = new URL(window.location.href);
		return url.searchParams.get(key) || "";
	}
	getUrlNumericParam(key: string) {
		const url = new URL(window.location.href);
		const param = url.searchParams.get(key);

		if (param && !Number.isNaN(Number.parseInt(param))) {
			return Number.parseInt(param);
		}
		return undefined;
	}

	getUrlMomentParam(key: string): Moment | null {
		const url = new URL(window.location.href);
		const param = url.searchParams.get(key);

		if (param && moment(param).isValid()) {
			return moment(param);
		}
		return null;
	}

	compareNullableNumbers(a: number, b: number): number {
		const first = typeof a === 'string' || Number.isNaN(a) ? 0 : a
		const second = typeof b === 'string' || Number.isNaN(b) ? 0 : b

		return first - second
	}

	extend(...args: any[]) {
		let options;
		let name;
		let src;
		let srcType;
		let copy;
		let copyIsArray;
		let clone;
		let target = args[0] || {};
		let i = 1;
		const length = args.length;
		let deep = false;
		if (typeof target === "boolean") {
			deep = target;
			target = args[i] || {};
			i++;
		}
		if (typeof target !== "object" && typeof target !== "function") {
			target = {};
		}
		if (i === length) {
			target = this;
			i--;
		}
		for (; i < length; i++) {
			if ((options = args[i]) !== null) {
				for (name in options) {
					src = target[name];
					copy = options[name];
					if (target === copy) {
						continue;
					}
					srcType = Array.isArray(src) ? "array" : typeof src;
					if (
						deep &&
						copy &&
						((copyIsArray = Array.isArray(copy)) || typeof copy === "object")
					) {
						if (copyIsArray) {
							copyIsArray = false;
							clone = src && srcType === "array" ? src : [];
						} else {
							clone = src && srcType === "object" ? src : {};
						}
						target[name] = this.extend(deep, clone, copy);
					} else if (copy !== undefined) {
						target[name] = copy;
					}
				}
			}
		}

		return target;
	}

	getPageTitle = (pathname: string) => {
		const route = routers.filter((route) => route.path === pathname);
		const localizedAppName = L("FuelMeterManager");
		if (!route || route.length === 0) {
			return localizedAppName;
		}

		return `${L(route[0].title)} | ${localizedAppName}`;
	};

	getPageHeader = (pathname: string) => {
		const route = routers.find((route) => route.path === pathname);

		if (!route) {
			const pathNameElements = pathname.split("/");
			const lastPathNameElements =
				pathNameElements[pathNameElements.length - 1];

			return (
				lastPathNameElements[0].toUpperCase() +
				lastPathNameElements.substring(1).toLowerCase()
			);
		}

		return L(route.title);
	};

	getRoute = (path: string): any => {
		return routers.filter((route) => route.path === path)[0];
	};

	setLocalization() {
		if (!abp.utils.getCookieValue("Abp.Localization.CultureName")) {
			const language = navigator.language;
			abp.utils.setCookieValue(
				"Abp.Localization.CultureName",
				language,
				new Date(new Date().getTime() + 5 * 365 * 86400000),
				abp.appPath,
			);
		}
	}

	getCurrentClockProvider(
		currentProviderName: string,
	): abpTypings.timing.IClockProvider {
		if (currentProviderName === "unspecifiedClockProvider") {
			return abp.timing.unspecifiedClockProvider;
		}

		if (currentProviderName === "utcClockProvider") {
			return abp.timing.utcClockProvider;
		}

		return abp.timing.localClockProvider;
	}

	downloadURI(uri: string, name: string) {
		const link = document.createElement("a");
		link.download = name;
		link.href = uri;
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	}

	debounce(func: Function, timeout = 300) {
		let timer: NodeJS.Timeout;
		return (...args: any[]) => {
			clearTimeout(timer);
			timer = setTimeout(() => {
				func.apply(this, args);
			}, timeout);
		};
	}

	wait(delayInSecconds: number) {
		return new Promise((resolve) =>
			setTimeout(resolve, delayInSecconds * 1000),
		);
	}
}

export default new Utils();
