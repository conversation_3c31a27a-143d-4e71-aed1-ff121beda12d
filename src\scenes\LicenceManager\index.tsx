import * as React from "react";

import { PlusOutlined, SettingOutlined } from "@ant-design/icons";

import {
	<PERSON><PERSON>,
	Card,
	Col,
	Dropdown,
	Input,
	Menu,
	Modal,
	Row,
	Table,
} from "antd";
import { inject, observer } from "mobx-react";

import moment from "moment";
import AppComponentBase from "../../components/AppComponentBase";
import { L } from "../../lib/abpUtility";
import { EntityDto } from "../../services/dto/entityDto";
import { GetLicenseOutput } from "../../services/license/dto/getLicenseOutput";
import LicenseStore from "../../stores/licenseStore";
import Stores from "../../stores/storeIdentifier";
import { ModalType } from "../ModalConsts";
import { getTablePaginationOptions } from "../renderUtils";
import CreateOrUpdateLicence from "./components/createOrUpdateLicence";

export interface ILicenceProps {
	licenseStore: LicenseStore;
}

export interface ILicenceState {
	modalVisible: boolean;
	maxResultCount: number;
	skipCount: number;
	filter: string;
	licenceId: number;
	modalOkButtonDisabled: boolean;
}

const confirm = Modal.confirm;
const Search = Input.Search;

@inject(Stores.TenantStore)
@inject(Stores.LicenseStore)
@observer
class Tenant extends AppComponentBase<ILicenceProps, ILicenceState> {
	formRef: any;

	state: ILicenceState = {
		modalVisible: false,
		maxResultCount: 10,
		skipCount: 0,
		filter: "",
		licenceId: 0,
		modalOkButtonDisabled: true,
	};

	async componentDidMount() {
		await this.getAll();
	}

	async getAll() {
		await this.props.licenseStore.getAll({
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: "",
		});
	}

	handleTableChange = (pagination: any) => {
		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount!,
				maxResultCount: pagination.pageSize ?? 10,
			},
			async () => await this.getAll(),
		);
	};

	async createOrUpdateModalOpen(entityDto: EntityDto) {
		this.setState({ modalOkButtonDisabled: false });

		if (entityDto.id === 0) {
			this.props.licenseStore.createLicence();
		} else {
			await this.props.licenseStore.get(entityDto);
		}

		this.setState({ licenceId: entityDto.id });
		this.setState({
			modalVisible: !this.state.modalVisible,
		});

		if (entityDto.id !== 0) {
			this.formRef.props.form.setFieldsValue({
				...this.props.licenseStore.licenseModel,
			});
		} else {
			this.formRef.props.form.resetFields();
		}
	}

	async delete(input: EntityDto) {
		const self = this;
		confirm({
			title: "Do you Want to delete these items?",
			async onOk() {
				await self.props.licenseStore.delete(input);
			},
			onCancel() {},
		});
	}

	handleCreate = () => {
		this.setState({ modalOkButtonDisabled: true });
		const form = this.formRef.props.form;

		form.validateFields(async (err: any, values: GetLicenseOutput) => {
			if (err) {
				return;
			}
				if (this.state.licenceId === 0) {
					await this.props.licenseStore.create(values);
				} else {
					values.id = this.state.licenceId;
					await this.props.licenseStore.update(values);
				}

			await this.getAll();
			this.setState({ modalVisible: false });
			form.resetFields();
		});
	};

	saveFormRef = (formRef: any) => {
		this.formRef = formRef;
	};

	handleSearch = (value: string) => {
		this.setState({ filter: value }, async () => await this.getAll());
	};

	public render() {
		const { licenses } = this.props.licenseStore;
		const paginationOptions = getTablePaginationOptions(licenses?.totalCount);
		const columns = [
			{
				title: L("ExpirationDate"),
				dataIndex: "expirationDate",
				key: "expirationDate",
				render: (text: string) => (
					<a>{moment(text).format("YYYY-MM-DD hh:mm:ss")}</a>
				),
			},
			{
				title: L("LicenceDescription"),
				dataIndex: "description",
				key: "description",
			},
			{
				title: L("LicenceKey"),
				dataIndex: "key",
				key: "key",
			},
			{
				title: L("Actions"),
				width: 150,
				render: (text: string, item: any) => (
					<div>
						<Dropdown
							trigger={["click"]}
							overlay={
								<Menu>
									<Menu.Item
										onClick={() =>
											this.createOrUpdateModalOpen({ id: item.id })
										}
									>
										{L("Edit")}
									</Menu.Item>
									<Menu.Item onClick={() => this.delete({ id: item.id })}>
										{L("Delete")}
									</Menu.Item>
								</Menu>
							}
							placement="bottomLeft"
						>
							<Button type="primary" icon={<SettingOutlined />}>
								{L("Actions")}
							</Button>
						</Dropdown>
					</div>
				),
			},
		];

		return (
			<Card>
				<Row>
					<Col
						xs={{ span: 10, offset: 0 }}
						sm={{ span: 10, offset: 0 }}
						md={{ span: 10, offset: 0 }}
						lg={{ span: 10, offset: 0 }}
						xl={{ span: 10, offset: 0 }}
						xxl={{ span: 10, offset: 0 }}
					>
						<Search
							placeholder={this.L("Filter")}
							onSearch={this.handleSearch}
						/>
					</Col>
					<Col
						xs={{ span: 13, offset: 0 }}
						sm={{ span: 13, offset: 0 }}
						md={{ span: 13, offset: 0 }}
						lg={{ span: 13, offset: 0 }}
						xl={{ span: 13, offset: 0 }}
						xxl={{ span: 13, offset: 0 }}
						style={{ display: "flex", justifyContent: "right" }}
					>
						<Button
							type="primary"
							shape="circle"
							icon={<PlusOutlined />}
							onClick={() => this.createOrUpdateModalOpen({ id: 0 })}
						/>
					</Col>
				</Row>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							columns={columns}
							dataSource={licenses === undefined ? [] : licenses.items}
							pagination={paginationOptions}
							rowKey={(license) => {
								return license.key;
							}}
						/>
					</Col>
				</Row>
				<CreateOrUpdateLicence
					wrappedComponentRef={this.saveFormRef}
					visible={this.state.modalVisible}
					onCancel={() =>
						this.setState({
							modalVisible: false,
						})
					}
					modalType={
						this.state.licenceId === 0 ? ModalType.create : ModalType.edit
					}
					onCreate={this.handleCreate}
					okButtonDisabled={this.state.modalOkButtonDisabled}
				/>
			</Card>
		);
	}
}

export default Tenant;
