.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.trigger:hover {
  color: #1890ff;
}

.logo {
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px;
}
.ant-tooltip-inner{
  background-color: rgba(58, 77, 119, 0.8) !important;
}

.ant-tooltip-placement-right .ant-tooltip-arrow{
  border-color: rgba(58, 77, 119, 0.8) !important;
}

.ant-layout {
  background-color: #F7F7F7;
}

.ant-layout-header {
  margin: 20px 20px 0 0;
  background-color: #F7F7F7;
}

/* Let's get this party started */
::-webkit-scrollbar {
  width: 12px;
}

/* Track */
::-webkit-scrollbar-track {
  -webkit-border: 1px solid #E1E1E1;
  border: 1px solid #E1E1E1;
  -webkit-border-radius: 10px;
  border-radius: 10px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  -webkit-border-radius: 10px;
  border-radius: 10px;
  background: #D8E6FF;
  -webkit-border: 1px solid #E1E1E1;
  border: 1px solid #E1E1E1;
}

::-webkit-scrollbar-thumb:window-inactive {
  background: #D8E6FF;
}

.sidebar {
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 3px 10px 30px 3px rgba(0, 0, 0, 0.19);
}

.TableCardContainer {
  margin: 25px 0px 0px 0px;
}

/* Lists */
.ant-card-bordered {
  border: none;

  .ant-card-body {
      background-color: #F7F7F7;
  }
}
