import { Form } from "@ant-design/compatible";
import * as React from "react";
import "@ant-design/compatible/assets/index.css";
import { Input, Modal } from "antd";
import FormItem from "antd/lib/form/FormItem";
import AppComponentBase from "../../../components/AppComponentBase";
import ComponentLayout from "../../../components/Layout/ComponentLayout";
import TrustDropdown from "../../../components/TrustDropdown";
import { L } from "../../../lib/abpUtility";
import MailRecipientStore from "../../../stores/mailRecipientStore";
import { FormWidths } from "../../ViewSettingsConsts";
import { mailLevelCustomData } from "../../enumUtils";
import { ModalFormComponentProps } from "../../modalFormComponentProps";
import rules from "./createOrUpdateMailRecipients.validation";

export interface ICreateOrUpdateMailRecipientsProps
	extends ModalFormComponentProps {
	mailRecipientsStore: MailRecipientStore;
}

export interface ICreateOrUpdateMailRecipientsState {
	selectedFile: File | null;
}

class CreateOrUpdateMailRecipients extends AppComponentBase<
	ICreateOrUpdateMailRecipientsProps,
	ICreateOrUpdateMailRecipientsState
> {
	state = {
		selectedFile: null,
	};

	onChangeShip = (shipId: number) => {
		const { resetFields } = this.props.form;
		resetFields(["barcode"]);
	};

	render() {
		const { getFieldDecorator } = this.props.form;
		const { visible, onCancel, onCreate, okButtonDisabled } = this.props;
		const { mailLevel } = this.props.mailRecipientsStore;

		return (
			<Modal
				visible={visible}
				cancelText={L("Cancel")}
				okText={L("OK")}
				onCancel={onCancel}
				onOk={onCreate}
				title={L("Mail Recipient")}
				okButtonProps={{ disabled: okButtonDisabled }}
			>
				<Form layout="vertical">
					<FormItem
						label={L("Name")}
						{...ComponentLayout.formItemLayout}
						required
					>
						{getFieldDecorator("name", { rules: rules.name })(
							<Input style={{ width: FormWidths.wide, marginLeft: "15px" }} />,
						)}
					</FormItem>
					<FormItem
						label={L("Email")}
						{...ComponentLayout.formItemLayout}
						required
					>
						{getFieldDecorator("email", { rules: rules.email })(
							<Input style={{ width: FormWidths.wide, marginLeft: "15px" }} />,
						)}
					</FormItem>
					<FormItem
						label={L("Mail Level")}
						{...ComponentLayout.formItemLayout}
						required
					>
						<TrustDropdown
							data={mailLevel}
							placeholder={L("Select Mail Level")}
							formRef={this.props.form}
							fieldDecoratorKey={"mailLevel"}
							rules={rules.mailLevel}
							customData={mailLevelCustomData}
						/>
					</FormItem>
				</Form>
			</Modal>
		);
	}
}

export default Form.create<ICreateOrUpdateMailRecipientsProps>()(
	CreateOrUpdateMailRecipients,
);
