import { EntityDto } from "../dto/entityDto";
import { PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoints";
import http from "../httpService";
import { CreateOrUpdatePassageInput } from "./dto/createOrUpdatePassageInput";
import { GetPassagesOutput } from "./dto/getPassagesOutput";
import { PagedPassagesResultRequestDto } from "./dto/pagedPassagesResultRequestDto";
import { UpdatePassageInput } from "./dto/updatePassageInput";

class PassagesService {
	public async get(entityDto: EntityDto): Promise<CreateOrUpdatePassageInput> {
		const result = await http.get(Endpoint.Passage.Get, { params: entityDto });
		return result.data.result;
	}

	public async getAll(
		pagedFilterAndSortedRequest: PagedPassagesResultRequestDto,
	): Promise<PagedResultDto<GetPassagesOutput>> {
		const result = await http.get(Endpoint.Passage.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}

	public async delete(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.Passage.Delete, {
			params: entityDto,
		});
		return result.data;
	}

	public async update(updatePassageInput: UpdatePassageInput) {
		const result = await http.put(Endpoint.Passage.Update, updatePassageInput);
		return result.data.result;
	}

	public async create(createPassageInput: CreateOrUpdatePassageInput) {
		const result = await http.post(Endpoint.Passage.Create, createPassageInput);
		return result.data.result;
	}
}

export default new PassagesService();
