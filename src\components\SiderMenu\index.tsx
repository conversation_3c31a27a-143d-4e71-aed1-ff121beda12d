import "./index.less";

import * as React from "react";

import { Avatar, Col, Layout, Menu } from "antd";
import { L, isCurrentUserHost, isGranted } from "../../lib/abpUtility";

import { appRouters } from "../../components/Router/router.config";
import Logo from "../../images/LOGO.svg";
import {
	answersForEventsIcon,
	bunkerDeliveryNoteIcon,
	cargoIcon,
	consumptionCalculationIcon,
	consumptionFormulasIcon,
	consumptionResultsIcon,
	controlBoardIcon,
	dashboardIcon,
	dataProviderIcon,
	dataSourceIcon,
	devicesIcon,
	draftsIcon,
	emissionReportIcon,
	eventsIcon,
	fuelTypesIcon,
	integrationsIcon,
	licenceManagerIcon,
	mailRecipientsIcon,
	mapIcon,
	meterConfigurationIcon,
	meterReadingsIcon,
	notificationsIcons,
	ocrIcon,
	passagesIcon,
	performanceReportIcon,
	rolesIcon,
	shipInfoIcon,
	shipRentalIcon,
	shipsIcon,
	usersIcon,
	vesselOperatorsIcon,
	voyagesIcon,
} from "./icons/icons";

const { Sider } = Layout;

// type Route = (typeof appRouters)[0];
export interface ISiderMenuProps {
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	history: any;
}

// const groupByCategory = (xs: Route[]) =>
// 	xs.reduce((rv: Record<string, Route[]>, x) => {
// 		// biome-ignore lint/suspicious/noAssignInExpressions: <explanation>
// 		(rv[x.backgroundColor || "unknown"] ??= []).push(x);
// 		return rv;
// 	}, {});

const SiderMenu = (props: ISiderMenuProps) => {
	const { history } = props;

	const getFluentIcon = (iconType: string) => {
		const iconMap = {
			dashboard: dashboardIcon,
			map: mapIcon,
			meters: meterConfigurationIcon,
			meterReadings: meterReadingsIcon,
			ships: shipsIcon,
			shipInfo: shipInfoIcon,
			shipRental: shipRentalIcon,
			dataSource: dataSourceIcon,
			dataProvider: dataProviderIcon,
			OCR: ocrIcon,
			voyages: voyagesIcon,
			events: eventsIcon,
			passages: passagesIcon,
			cargo: cargoIcon,
			emissionReport: emissionReportIcon,
			performanceReport: performanceReportIcon,
			notifications: notificationsIcons,
			answersForEvents: answersForEventsIcon,
			users: usersIcon,
			roles: rolesIcon,
			devices: devicesIcon,
			mailRecipients: mailRecipientsIcon,
			consumptionConsumer: consumptionFormulasIcon,
			consumptionResults: consumptionResultsIcon,
			drafts: draftsIcon,
			integrations: integrationsIcon,
			fuelTypes: fuelTypesIcon,
			consumptionCalculation: consumptionCalculationIcon,
			bunkerNotes: bunkerDeliveryNoteIcon,
			vesselOperator: vesselOperatorsIcon,
			licenceManager: licenceManagerIcon,
			controlBoard: controlBoardIcon,
		} as const;

		const IconComponent = iconMap[iconType as keyof typeof iconMap];
		return IconComponent ? <IconComponent /> : null;
	};

	// const categoryGroups = React.useMemo(
	// 	() =>
	// 		groupByCategory(
	// 			appRouters
	// 				.filter((item) => !item.isLayout && item.showInMenu)
	// 				.filter((route) => route.permission && isGranted(route.permission))
	// 				.filter(
	// 					(route) =>
	// 						route.hideForHost === undefined ||
	// 						(route.hideForHost && !isCurrentUserHost()),
	// 				),
	// 		),
	// 	[appRouters],
	// );

	return (
		<Sider trigger={null} className={"sidebar"} width={256}>
			<Col style={{ textAlign: "center", marginTop: 35, marginBottom: 40 }}>
				<Avatar shape="square" style={{ width: 150 }} src={Logo} />
			</Col>
			<Menu theme="dark" mode="inline">
				{appRouters
					.filter((item) => !item.isLayout && item.showInMenu)
					.map((route, index: number) => {
						if (route.permission && !isGranted(route.permission)) return null;
						if (route.hideForHost && isCurrentUserHost()) return null;

						const IconComponent = getFluentIcon(route.icon || "default");
						return (
							<Menu.Item
								key={route.path}
								onClick={() => history.push(route.path)}
								style={
									route.backgroundColor
										? {
												backgroundColor: route.backgroundColor,
												margin: 0,
												paddingTop: "4px",
												paddingBottom: "4px",
											}
										: { backgroundColor: "transparent" }
								}
							>
								<span style={{ display: "flex", alignItems: "center" }}>
									{IconComponent}
									<span>{L(route.title)}</span>
								</span>
							</Menu.Item>
						);
					})}
			</Menu>
		</Sider>
	);
};

export default SiderMenu;
