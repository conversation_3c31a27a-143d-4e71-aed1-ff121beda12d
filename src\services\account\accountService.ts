import Endpoint from "../endpoints";
import http from "../httpService";
import { IsTenantAvaibleInput } from "./dto/isTenantAvailableInput";
import IsTenantAvaibleOutput from "./dto/isTenantAvailableOutput";
import { RegisterInput } from "./dto/registerInput";
import { RegisterOutput } from "./dto/registerOutput";
import { RequestResetPasswordInput } from "./dto/requestResetPasswordInput";
import { ResetPasswordInput } from "./dto/resetPasswordInput";

class AccountService {
	public async isTenantAvailable(
		isTenantAvaibleInput: IsTenantAvaibleInput,
	): Promise<IsTenantAvaibleOutput> {
		const result = await http.post(
			Endpoint.Account.IsTenantAvailable,
			isTenantAvaibleInput,
		);
		return result.data.result;
	}

	public async register(registerInput: RegisterInput): Promise<RegisterOutput> {
		const result = await http.post(Endpoint.Account.Register, registerInput);
		return result.data.result;
	}

	public async requestResetPassword(
		requestResetPasswordInput: RequestResetPasswordInput,
	): Promise<void> {
		const result = await http.post(
			Endpoint.Account.RequestResetPassword,
			requestResetPasswordInput,
		);
		return result.data.result;
	}

	public async resetPassword(
		resetPasswordInput: ResetPasswordInput,
	): Promise<void> {
		const result = await http.post(
			Endpoint.Account.ResetPassword,
			resetPasswordInput,
		);
		return result.data.result;
	}
}

export default new AccountService();
