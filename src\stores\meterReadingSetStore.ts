import { action, observable } from "mobx";
import { Moment } from "moment";
import { GetMeterReadingSetOutput } from "../services/meterReadingSet/dto/getMeterReadingSetOutput";
import { GetMeterReadingSetPermormanceDataOutput } from "../services/meterReadingSet/dto/getMeterReadingSetPerformanceDataOutput";
import meterReadingSetService, * as meterReadingSetService_1 from "../services/meterReadingSet/meterReadingSetService";
import utils from "../utils/utils";

class MeterReadingSetStore {
	@observable meterReadingsSets!: GetMeterReadingSetOutput[];
	@observable
	meterReadingSetPerformanceData!: GetMeterReadingSetPermormanceDataOutput[];

	@action
	async getEmissionReport(shipId: number, startDate: string, endDate: string) {
		const result: GetMeterReadingSetOutput[] =
			await meterReadingSetService.getEmissionReport(
				shipId,
				startDate,
				endDate,
			);

		for (let index = 0; index < result.length; index++) {
			const element = result[index];

			let status = "Unknown";
			if (result[index + 1]) status = result[index + 1].aisStatus;

			element.aisStatus = status;
		}

		this.meterReadingsSets = result;
	}

	@action
	async getPerformanceReport(
		shipId: number,
		stwRange: meterReadingSetService_1.ParamRange<number>,
		avgStwRange: meterReadingSetService_1.ParamRange<number>,
		sogRange: meterReadingSetService_1.ParamRange<number>,
		avgSogRange: meterReadingSetService_1.ParamRange<number>,
		wavesHeightRange: meterReadingSetService_1.ParamRange<number>,
		windSpeedRange: meterReadingSetService_1.ParamRange<number>,
		draftRange: meterReadingSetService_1.ParamRange<number>,
		durationRange: meterReadingSetService_1.ParamRange<number>,
		dateRange: meterReadingSetService_1.ParamRange<Moment>,
	) {
		const result = await meterReadingSetService.getPerformanceReport(
			shipId,
			stwRange,
			avgStwRange,
			sogRange,
			avgSogRange,
			wavesHeightRange,
			windSpeedRange,
			draftRange,
			durationRange,
			dateRange,
		);
		this.meterReadingSetPerformanceData = result;
	}

	@action
	async exportEmissionReport(shipId: number, voyageType: string, id: number[]) {
		const url = await meterReadingSetService.exportEmissionReport(
			shipId,
			voyageType,
			id,
		);
		utils.downloadURI(url, "EmissionReport.xlsx");
	}

	@action
	async exportPerformanceReport(shipId: number, id: number[]) {
		const url = await meterReadingSetService.exportPerformanceReport(
			shipId,
			id,
		);
		utils.downloadURI(url, "PerformanceReport.xlsx");
	}
}

export default MeterReadingSetStore;
