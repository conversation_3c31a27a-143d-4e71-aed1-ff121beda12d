.App {
	text-align: center;
	background: #142c52;
}

.data-title {
    width: min(calc(40% - 10px), 100px);
    margin-right: 10px;
}

.data-value {
    width: max(60%, calc(100% - min(calc(40% - 10px), 100px)));
}

.data-title-without-max {
    width: calc(40% - 10px);
    margin-right: 10px;
	white-space: nowrap;
}

.data-value-without-max {
    width: 60%;
}


.App-logo {
	animation: App-logo-spin infinite 20s linear;
	height: 80px;
}
.ant-modal-confirm-content {
	white-space: pre-line !important;
}

.App-header {
	background-color: #222;
	height: 150px;
	padding: 20px;
	color: white;
}

.App-title {
	font-size: 1.5em;
}

.App-intro {
	font-size: large;
}

@keyframes App-logo-spin {
	from {
		transform: rotate(0deg);
	}

	to {
		transform: rotate(360deg);
	}
}
