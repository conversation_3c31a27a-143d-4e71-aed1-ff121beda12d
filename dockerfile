# build environment
FROM node:12.2.0-alpine as build
WORKDIR /app
ENV PATH /app/node_modules/.bin:$PATH
COPY package.json /app/package.json
RUN yarn install
COPY . /app
RUN npm run build

# production environment
FROM nginx:1.16.0-alpine
COPY --from=build /app/build /usr/share/nginx/html
RUN rm /etc/nginx/conf.d/default.conf
COPY nginx/nginx.conf /etc/nginx/conf.d
EXPOSE 80


WORKDIR /usr/share/nginx/html
COPY ./buildEnv.py .
COPY .env .

RUN apk add python
RUN apk add --no-cache bash

RUN chmod +x buildEnv.py

CMD ["/bin/bash", "-c", "python /usr/share/nginx/html/buildEnv.py && nginx -g \"daemon off;\""]