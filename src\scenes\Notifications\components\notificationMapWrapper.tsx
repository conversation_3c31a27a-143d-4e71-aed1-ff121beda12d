import { Layout } from "antd";
import React, { useEffect, useState } from "react";
import { AzureMapsProvider } from "react-azure-maps";
import { ECABorder } from "../../../services/ais/dto/ECABorder";
import {
	ShipTrace,
	ShipTraceExtended,
} from "../../../services/notifications/dto/ShipTrace";
import { SingleNotification } from "../../../services/notifications/dto/SingleNotification";
import NotificationFooter from "./notificationFooter";
import NotificationMap from "./notificationMap";
import NotificationRightBar from "./notificationRightBar";

export interface NotificationMapProps {
	notification: SingleNotification;
	shipTraces: ShipTrace[];
	ecaBorders: ECABorder[];
}

export function getSpeedColor(speed: number): string {
	// Define hue ranges for hotter to colder colors
	const startHue = 240; // Blue (240 degrees)
	const endHue = 0; // Red (0 degrees)

	// Map the speed to a hue value within the reversed range
	const hue = startHue + (endHue - startHue) * (speed / 18);

	// Convert the HSV color to RGB and then to hexadecimal
	const [r, g, b] = hsvToRgb(hue, 1, 1);
	return rgbToHex(r * 255, g * 255, b * 255);
}

// Function to convert HSV to RGB
function hsvToRgb(h: number, s: number, v: number): [number, number, number] {
	const c = v * s;
	const hh = h / 60;
	const x = c * (1 - Math.abs((hh % 2) - 1));
	let r = 0;
	let g = 0;
	let b = 0;
	if (0 <= hh && hh < 1) {
		[r, g, b] = [c, x, 0];
	} else if (1 <= hh && hh < 2) {
		[r, g, b] = [x, c, 0];
	} else if (2 <= hh && hh < 3) {
		[r, g, b] = [0, c, x];
	} else if (3 <= hh && hh < 4) {
		[r, g, b] = [0, x, c];
	} else if (4 <= hh && hh < 5) {
		[r, g, b] = [x, 0, c];
	} else if (5 <= hh && hh < 6) {
		[r, g, b] = [c, 0, x];
	}
	const m = v - c;
	return [r + m, g + m, b + m];
}

// Function to convert RGB components to a hexadecimal color string
function rgbToHex(r: number, g: number, b: number): string {
	const toHex = (c: number) => Math.round(c).toString(16).padStart(2, "0");
	return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
}

const NotificationMapWrapper: React.FC<NotificationMapProps> = ({
	notification,
	shipTraces,
	ecaBorders,
}) => {
	const [footerData, setFooterData] = useState<ShipTrace | null>(null);
	const [shipTracesExt, setShipTracesExt] = useState<ShipTraceExtended[]>([]);
	const [ecaVisible, setEcaVisible] = useState<boolean>(false);
	const { Footer, Sider, Content } = Layout;

	useEffect(() => {
		const shipTracesExtNew: ShipTraceExtended[] = [];
		shipTraces.forEach((shipTrace: ShipTrace) => {
			const shipTraceExt: ShipTraceExtended = shipTrace as ShipTraceExtended;
			shipTraceExt.LineColor = getSpeedColor(shipTrace.SOG);

			shipTracesExtNew.push(shipTraceExt);
		});

		setShipTracesExt(shipTracesExtNew);
	}, []);

	const siderStyle: React.CSSProperties = {
		textAlign: "center",
		lineHeight: "24px",
		color: "#fff",
		margin: 0,
		maxWidth: 100,
		// backgroundColor: '#1677ff',
	};

	const footerStyle: React.CSSProperties = {
		textAlign: "center",
		color: "#fff",
		// backgroundColor: '#4096ff',
	};

	const layoutStyle = {
		// borderRadius: 8,
		overflow: "hidden",
		// width: 'calc(50% - 8px)',
		// maxWidth: 'calc(50% - 8px)',
	};

	const contentStyle: React.CSSProperties = {
		textAlign: "center",
		height: 640,
		maxWidth: 640,
		minWidth: 640,
		color: "#fff",
		// backgroundColor: '#0958d9',
	};

	return (
		// <div style={{height: 640}}>
		<Layout style={layoutStyle}>
			<Layout>
				<Content style={contentStyle}>
					<AzureMapsProvider>
						<NotificationMap
							notification={notification}
							shipTraces={shipTracesExt}
							setFooterData={setFooterData}
							ecaVisible={ecaVisible}
							ecaBorders={ecaBorders}
						/>
					</AzureMapsProvider>
				</Content>
				<Sider width="15%" style={siderStyle}>
					<NotificationRightBar
						ecaVisible={ecaVisible}
						setEcaVisible={setEcaVisible}
					/>
				</Sider>
			</Layout>
			<Footer style={footerStyle}>
				<NotificationFooter shipTrace={footerData} />
			</Footer>
		</Layout>
	);
};

export default NotificationMapWrapper;
