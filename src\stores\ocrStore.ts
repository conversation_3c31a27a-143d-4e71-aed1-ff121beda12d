import { action, observable } from "mobx";
import type { PagedFilterAndSortedRequestOCR } from "../services/dto/pagedFilterAndSortedRequestOCR";
import type { OcrDto } from "../services/ocr/dTo/ocrDto";
import ocrService from "../services/ocr/ocrService";

class OcrStore {
	public key: any;
	public value: any;
	@observable ocr!: any;

	constructor() {
		this.key = "";
		this.value = 0;
	}
	actualValues: { [key: string]: string } = {};

	@action
	updateActualValue(key: string, value: string) {
		this.actualValues[key] = value;
	}

	@action
	async analyzeFile(ocrInput: OcrDto) {
		await ocrService.analyzeFile(ocrInput);
	}
	@action
	async verifyFile(ocrInput: OcrDto[]) {
		await ocrService.verifyFile(ocrInput);
	}

	@action
	async denyFile(ocrInput: OcrDto) {
		await ocrService.denyFile(ocrInput);
	}

	@action
	async moveFile(ocrInput: OcrDto) {
		await ocrService.moveFile(ocrInput);
	}

	@action
	async changeStatus(ocrInput: OcrDto) {
		await ocrService.changeStatus(ocrInput);
	}

	@action
	async getAllByStatus(
		pagedFilterAndSortedRequestOCR: PagedFilterAndSortedRequestOCR,
	) {
		const result = await ocrService.getAllByStatus(
			pagedFilterAndSortedRequestOCR,
		);
		this.ocr = result;
	}

	@action
	async createMany(ocrInput: Pick<OcrDto, "fileName" | "fileUrl">[]) {
		await ocrService.createMany(ocrInput);
	}
}

export const defaultOcr: OcrDto = {
	fileName: "",
	fileUrl: "",
	status: 0,
	jsonContent: "",
	errorMessage: [],
	error: false,
	lastModificationTime: "",
	lastModificationTimeId: 0,
	creationTime: "",
	creatorUserId: 0,
	id: 0,
};

export default OcrStore;
