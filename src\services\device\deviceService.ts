import { EntityDto } from "../dto/entityDto";
import { PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoints";
import http from "../httpService";
import { PagedDeviceResultRequestDto } from "./dto/PagedDeviceResultRequestDto";
import { CreateOrUpdateDeviceInput } from "./dto/createOrUpdateDeviceInput";
import { GetAllDeviceOutput } from "./dto/getAllDeviceOutput";
import { RegisterDeviceInput } from "./dto/registerDeviceInput";

class DeviceService {
	public async create(createDeviceInput: CreateOrUpdateDeviceInput) {
		const result = await http.post(Endpoint.Device.Create, createDeviceInput);
		return result.data.result;
	}

	public async registerDevice(registerDeviceInput: RegisterDeviceInput) {
		const result = await http.post(
			Endpoint.Device.RegisterDevice,
			registerDeviceInput,
		);
		return result.data.result;
	}

	public async update(updateDeviceInput: CreateOrUpdateDeviceInput) {
		const result = await http.put(Endpoint.Device.Update, updateDeviceInput);
		return result.data.result;
	}

	public async delete(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.Device.Delete, {
			params: entityDto,
		});
		return result.data;
	}

	public async get(entityDto: EntityDto): Promise<CreateOrUpdateDeviceInput> {
		const result = await http.get(Endpoint.Device.Get, { params: entityDto });
		return result.data.result;
	}

	public async getAll(
		pagedFilterAndSortedRequest: PagedDeviceResultRequestDto,
	): Promise<PagedResultDto<GetAllDeviceOutput>> {
		const result = await http.get(Endpoint.Device.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}

	public async getDeviceStatuses() {
		const result = await http.get(Endpoint.Device.GetDeviceStatuses);
		return result.data.result;
	}
}

export default new DeviceService();
