import { ValidationRule } from "@ant-design/compatible/lib/form";
import { L } from "../../lib/abpUtility";

export interface IPasswordsRules {
	password: ValidationRule[];
	confirmPassword: (
		compareToFirstPassword: (rule: any, value: any, callback: any) => void,
	) => ValidationRule[];
}

const passwordsRules: IPasswordsRules = {
	password: [{ required: true, message: L("ThisFieldIsRequired") }],
	confirmPassword: (compareToFirstPassword) => [
		{ required: true, message: L("ThisFieldIsRequired") },
		{ validator: compareToFirstPassword },
	],
};

export default passwordsRules;
