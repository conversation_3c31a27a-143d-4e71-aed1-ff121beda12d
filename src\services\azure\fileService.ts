import { BlobServiceClient } from "@azure/storage-blob";
import Endpoint from "../endpoints";
import http from "../httpService";

export const getSasTokenForMeter = async () => {
	const response = await http.get(Endpoint.Storage.GetSassTokenForMeter);
	return response.data.result;
};

const getSasTokenForOCR = async () => {
	const response = await http.get(Endpoint.Storage.GetSasTokenForOCR);
	return response.data.result;
};

const uploadFileToStorage = async (file: File, data: any) => {
	const blobServiceClient = new BlobServiceClient(
		`${data.blobUri}/?${data.sasKey}`,
	);

	const containerClient = blobServiceClient.getContainerClient(data.container);
	const blockblobClient = containerClient.getBlockBlobClient(data.blobName);

	await blockblobClient.uploadBrowserData(file, {
		metadata: { DisplayName: file.name },
	});
};

class FileService {
	public async uploadFile(file: File) {
		const data = await getSasTokenForMeter();
		await uploadFileToStorage(file, data);

		return data.blobName;
	}

	public async uploadFileMeterConfiguration(file: File) {
		const data = await getSasTokenForMeter();
		await uploadFileToStorage(file, data);

		return `${data.blobUri}/${data.container}/${data.blobName}`;
	}

	public async uploadFileOCR(file: File) {
		const data = await getSasTokenForOCR();

		await uploadFileToStorage(file, data);

		return `${data.blobUri}/${data.container}/${data.blobName}`;
	}
}

export default new FileService();
