import { GsStatus } from "../../draft/dto/draftDto";
import { CreationAuditedEntityDto } from "../../dto/creationAuditedEntityDto";

export interface ConsumptionResultDto extends CreationAuditedEntityDto {
	formulaeName: string;
	meterType: string;
	consumptionConsumerUuid: string;
	consumptionConsumerInformation: string;
	firstReadingDate: Date;
	lastReadingDate: Date;
	firstReadingDateUtc: Date;
	lastReadingDateUtc: Date;
	timezone: string;
	period: string;
	fuelType: string;
	consumptionTotal: string;
	consumptionPerHour: string;
	consumption24h: string;
	uuid: string;
	density: string;
	idCorrect: boolean;
	shipId: number;
	eventId: number;
	isMadeManually: boolean;
	consumer: string;
	gsStatus: GsStatus;
	isCalculatedCentrally: boolean;
	reason: string;
	duration: string;
	ship: {
		imoNumber: number;
		shipName: string;
	};
	consumptionConsumer: {
		name: string;
	};
}
