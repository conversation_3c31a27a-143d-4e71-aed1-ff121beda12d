import * as React from "react";

import { DeleteOutlined } from "@ant-design/icons";

import { Form } from "@ant-design/compatible";
import "@ant-design/compatible/assets/index.css";

import { <PERSON><PERSON>, Modal, Table } from "antd";

import { FormComponentProps } from "@ant-design/compatible/lib/form";
import { observer } from "mobx-react";
import moment from "moment";
import { L } from "../../../lib/abpUtility";
import { GetLicenseOutput } from "../../../services/license/dto/getLicenseOutput";
import { ColumnProps } from "antd/lib/table";

export interface ILicensesProps extends FormComponentProps {
	visible: boolean;
	onOk: () => void;
	licenses: GetLicenseOutput[];
	onCancel: () => void;
	isAddLicenseAllowed: boolean;
	onDelete: (licenceId: number) => void;
}

export interface ILicensesState {
	keyValue: string;
}

@observer
class Licenses extends React.Component<ILicensesProps, ILicensesState> {
	state = {
		keyValue: "",
	};

	render() {
		const { visible, onOk, onCancel } = this.props;
		const columns : Array<ColumnProps<GetLicenseOutput>> = [
			{
				title: L("Expiration date"),
				dataIndex: "expirationDate",
				key: "expirationDate",
				render: (text: string) => (
					<a>{moment(text).format("YYYY-MM-DD hh:mm:ss")}</a>
				),
			},
			{
				title: L("LicenceDescription"),
				dataIndex: "description",
				key: "description",
			},
			{
				title: L("LicenceKey"),
				dataIndex: "key",
				key: "key",
			},
			{
				title: L('Actions'),
				render: (text: string, item) => (
					<Button
						danger
						icon={<DeleteOutlined />}
						onClick={() => {
							this.props.onDelete(item.id);
						}}
					/>
				),
			},
		];

		return (
			<Modal
				bodyStyle={{ padding: 0 }}
				cancelButtonProps={
					!this.props.isAddLicenseAllowed ? { hidden: true } : {}
				}
				okText={this.props.isAddLicenseAllowed ? "Add" : "Ok"}
				visible={visible}
				onOk={this.props.isAddLicenseAllowed ? onOk : onCancel}
				onCancel={onCancel}
				title={L("Tenants")}
				width={550}
			>
				<Table
					columns={columns}
					dataSource={this.props.licenses}
					pagination={false}
					rowKey={(license) => {
						return license.key;
					}}
					scroll={{ y: 300 }}
				/>
			</Modal>
		);
	}
}

export default Form.create<ILicensesProps>()(Licenses);
