import * as React from "react";

import { Form } from "@ant-design/compatible";
import "@ant-design/compatible/assets/index.css";

import { Input, Modal, Tabs } from "antd";

import { FormComponentProps } from "@ant-design/compatible/lib/form";
import CheckboxGroup from "antd/lib/checkbox/Group";
import FormItem from "antd/lib/form/FormItem";
import ComponentLayout from "../../../components/Layout/ComponentLayout";
import { isGranted, L } from "../../../lib/abpUtility";
import { GetAllPermissionsOutput } from "../../../services/role/dto/getAllPermissionsOutput";
import RoleStore from "../../../stores/roleStore";
import { FormWidths } from "../../ViewSettingsConsts";
import rules from "./createOrUpdateRole.validation";

const TabPane = Tabs.TabPane;

export interface ICreateOrUpdateRoleProps extends FormComponentProps {
	roleStore: RoleStore;
	visible: boolean;
	onCancel: () => void;
	modalType: string;
	onOk: () => void;
	loading: boolean;
	permissions: GetAllPermissionsOutput[];
}

class CreateOrUpdateRole extends React.Component<ICreateOrUpdateRoleProps> {
	state = {
		confirmDirty: false,
	};

	render() {
		const { permissions } = this.props;

		const options = permissions.map((item) => {
			const label = item.displayName.replace(/\[|\]/g, "").trim();

			return { label, value: item.name, disabled: true };
		});

		const { getFieldDecorator } = this.props.form;

		const groupByPermission = (
			list: { label: string; value: string; disabled: boolean }[],
		) =>
			list.reduce(
				(hash: Record<string, typeof list>, obj) => ({
					// biome-ignore lint/performance/noAccumulatingSpread: <explanation>
					...hash,
					[extractPermissionName(obj.value)]: (
						hash[extractPermissionName(obj.value)] || []
					).concat({
						label: obj.label.split("-")[1] || extractPermissionName(obj.value),
						value: obj.value,
						disabled: !isGranted(obj.value),
					}),
				}),
				{},
			);

		const extractPermissionName = (label: string) =>
			label.split(".")[1].split("-")[0];

		const formatTitle = (title: string) =>
			title
				.replace(/([a-z])([A-Z])/g, "$1 $2")
				.replace(/([A-Z])([A-Z][a-z])/g, "$1 $2")
				.trim();

		return (
			<Modal
				visible={this.props.visible}
				cancelText={L("Cancel")}
				okText={L("OK")}
				onCancel={this.props.onCancel}
				title={L("Role")}
				onOk={this.props.onOk}
				okButtonProps={{ loading: this.props.loading }}
			>
				<Form layout="vertical">
					<Tabs defaultActiveKey={"role"} size={"small"} tabBarGutter={64}>
						<TabPane tab={L("RoleDetails")} key={"role"}>
							<FormItem
								label={L("RoleName")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("name", { rules: rules.name })(
									<Input style={{ width: FormWidths.wide }} />,
								)}
							</FormItem>
							<FormItem
								label={L("DisplayName")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("displayName", { rules: rules.displayName })(
									<Input style={{ width: FormWidths.wide }} />,
								)}
							</FormItem>
							<FormItem
								label={L("Description")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("description")(
									<Input style={{ width: FormWidths.wide }} />,
								)}
							</FormItem>
						</TabPane>
						<TabPane tab={L("RolePermission")} key={"permission"}>
							<FormItem {...ComponentLayout.roleFormItemLayout}>
								{Object.entries(groupByPermission(options)).map(
									([key, values]) => (
										<div key={key}>
											<h2 style={{ marginBottom: 0, marginTop: "1rem" }}>
												{formatTitle(key)}
											</h2>
											{getFieldDecorator(`grantedPermissions.${key}`, {
												valuePropName: "value",
											})(<CheckboxGroup options={values} />)}
										</div>
									),
								)}
							</FormItem>
						</TabPane>
					</Tabs>
				</Form>
			</Modal>
		);
	}
}

export default Form.create<ICreateOrUpdateRoleProps>()(CreateOrUpdateRole);
