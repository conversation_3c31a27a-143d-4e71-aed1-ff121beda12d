import { Moment } from "moment";
import Endpoint from "../endpoints";
import http from "../httpService";

export type ParamRange<T> = {
	from?: T;
	to?: T;
};
class MeterReadingSetService {
	public async getEmissionReport(
		shipId: number,
		startDate: string,
		endDate: string,
	) {
		const result = await http.get(Endpoint.MeterReadingSet.GetEmissionReport, {
			params: { shipId, endDate, startDate },
		});
		return result.data.result;
	}

	public async getPerformanceReport(
		shipId: number,
		stwRange: ParamRange<number>,
		avgStwRange: ParamRange<number>,
		sogRange: ParamRange<number>,
		avgSogRange: ParamRange<number>,
		wavesHeightRange: ParamRange<number>,
		windSpeedRange: ParamRange<number>,
		draftRange: ParamRange<number>,
		durationRange: ParamRange<number>,
		dateRange: ParamRange<Moment>,
	) {
		const result = await http.get(
			Endpoint.MeterReadingSet.GetPerformanceReport,
			{
				params: {
					shipId,
					...this.convertNumberRangeToQuery(stwRange, "Stw"),
					...this.convertNumberRangeToQuery(avgStwRange, "AvgStw"),
					...this.convertNumberRangeToQuery(sogRange, "Sog"),
					...this.convertNumberRangeToQuery(avgSogRange, "AvgSog"),
					...this.convertNumberRangeToQuery(wavesHeightRange, "WaveHeight"),
					...this.convertNumberRangeToQuery(windSpeedRange, "WindSpeed"),
					...this.convertNumberRangeToQuery(draftRange, "Draft"),
					...this.convertNumberRangeToQuery(durationRange, "Duration"),
					...this.convertDateRangeToQuery(dateRange, "Date"),
				},
			},
		);
		return result.data.result;
	}

	private convertNumberRangeToQuery(range: ParamRange<number>, name: string) {
		return {
			[`min${name}`]: range.from,
			[`max${name}`]: range.to,
		};
	}

	private convertDateRangeToQuery(range: ParamRange<Moment>, name: string) {
		return {
			[`start${name}`]: range.from?.startOf("day").toISOString(),
			[`end${name}`]: range.to?.endOf("day").toISOString(),
		};
	}

	public async exportEmissionReport(
		shipId: number,
		voyageType: string,
		id: number[],
	) {
		const result = await http.get(
			Endpoint.MeterReadingSet.ExportEmissionReport,
			{
				params: {
					shipId,
					voyageType: encodeURIComponent(voyageType),
					sets: id,
				},
			},
		);
		return result.data.result;
	}

	public async exportPerformanceReport(shipId: number, id: number[]) {
		const result = await http.get(
			Endpoint.MeterReadingSet.ExportPerformanceReport,
			{ params: { shipId, sets: id } },
		);
		return result.data.result;
	}
}

export default new MeterReadingSetService();
