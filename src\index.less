body {
  margin: 0;
  padding: 0;
  font-family: sans-serif;
}
.ant-form-item-label > label.ant-form-item-required::before{
  content: none;
}
.ant-form-item-label > label.ant-form-item-required::after{
  display: inline-block !important;
  margin-left: 4px;
  color: #f5222d;
  font-size: 14px;
  line-height: 1;
  content: '*';
}


.ant-table-thead > tr > th {
  background-color: #6794E5 !important;
}


.ant-table-cell.ant-table-column-has-sorters, 
.ant-table-cell.ant-table-cell-fix-right.ant-table-cell-fix-right-first{
  padding-block: 0 !important;
}

.ant-table-cell:has(div.ant-table-filter-column){
  padding: 0 !important;
}

.ant-table-cell:has(div.ant-table-filter-column)
> .ant-table-filter-column
> .ant-table-column-title{
  padding: 20px 15px !important;
}

// .ant-table-cell.ant-table-column-has-sorters 
// > .ant-table-filter-column
// > .ant-table-column-title
// > .ant-table-column-sorters{
//   padding: 0px !important;
// }


.ant-table-filter-trigger{
  margin: 0 !important;
}

.ant-form-item{
  margin-bottom: 24px;
}

.ant-table-column-sorter-up.active,
.ant-table-column-sorter-down.active{

  color: #747474 !important;
}

.ant-table-thead > tr > th span,
.ant-table-thead > tr > th
 {
  color: #fff !important;
}



.ant-table-thead > tr > th.ant-table-column-has-actions.ant-table-column-has-sorters:hover .anticon-filter {
  background: #638EDC !important;
}

.ant-table-thead > tr > th.ant-table-column-has-actions.ant-table-column-has-sorters:hover .ant-table-filter-icon {
  background: #638EDC !important;
}

.ant-table-thead > tr > th.ant-table-column-has-actions.ant-table-column-has-filters:hover .ant-table-filter-icon:hover {
  background: #638EDC !important;
}

.ant-table-thead > tr > th.ant-table-column-has-actions.ant-table-column-has-filters .ant-table-filter-icon.ant-table-filter-open {
  background: #638EDC !important;
}

.ant-table-thead > tr > th.ant-table-column-has-actions.ant-table-column-has-filters .anticon-filter.ant-table-filter-open, .ant-table-thead > tr > th.ant-table-column-has-actions.ant-table-column-has-filters .ant-table-filter-icon.ant-table-filter-open {
  background: #638EDC !important;
}

.ant-table-thead > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td, .ant-table-tbody > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td, .ant-table-thead > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td, .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #A3CFFF4D !important;
}

.ant-table-bordered .ant-table-thead > tr > th, .ant-table-bordered .ant-table-tbody > tr > td {
  background: #fff;
}

.ant-table-tbody > tr > td.ant-table-column-sort {
  background: #fff !important;
}

.ant-card, .ant-card-bordered {
  border: none !important;
}

/* Tags in list */

.ant-tag {
  font-weight: bold;
}

.ant-tag-red {
  border-radius: 6px !important;
  background: #FEA34F !important;
  color: #fff !important;
}

/* Buttons */

.ant-btn-primary-disabled, .ant-btn-primary.disabled, .ant-btn-primary[disabled], .ant-btn-primary-disabled:hover, .ant-btn-primary.disabled:hover, .ant-btn-primary[disabled]:hover, .ant-btn-primary-disabled:focus, .ant-btn-primary.disabled:focus, .ant-btn-primary[disabled]:focus, .ant-btn-primary-disabled:active, .ant-btn-primary.disabled:active, .ant-btn-primary[disabled]:active, .ant-btn-primary-disabled.active, .ant-btn-primary.disabled.active, .ant-btn-primary[disabled].active {
  background-color: #CDD1D9 !important;
  color: #fff !important;
}

.ant-btn-primary {
  background-color: #233762 !important;
  border: none !important;
}

.ant-btn-primary:hover, .ant-btn-primary:focus {
  background-color: #3a4d77 !important;
}

.ant-btn-danger-disabled, .ant-btn-danger.disabled, .ant-btn-danger[disabled], .ant-btn-danger-disabled:hover, .ant-btn-danger.disabled:hover, .ant-btn-danger[disabled]:hover, .ant-btn-danger-disabled:focus, .ant-btn-danger.disabled:focus, .ant-btn-danger[disabled]:focus, .ant-btn-danger-disabled:active, .ant-btn-danger.disabled:active, .ant-btn-danger[disabled]:active, .ant-btn-danger-disabled.active, .ant-btn-danger.disabled.active, .ant-btn-danger[disabled].active {
  background-color: #FFB2B2 !important;
  color: #fff !important;
}

/* Pagination */

.ant-table-pagination.ant-pagination {
  width: 100%;
  text-align: center;
  
  .ant-pagination-item{
    border: none;
    background-color: transparent;
    border-radius: 7.5px;

    a {
      color: #000000;
    }
  }

  .ant-pagination-item-active {
    background-color: #6694E5;

    a {
      color: #FFFFFF;
    }
  }

  .ant-pagination-disabled {
    display: none;
  }

  .ant-pagination-item-link {
    border: none;
    background-color: #6694E5;
    border-radius: 7.5px;
    color: #FFFFFF;
  }

  .ant-pagination-item-link {
    border: none;
    background-color: #6694E5;
    border-radius: 7.5px;
    color: #FFFFFF;
  }

  .ant-pagination-prev:hover .ant-pagination-item-link, .ant-pagination-prev:focus .ant-pagination-item-link, .ant-pagination-next:hover .ant-pagination-item-link, .ant-pagination-next:focus .ant-pagination-item-link {
    border: none;
    background-color: #6694E5;
    border-radius: 7.5px;
    color: #FFFFFF;
  }
}