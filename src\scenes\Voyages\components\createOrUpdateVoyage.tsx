import * as React from "react";

import { Form } from "@ant-design/compatible";
import "@ant-design/compatible/assets/index.css";

import {
	Card,
	Checkbox,
	Col,
	DatePicker,
	Input,
	Modal,
	Row,
	Select,
	Tabs,
} from "antd";
import Meta from "antd/lib/card/Meta";
import FormItem from "antd/lib/form/FormItem";
import moment from "moment";
import AppComponentBase from "../../../components/AppComponentBase";
import ComponentLayout from "../../../components/Layout/ComponentLayout";
import { L } from "../../../lib/abpUtility";
import { VoyageInitializationSource } from "../../../services/voyage/dto/voyageInitializationSource";
import { VoyageStatus } from "../../../services/voyage/dto/voyageStatus";
import ShipStore from "../../../stores/shipStore";
import VoyageStore from "../../../stores/voyageStore";
import { ModalType } from "../../ModalConsts";
import { DateTimeFormat, FormWidths } from "../../ViewSettingsConsts";
import { ModalFormComponentProps } from "../../modalFormComponentProps";
import rules from "./createOrUpdateVoyage.validation";

const { Option } = Select;

export interface ICreateOrUpdateVoyageProps extends ModalFormComponentProps {
	voyageStore: VoyageStore;
}

export interface ICreateOrUpdateVoyageState {
	confirmDirty: boolean;
	isStartRequired: boolean;
}

class CreateOrUpdateVoyage extends AppComponentBase<
	ICreateOrUpdateVoyageProps,
	ICreateOrUpdateVoyageState
> {
	shipStore: ShipStore = new ShipStore();

	state = {
		confirmDirty: false,
		isStartRequired: false,
	};

	async componentDidMount() {
		await this.shipStore.getAllWithIdAndName();
	}

	onChangeStatus = (status: number) => {
		this.setRequiredStart(status);
	};

	componentDidUpdate() {
		const { getFieldValue } = this.props.form;
		const status = getFieldValue("status");
		if (status != null) {
			this.setRequiredStart(status);
		}
	}

	setRequiredStart = (status: number) => {
		if (status === 2 && !this.state.isStartRequired) {
			this.setState({ ...this.state, isStartRequired: true });
		} else if (status !== 2 && this.state.isStartRequired) {
			this.setState({ ...this.state, isStartRequired: false });
		}
	};

	getStartIsRequired = (): boolean => {
		return this.state.isStartRequired;
	};

	render() {
		const { getFieldDecorator } = this.props.form;
		const { visible, onCancel, onCreate, okButtonDisabled } = this.props;
		const { voyageStatuses } = this.props.voyageStore;
		const { allShips } = this.shipStore;

		const voyageStatusesSelectList = voyageStatuses
			? Object.values(voyageStatuses)
			: [];
		const ships = allShips ? allShips : [];

		const qrCodeLink = this.props.voyageStore.qrCodeLink
			? this.props.voyageStore.qrCodeLink
			: "";

		const voyageIniatlizationSource: VoyageInitializationSource =
			this.props.voyageStore.editVoyage?.initializationSource;
		const voyageIniatlizationSourceText =
			voyageIniatlizationSource !== undefined
				? VoyageInitializationSource[voyageIniatlizationSource]
				: "";

		return (
			<Modal
				visible={visible}
				cancelText={L("Cancel")}
				okText={L("OK")}
				onCancel={onCancel}
				onOk={onCreate}
				title={L("Voyage")}
				width={700}
				okButtonProps={{ disabled: okButtonDisabled }}
			>
				{getFieldDecorator("ruleNames")(<Input hidden />)}
				{getFieldDecorator("initializationSource")(<Input hidden />)}
				<Form layout="vertical">
					<Tabs defaultActiveKey={"general"} size={"small"} tabBarGutter={64}>
						<Tabs.TabPane tab={L("General")} key={"general"}>
							<FormItem label={L("Ship")} {...ComponentLayout.formItemLayout}>
								{getFieldDecorator("shipId", { rules: rules.shipId })(
									<Select
										{...ComponentLayout.formItemLayout}
										showSearch
										style={{ width: FormWidths.wide }}
										placeholder={L("SelectShip")}
										optionFilterProp="childen"
										filterOption={(input, option) =>
											option?.value
												? option.value
														.toString()
														.toLowerCase()
														.indexOf(input.toLowerCase()) >= 0
												: false}
										disabled={
											this.props.modalType  === ModalType.edit
										}
									>
										{ships.map((item) => (
											<Option key={item.id} value={item.id}>
												{item.shipName}
											</Option>
										))}
									</Select>,
								)}
							</FormItem>
							<FormItem
								label={L("VoyageNumberInfo")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("voyageNumber", {
									rules: rules.voyageNumber,
								})(<Input style={{ width: FormWidths.wide }} />)}
							</FormItem>
							{this.props.modalType === ModalType.edit && (
								<FormItem
									label={L("VoyageIdInfo")}
									{...ComponentLayout.formItemLayout}
								>
									{getFieldDecorator("id")(
										<Input
											disabled={true}
											style={{ width: FormWidths.wide }}
										/>,
									)}
								</FormItem>
							)}
							<FormItem label={L("Status")} {...ComponentLayout.formItemLayout}>
								{getFieldDecorator("status", {
									initialValue: this.props.form.getFieldValue("status") ?? 0,
								})(
									<Select
										{...ComponentLayout.formItemLayout}
										showSearch
										style={{ width: FormWidths.wide }}
										placeholder={L("SelectStatus")}
										optionFilterProp="childen"
										filterOption={(input, option) =>
											option?.value
												? option.value
														.toString()
														.toLowerCase()
														.indexOf(input.toLowerCase()) >= 0
												: false
										}
										onChange={this.onChangeStatus}
									>
										{voyageStatusesSelectList.map((item, i) => (
											<Option key={i} value={i}>
												{item}
											</Option>
										))}
									</Select>,
								)}
							</FormItem>
							{this.props.modalType === ModalType.create && (
								<FormItem
									label={L("SecretPin")}
									{...ComponentLayout.formItemLayout}
								>
									{getFieldDecorator("secretPin", { rules: rules.secretPin })(
										<Input style={{ width: FormWidths.wide }} />,
									)}
								</FormItem>
							)}
							{this.props.modalType === ModalType.edit && (
								<>
									<FormItem
										label={L("Initializataion Device Id")}
										{...ComponentLayout.formItemLayout}
									>
										{getFieldDecorator("deviceId")(
											<Input
												className={"disabled-input"}
												style={{ width: FormWidths.wide }}
												disabled
											/>,
										)}
									</FormItem>
									<FormItem
										label={L("VoyageIniatliazationSource")}
										{...ComponentLayout.formItemLayout}
									>
										<Input
											value={voyageIniatlizationSourceText}
											className={"disabled-input"}
											style={{ width: FormWidths.wide }}
											disabled
										/>
									</FormItem>
								</>
							)}
							<FormItem
								label={L("DestinationFrom")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("destinationFrom", {
									rules: rules.destinationFrom,
								})(<Input style={{ width: FormWidths.wide }} />)}
							</FormItem>

							<FormItem
								{...ComponentLayout.formItemLayout}
								label={L("PlanedStart")}
							>
								{getFieldDecorator("planedStart", { rules: rules.planedStart })(
									<DatePicker
										showTime={{ format: DateTimeFormat.time }}
										format={DateTimeFormat.fullDate}
										style={{ width: FormWidths.wide }}
									/>,
								)}
							</FormItem>

							<FormItem {...ComponentLayout.formItemLayout} label={L("Start")}>
								{getFieldDecorator("start", {
									rules: rules.start(this.getStartIsRequired()),
								})(
									<DatePicker
										showTime={{ format: DateTimeFormat.time }}
										format={DateTimeFormat.fullDate}
										style={{ width: FormWidths.wide }}
										disabled={
											this.props.form.getFieldValue("status") !==
											VoyageStatus.Started
										}
									/>,
								)}
							</FormItem>

							<FormItem
								label={L("DestinationTo")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("destinationTo", {
									rules: rules.destinationTo,
								})(<Input style={{ width: FormWidths.wide }} />)}
							</FormItem>

							<FormItem
								{...ComponentLayout.formItemLayout}
								label={L("PlanedEnd")}
							>
								{getFieldDecorator("planedEnd", { rules: rules.planedEnd })(
									<DatePicker
										showTime={{ format: DateTimeFormat.time }}
										format={DateTimeFormat.fullDate}
										style={{ width: FormWidths.wide }}
									/>,
								)}
							</FormItem>

							<FormItem {...ComponentLayout.formItemLayout} label={L("End")}>
								{getFieldDecorator("end", {
									initialValue: moment(),
									rules: rules.end,
								})(
									<DatePicker
										showTime={{ format: DateTimeFormat.time }}
										format={DateTimeFormat.fullDate}
										style={{ width: FormWidths.wide }}
										disabled={
											!(
											this.props.form.getFieldValue("status") >=
											VoyageStatus.Finished)
										}
									/>,
								)}
							</FormItem>
						</Tabs.TabPane>
						<Tabs.TabPane tab={L("Settings")} key={"setting"}>
							<FormItem
								label={L("IsArchived")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("isArchived", { valuePropName: "checked" })(
									<Checkbox />,
								)}
							</FormItem>

							<FormItem
								label={L("IsActive")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("isActive", { valuePropName: "checked" })(
									<Checkbox />,
								)}
							</FormItem>
						</Tabs.TabPane>

						{this.props.modalType === ModalType.edit && (
							<Tabs.TabPane tab={L("QrCode")} key={"qrCode"}>
								<Row gutter={24} justify="center">
									<Col span={24}>
										<FormItem
											label={L("QrCode")}
											{...ComponentLayout.formItemLayout}
										>
											{getFieldDecorator("qrCode")(
												<Input className={"disabled-input"} disabled />,
											)}
										</FormItem>
									</Col>
								</Row>
								<Row gutter={24} justify="center">
									<Col span={12}>
										<Card hoverable cover={<img src={qrCodeLink} />}>
											<Meta title={L("QrCode")} />
										</Card>
									</Col>
								</Row>
							</Tabs.TabPane>
						)}
					</Tabs>
				</Form>
			</Modal>
		);
	}
}

export default Form.create<ICreateOrUpdateVoyageProps>()(CreateOrUpdateVoyage);
