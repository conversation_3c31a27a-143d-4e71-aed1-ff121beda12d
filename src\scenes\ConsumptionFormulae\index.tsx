import { WrappedFormUtils } from "@ant-design/compatible/lib/form/Form";
import { PlusOutlined, SettingOutlined } from "@ant-design/icons";
import { Badge, Button, Card, Col, Dropdown, Modal, Popover, Row } from "antd";
import Table, { ColumnProps } from "antd/lib/table";
import { SorterResult, TablePaginationConfig } from "antd/lib/table/interface";
import { inject, observer } from "mobx-react";
import Menu, { MenuItem } from "rc-menu";
import * as React from "react";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import { L, isGranted } from "../../lib/abpUtility";
import { ConsumptionConsumerDto } from "../../services/consumptionConsumer/dto/consumptionConsumerDto";
import { EntityDto } from "../../services/dto/entityDto";
import ConsumptionConsumerStore from "../../stores/consumptionConsumerStore";
import MeterConfigurationStore from "../../stores/meterConfigurationStore";
import ShipStore from "../../stores/shipStore";
import Stores from "../../stores/storeIdentifier";
import utils from "../../utils/utils";
import { ModalType } from "../ModalConsts";
import { getTablePaginationOptions, renderCheckboxValue } from "../renderUtils";
import CreateOrUpdateConsumptionConsumer from "./components/createOrUpdateConsumptionConsumer";

export interface IConsumptionFormulaeProps {
	consumptionConsumerStore: ConsumptionConsumerStore;
	shipStore: ShipStore;
	meterConfigurationStore: MeterConfigurationStore;
}

export interface IConsumptionFormulaeState {
	modalVisible: boolean;
	loading: boolean;
	maxResultCount: number;
	skipCount: number;
	sorting: string;
	searchTextTable: string;
	sorters: SorterResult<ConsumptionConsumerDto>[];
	id: number;
	searchedColumnTable: string;
	searchTextInsideTable: Array<{ index: string; searchText: string }>;
	okButtonDisabled: boolean;
}

const confirm = Modal.confirm;

type PreviousState = {
	sorters: IConsumptionFormulaeState["sorters"];
};

@inject(Stores.ConsumptionConsumerStore)
@inject(Stores.ShipStore)
@inject(Stores.MeterConfigurationStore)
@observer
class ConsumptionFormulae extends AppComponentBase<
	IConsumptionFormulaeProps,
	IConsumptionFormulaeState
> {
	formRef?: WrappedFormUtils;

	state: IConsumptionFormulaeState = {
		loading: false,
		modalVisible: false,
		maxResultCount: 100,
		skipCount: 0,
		sorting: "",
		sorters: [],
		searchTextTable: "",
		searchTextInsideTable: [{ index: "", searchText: "" }],
		searchedColumnTable: "",
		okButtonDisabled: false,
		id: 0,
	};

	async componentDidMount() {
		const prevState = this.extractPreviousState();

		this.setState({ ...prevState }, async () => {
			await Promise.all([
				this.getAll(),
				this.props.meterConfigurationStore.getMeterNames(),
			]);
		});
	}

	extractPreviousState(): PreviousState {
		let state: PreviousState = {
			sorters: [],
		};
		const prevState =
			utils.getSortAndFilterFromStorage<PreviousState>("formulae-filters");

		if (prevState) state = { ...prevState };

		utils.removeStateFromStorage("formulae-filters");

		return state;
	}

	componentWillUnmount(): void {
		const settings: PreviousState = {
			sorters: this.state.sorters,
		};

		utils.saveSortAndFilterToStorage("formulae-filters", settings);
	}

	async getAll() {
		const sortString = utils.getSorterString(this.state.sorters);

		this.setState({ loading: true });
		await this.props.consumptionConsumerStore.getAll({
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: this.state.sorting,
			sorting: sortString,
		});
		this.setState({ loading: false });
	}

	Modal = () => {
		this.setState({
			modalVisible: !this.state.modalVisible,
		});
	};
	async createOrUpdateModalOpen(entityDto: EntityDto) {
		if (entityDto.id === 0) {
			await this.props.consumptionConsumerStore.createConsumptionConsumer();
		} else {
			await this.props.consumptionConsumerStore.get(entityDto);
		}
		this.setState({ id: entityDto.id });
		this.Modal();

		if (!this.formRef) return;
		this.formRef.setFieldsValue({
			...this.props.consumptionConsumerStore.editConsumptionConsumer,
		});
	}

	setModalVisibleFalse = () => {
		this.setState({ modalVisible: false });
		this.setState({ okButtonDisabled: false });
	};

	delete(input: EntityDto) {
		const self = this;
		confirm({
			title: "Do you want to delete these items?",
			onOk() {
				self.props.consumptionConsumerStore.delete(input);
			},
			onCancel() {},
		});
	}

	handleCreate = () => {
		if (this.state.okButtonDisabled === false) {
			const form = this.formRef;
			if (!form) return;

			// biome-ignore lint/suspicious/noExplicitAny: Poor type implementation from library
			form.validateFields(async (err: any, values: ConsumptionConsumerDto) => {
				let errors: Record<string, { errors: unknown[]; value?: unknown }> =
					err;
				if (
					values.metersMinus?.length === 0 &&
					values.metersPlus?.length === 0
				) {
					errors = {
						...errors,
						metersMinus: {
							errors: [
								new Error("Must select at least one from either plus or minux"),
							],
						},
						metersPlus: {
							errors: [
								new Error("Must select at least one from either plus or minux"),
							],
						},
					};
				} else {
					errors = {
						...errors,
						metersMinus: {
							value: values.metersMinus,
							errors: [],
						},
						metersPlus: {
							value: values.metersPlus,
							errors: [],
						},
					};
				}

				form.setFields(errors);
				if (
					Object.entries(errors).some(
						([keys, value]) => value.errors.length !== 0,
					)
				) {
					return;
				}

				this.setState({ okButtonDisabled: true });
				try {
					if (this.state.id === 0) {
						await this.props.consumptionConsumerStore.create(values);
					} else {
						await this.props.consumptionConsumerStore.update({
							...values,
							id: this.state.id,
						});
					}
				} catch (ex) {}
				this.getAll();
				this.setModalVisibleFalse();
				form.resetFields();
			});
		}
	};
	handleTableChange = (
		pagination: TablePaginationConfig,
		filters: Partial<Record<keyof ConsumptionConsumerDto, string[]>>,
		sorter:
			| SorterResult<ConsumptionConsumerDto>
			| SorterResult<ConsumptionConsumerDto>[],
	) => {
		const sorters = utils.getSorters(sorter);

		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
				sorters: sorters,
			},
			async () => await this.getAll(),
		);
	};

	handleSearchTable = (
		selectedKeys: React.Key[],
		dataIndex: string,
		confirm?: () => void,
	) => {
		const allSearches = this.state.searchTextInsideTable;
		const filtred = allSearches.filter((x) => x.index !== dataIndex);

		filtred.push({ index: dataIndex, searchText: selectedKeys[0].toString() });
		if (confirm) confirm();
		this.setState({
			searchTextTable: selectedKeys[0].toString(),
			searchedColumnTable: dataIndex,
			searchTextInsideTable: filtred,
		});
	};

	onFormCancel = () => {
		const form = this.formRef;
		this.setState({
			modalVisible: false,
		});
		this.getAll();
		if (!form) return;
		form.resetFields();
	};

	// biome-ignore lint/suspicious/noExplicitAny: Poor type implementation from library
	saveFormRef = (formRef: any) => {
		if (!formRef) return;
		this.formRef = formRef.props.form;
	};

	handleReset = (dataIndex: string, clearFilters?: () => void) => {
		const allSearches = this.state.searchTextInsideTable;
		const filtred = allSearches.filter((x) => x.index !== dataIndex);
		if (clearFilters) clearFilters();
		this.setState({
			searchTextTable: "",
			searchTextInsideTable: filtred,
		});
	};

	public render() {
		const { consumptionConsumers } = this.props.consumptionConsumerStore;
		const { meterNames } = this.props.meterConfigurationStore;
		const paginationOptions = getTablePaginationOptions(
			consumptionConsumers?.totalCount,
		);
		const columns: ColumnProps<ConsumptionConsumerDto>[] = [
			{
				title: L("Ship Name"),
				dataIndex: ["ship", "shipName"],
				key: "ship.shipName",
				width: 150,
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "ship.shipName",
				)?.order,
				sorter: { multiple: 4 },
			},
			{
				title: L("Name of formula"),
				dataIndex: "name",
				key: "name",
				width: 200,
				sortOrder: this.state.sorters.find((x) => x.columnKey === "name")
					?.order,
				sorter: { multiple: 3 },
			},
			{
				title: L("Meters Plus"),
				dataIndex: "metersPlus",
				key: "metersPlus",
				width: 150,
				render: (metersPlus: string[]) => {
					if (!meterNames || meterNames.length === 0)
						return metersPlus.join(", ");

					const missing: string[] = [];

					const names = metersPlus.map((x) => {
						const meterName = meterNames.find((m) => m.guid === x);
						if (meterName) return meterName.name;
						missing.push(x);
						return "";
					});

					return (
						<div style={{ display: "flex", alignItems: "center", gap: "15px" }}>
							<span style={{ whiteSpace: "pre-wrap" }}>
								{names.filter((x) => x.length !== 0).join(", \n")}
							</span>
							<Popover
								content={
									<span style={{ whiteSpace: "pre-wrap" }}>
										{missing.join(", \n")}
									</span>
								}
								title="Unknown meter ids"
								trigger="hover"
								placement="right"
							>
								<Badge
									count={missing.length}
									style={{ backgroundColor: "#faad14" }}
								/>
							</Popover>
						</div>
					);
				},
			},
			{
				title: L("Meters Minus"),
				dataIndex: "metersMinus",
				key: "metersMinus",
				width: 150,
				render: (metersMinus: string[]) => {
					if (!meterNames || meterNames.length === 0)
						return metersMinus.join(", ");

					const missing: string[] = [];

					const names = metersMinus.map((x) => {
						const meterName = meterNames.find((m) => m.guid === x);
						if (meterName) return meterName.name;
						missing.push(x);
						return "";
					});

					return (
						<div style={{ display: "flex", alignItems: "center", gap: "15px" }}>
							<span style={{ whiteSpace: "pre-wrap" }}>
								{names.filter((x) => x.length !== 0).join(", \n")}
							</span>
							<Popover
								content={
									<span style={{ whiteSpace: "pre-wrap" }}>
										{missing.join(", \n")}
									</span>
								}
								title="Unknown meter ids"
								trigger="hover"
								placement="right"
							>
								<Badge
									count={missing.length}
									style={{ backgroundColor: "#faad14" }}
								/>
							</Popover>
						</div>
					);
				},
			},
			{
				title: L("Is Active"),
				dataIndex: "isActive",
				key: "isActive",
				width: 150,
				sortOrder: this.state.sorters.find((x) => x.columnKey === "isActive")
					?.order,
				sorter: { multiple: 2 },
				render: renderCheckboxValue,
			},
			{
				title: L("Is Auto Calculation"),
				dataIndex: "isAutoCalculation",
				key: "isAutoCalculation",
				width: 200,
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "isAutoCalculation",
				)?.order,
				sorter: { multiple: 1 },
				render: renderCheckboxValue,
			},
			{
				title: L("Actions"),
				width: "10%",
				fixed: "right" as const,
				render: (text: string, item) => (
					<div>
						<Dropdown
							trigger={["click"]}
							overlay={
								<Menu>
									{isGranted("Pages.ConsumptionConsumers-Edit") && (
										<MenuItem
											onClick={() =>
												this.createOrUpdateModalOpen({ id: item.id })
											}
										>
											{L("Edit")}
										</MenuItem>
									)}
									{isGranted("Pages.ConsumptionConsumers-Delete") && (
										<MenuItem onClick={() => this.delete({ id: item.id })}>
											{L("Delete")}
										</MenuItem>
									)}
								</Menu>
							}
							placement="bottomLeft"
						>
							<Button type="primary" icon={<SettingOutlined />}>
								{L("Actions")}
							</Button>
						</Dropdown>
					</div>
				),
			},
		];

		const hasNoActions =
			!isGranted("Pages.ConsumptionConsumers-Edit") &&
			!isGranted("Pages.ConsumptionConsumers-Delete");

		if (hasNoActions) {
			columns.pop();
		}

		return (
			<Card>
				<Row>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
						style={{ display: "flex", justifyContent: "right" }}
					>
						{isGranted("Pages.ConsumptionConsumers-Add") && (
							<Button
								type="primary"
								shape="circle"
								icon={<PlusOutlined />}
								onClick={() => this.createOrUpdateModalOpen({ id: 0 })}
							/>
						)}
					</Col>
				</Row>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey={(record: ConsumptionConsumerDto) => record.id.toString()}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={consumptionConsumers === undefined || this.state.loading}
							dataSource={
								consumptionConsumers === undefined
									? []
									: consumptionConsumers.items
							}
							onChange={this.handleTableChange}
							scroll={{ y: 850 }}
						/>
					</Col>
				</Row>
				<CreateOrUpdateConsumptionConsumer
					shipStore={this.props.shipStore}
					meterConfigurationStore={this.props.meterConfigurationStore}
					consumptionConsumerStore={this.props.consumptionConsumerStore}
					wrappedComponentRef={this.saveFormRef}
					visible={this.state.modalVisible}
					onCancel={this.onFormCancel}
					modalType={this.state.id === 0 ? ModalType.create : ModalType.edit}
					onCreate={this.handleCreate}
					roles={this.props.consumptionConsumerStore.roles}
					okButtonDisabled={this.state.okButtonDisabled}
				/>
				<Chat />
			</Card>
		);
	}
}

export default ConsumptionFormulae;
