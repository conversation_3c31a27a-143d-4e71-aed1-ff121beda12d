import { Form } from "@ant-design/compatible";
import React from "react";
import "@ant-design/compatible/assets/index.css";
import { Button, Input, Modal, Table } from "antd";
import { inject, observer } from "mobx-react";
import moment from "moment";
import { Document, Page } from "react-pdf";
import { pdfjs } from "react-pdf";
import { L } from "../../../lib/abpUtility";
import { OcrDto } from "../../../services/ocr/dTo/ocrDto";
import OcrStore from "../../../stores/ocrStore";
import Stores from "../../../stores/storeIdentifier";
import { renderDate } from "../../renderUtils";
import { TableRowSelection } from "antd/lib/table/interface";
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.js`;

interface IAfterOcrTableProps {
	ocrStore: OcrStore;
	verifyFile: (ocrStore: OcrDto[]) => Promise<void>;
	selectedRow?: OcrDto | null;
}

@inject(Stores.OcrStore)
@observer
class AfterOcrTable extends React.Component<IAfterOcrTableProps> {
	ocrStore: OcrStore = this.props.ocrStore;
	data: Record<string, string>;
	jsonContent: Record<string, string>;
	predictColumn = React.createRef<HTMLDivElement>();
	actualColumn = React.createRef<HTMLDivElement>();

	state = {
		isModalVisible: false,
		selectedFile: null,
		selectedRow: [] as OcrDto[],
		maxResultCount: 10,
		skipCount: 0,
		ocrId: 0,
		filter: "",

		overrideFields: {} as { [key: string]: string },
	};
	constructor(props: IAfterOcrTableProps) {
		super(props);
		this.data = {};
		this.jsonContent = {};
		this.state = {
			isModalVisible: false,
			selectedFile: null,
			selectedRow: [],
			maxResultCount: 10,
			skipCount: 0,
			ocrId: 0,
			filter: "",
			overrideFields: {},
		};
	}

	componentDidUpdate(): void {
		if (!this.predictColumn.current || !this.actualColumn.current) return;

		this.actualColumn.current.addEventListener("scroll", (e) => {
			if (!this.predictColumn.current) return;
			this.predictColumn.current.scrollTop = (
				e.target as HTMLDivElement
			).scrollTop;
		});

		this.predictColumn.current.addEventListener("scroll", (e) => {
			if (!this.actualColumn.current) return;
			this.actualColumn.current.scrollTop = (
				e.target as HTMLDivElement
			).scrollTop;
		});
	}

	showModal = () => {
		if (
			this.state.selectedRow.length > 0 &&
			!this.state.selectedRow[0].jsonContent
		) {
			alert("Processing file");
			return;
		}
		this.setState({ isModalVisible: true });
	};

	handleOk = () => {
		this.setState({ isModalVisible: false, selectedRow: [] });
	};

	handleCancel = () => {
		this.setState({ isModalVisible: false });
	};

	async handleVerifyFileClick() {
		if (this.state.selectedRow.length <= 0) return;
		const jsonContent = this.state.selectedRow[0].jsonContent;
		const parsJson = JSON.parse(jsonContent || "{}");
		// biome-ignore lint/complexity/noForEach: <explanation>
		Object.entries(this.state.overrideFields).forEach(([key, value]) => {
			parsJson.Fields[key] = value;
		});
		this.props.verifyFile([
			{ ...this.state.selectedRow[0], jsonContent: JSON.stringify(parsJson) },
		]);
		this.setState({ overrideFields: {} });
	}

	public render() {
		const columns = [
			{
				title: L("CreationTime"),
				dataIndex: "creationTime",
				key: "creationTime",
				width: 150,
				sorter: (a: OcrDto, b: OcrDto) =>
					moment(a.creationTime).diff(moment(b.creationTime)),
				render: (text: string, rekord: OcrDto, index: number) =>
					renderDate(text),
			},
			{
				title: L("LastModificationTime"),
				dataIndex: "lastModificationTime",
				key: "lastModificationTime",
				width: 150,
				sorter: (a: OcrDto, b: OcrDto) =>
					moment(a.lastModificationTime).diff(moment(b.lastModificationTime)),
				render: (text: string, rekord: OcrDto, index: number) =>
					renderDate(text),
			},
			{
				title: L("FileName"),
				dataIndex: "fileName",
				key: "fileName",
				width: 150,
			},
			{
				title: L("Status"),
				dataIndex: "status",
				key: "status",
				width: 150,
			},
		];

		const { selectedRow } = this.state;

		const rowSelection: TableRowSelection<OcrDto> = {
			onChange: (selectedRowKeys, selectedRows: OcrDto[]) => {
				this.setState({
					selectedRow: selectedRows,
				});
			},
		};

		const jsonContent = selectedRow[0]?.jsonContent;
		const fileUrl = selectedRow[0]?.fileUrl;
		const parsJson = JSON.parse(jsonContent || "{}");
		const fields = parsJson.Fields || {};
		const predictFields = [...Object.entries(fields)];

		return (
			<>
				<div className="ocr-header__buttons">
					<Button
						type="primary"
						onClick={this.showModal}
						disabled={this.state.selectedRow.length === 0}
					>
						{L("Check Value")}
					</Button>
					<Button
						type="primary"
						onClick={() => {
							this.setState({ selectedRow: [] });
							this.props.verifyFile(selectedRow);
						}}
						disabled={this.state.selectedRow.length === 0}
					>
						{L("Verify File")}
					</Button>
				</div>
				<Table
					bordered={true}
					rowKey={(record: OcrDto) => record.id.toString()}
					columns={columns}
					dataSource={this.props.ocrStore.ocr ? this.props.ocrStore.ocr : []}
					rowSelection={rowSelection}
				/>
				<Modal
					title="Check Value"
					visible={this.state.isModalVisible}
					onOk={this.handleOk}
					onCancel={this.handleCancel}
					width="90%"
					style={{ top: "5vh" }}
					footer={
						<div style={{ display: "flex", justifyContent: "end" }}>
							<Button
								key="verifyFile"
								type="primary"
								style={{ marginRight: 8 }}
								onClick={() => this.handleVerifyFileClick()}
							>
								{L("VerifyFile")}
							</Button>
							,
							<div>
								<Button
									key="back"
									onClick={this.handleCancel}
									style={{ marginRight: 8 }}
								>
									{L("Cancel")}
								</Button>
								<Button key="submit" type="primary" onClick={this.handleOk}>
									{L("OK")}
								</Button>
							</div>
						</div>
					}
				>
					<div className="dataContainer">
						<div className="pdfContainer" id="pdfContainer">
							<Document file={fileUrl}>
								<Page
									pageNumber={1}
									height={
										document.getElementById("pdfContainer")?.clientHeight || 500
									}
									className={"canvasStyle"}
									renderInteractiveForms
									renderAnnotationLayer
								/>
							</Document>
						</div>
						<div className="columnContainer">
							<div className="columnContent" ref={this.predictColumn}>
								<h2>{L("PREDICT")}</h2>
								{predictFields.map(([key, value]) => (
									<Form.Item label={key} key={key}>
										<Input
											placeholder="Enter text here"
											value={value as string}
											readOnly
										/>
									</Form.Item>
								))}
							</div>
							<div className="columnContent" ref={this.actualColumn}>
								<h2>{L("ACTUAL")}</h2>
								{predictFields.map(([key, value]) => (
									<Form.Item label={key} key={key}>
										<Input
											placeholder={value as string}
											value={this.state.overrideFields[key]}
											onChange={(event) => {
												const localFields = { ...this.state.overrideFields };
												localFields[key] = event.target.value;
												this.setState({ overrideFields: localFields });
											}}
										/>
									</Form.Item>
								))}
							</div>
						</div>
					</div>
				</Modal>
			</>
		);
	}
}

export default AfterOcrTable;
