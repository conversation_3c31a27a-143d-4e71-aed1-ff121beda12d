import * as React from "react";

import { Form } from "@ant-design/compatible";
import "@ant-design/compatible/assets/index.css";

import { Checkbox, Input, Modal, Tabs } from "antd";

import { FormComponentProps } from "@ant-design/compatible/lib/form";
import CheckboxGroup from "antd/lib/checkbox/Group";
import ComponentLayout from "../../../components/Layout/ComponentLayout";
import { L } from "../../../lib/abpUtility";
import { GetRoles } from "../../../services/user/dto/getRolesOuput";
import { ModalType } from "../../ModalConsts";
import { FormWidths } from "../../ViewSettingsConsts";
import rules from "./createOrUpdateUser.validation";
import { ExclamationOutlined } from "@ant-design/icons";

const TabPane = Tabs.TabPane;

export interface ICreateOrUpdateUserProps extends FormComponentProps {
	loading: boolean;
	visible: boolean;
	onCancel: () => void;
	modalType: string;
	onCreate: () => void;
	roles: GetRoles[];
}

class CreateOrUpdateUser extends React.Component<ICreateOrUpdateUserProps> {
	state = {
		confirmDirty: false,
	};

	// biome-ignore lint/suspicious/noExplicitAny: Poor library implementation
	compareToFirstPassword = (rule: any, value: any, callback: any) => {
		const form = this.props.form;
		if (value && value !== form.getFieldValue("password")) {
			callback("Two passwords that you enter is inconsistent!");
		} else {
			callback();
		}
	};

	// biome-ignore lint/suspicious/noExplicitAny: Poor library implementation
	validateToNextPassword = (rule: any, value: any, callback: any) => {
		const form = this.props.form;
		if (value && this.state.confirmDirty) {
			form.validateFields(["confirm"], { force: true });
		}
		callback();
	};

	hasError = (...string: string[]) => {
		const error = this.props.form.getFieldsError(string);
		return Object.values(error).some((x) => x && x.length !== 0);
	};

	render() {
		const { roles } = this.props;

		const { getFieldDecorator } = this.props.form;
		const { visible, onCancel, onCreate } = this.props;

		const options = roles.map((x: GetRoles) => {
			const test = { label: x.displayName, value: x.normalizedName };
			return test;
		});

		return (
			<Modal
				visible={visible}
				cancelText={L("Cancel")}
				okText={L("OK")}
				onCancel={onCancel}
				onOk={onCreate}
				title={"User"}
				okButtonProps={{ loading: this.props.loading }}
			>
				<Form layout="vertical">
					<Tabs defaultActiveKey={"userInfo"} size={"small"} tabBarGutter={64}>
						<TabPane
							tab={
								<span>
									{L("User details")}
									{this.hasError(
										"name",
										"surname",
										"userName",
										"emailAddress",
										"password",
										"confirm",
									) && <ExclamationOutlined style={{ color: "red" }} />}
								</span>
							}
							key={"user"}
						>
							<Form.Item label={L("Name")} {...ComponentLayout.formItemLayout}>
								{getFieldDecorator("name", { rules: rules.name })(
									<Input style={{ width: FormWidths.wide }} />,
								)}
							</Form.Item>
							<Form.Item
								label={L("Surname")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("surname", { rules: rules.surname })(
									<Input style={{ width: FormWidths.wide }} />,
								)}
							</Form.Item>
							<Form.Item
								label={L("Company")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("userName", { rules: rules.userName })(
									<Input style={{ width: FormWidths.wide }} />,
								)}
							</Form.Item>
							<Form.Item label={L("Email")} {...ComponentLayout.formItemLayout}>
								{getFieldDecorator("emailAddress", {
									rules: rules.emailAddress,
								})(<Input style={{ width: FormWidths.wide }} />)}
							</Form.Item>
							{this.props.modalType === ModalType.edit ? (
								<Form.Item
									label={L("Password")}
									{...ComponentLayout.formItemLayout}
								>
									{getFieldDecorator("password", {
										rules: [
											{
												required: true,
												message: "Please input password!",
											},
											{
												validator: this.validateToNextPassword,
											},
										],
									})(
										<Input
											type="password"
											style={{ width: FormWidths.wide }}
										/>,
									)}
								</Form.Item>
							) : null}
							{this.props.modalType === ModalType.edit ? (
								<Form.Item
									label={L("ConfirmPassword")}
									{...ComponentLayout.formItemLayout}
								>
									{getFieldDecorator("confirm", {
										rules: [
											{
												required: true,
												message: L("ConfirmPassword"),
											},
											{
												validator: this.compareToFirstPassword,
											},
										],
									})(
										<Input
											type="password"
											style={{ width: FormWidths.wide }}
										/>,
									)}
								</Form.Item>
							) : null}
							<Form.Item
								label={L("IsActive")}
								{...ComponentLayout.tailFormItemLayout}
							>
								{getFieldDecorator("isActive", { valuePropName: "checked" })(
									<Checkbox>{L("Active")}</Checkbox>,
								)}
							</Form.Item>
						</TabPane>
						<TabPane
							tab={
								<span>
									{L("Roles")}{" "}
									{this.hasError("roleNames") && (
										<ExclamationOutlined style={{ color: "red" }} />
									)}
								</span>
							}
							key={"rol"}
						>
							<Form.Item {...ComponentLayout.tailFormItemLayout}>
								{getFieldDecorator("roleNames", {
									valuePropName: "value",
									rules: [
										{ required: true, message: "At least 1 role is required" },
									],
								})(<CheckboxGroup options={options} />)}
							</Form.Item>
						</TabPane>
					</Tabs>
				</Form>
			</Modal>
		);
	}
}

export default Form.create<ICreateOrUpdateUserProps>()(CreateOrUpdateUser);
