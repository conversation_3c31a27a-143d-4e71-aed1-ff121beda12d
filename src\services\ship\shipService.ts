import { EntityDto } from "../../services/dto/entityDto";
import {
	ListResultDto,
	PagedResultDto,
} from "../../services/dto/pagedResultDto";
import Endpoint from "../endpoints";
import http from "../httpService";
import { PagedShipResultRequestDto } from "./dto/PagedShipResultRequestDto";
import { CreateOrUpdateShipInput } from "./dto/createOrUpdateShipInput";
import { GetShipOutput } from "./dto/getShipOutput";
import { UpdateShipInput } from "./dto/updateShipInput";

class ShipService {
	public async create(createShipInput: CreateOrUpdateShipInput) {
		const result = await http.post(Endpoint.Ship.Create, createShipInput);
		return result.data.result;
	}

	public async update(updateShipInput: UpdateShipInput) {
		const result = await http.put(Endpoint.Ship.Update, updateShipInput);
		return result.data.result;
	}

	public async getFilters(
		pagedFilterAndSortedRequest: { keyword: string; searchColumn: string },
		property: string,
	) {
		const result = await http.get(Endpoint.Ship.Filters, {
			params: { ...pagedFilterAndSortedRequest, propertyName: property },
		});
		return result.data.result;
	}

	public async delete(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.Ship.Delete, {
			params: entityDto,
		});
		return result.data;
	}

	public async getRoles() {
		const result = await http.get("api/services/app/Ship/GetRoles");
		return result.data.result.items;
	}

	public async get(entityDto: EntityDto): Promise<CreateOrUpdateShipInput> {
		const result = await http.get(Endpoint.Ship.Get, { params: entityDto });
		return result.data.result;
	}

	public async getAll(
		pagedFilterAndSortedRequest: PagedShipResultRequestDto,
	): Promise<PagedResultDto<GetShipOutput>> {
		const result = await http.get(Endpoint.Ship.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}

	public async getShipNames(): Promise<GetShipOutput[]> {
		const result = await http.get(Endpoint.Ship.GetShipNames);
		return result.data.result;
	}

	public async getAllWithIdAndName(): Promise<ListResultDto<GetShipOutput>> {
		const result = await http.get(Endpoint.Ship.GetAllWithIdAndName);
		return result.data.result;
	}
}

export default new ShipService();
