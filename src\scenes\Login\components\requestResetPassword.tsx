import { Form } from "@ant-design/compatible";
import { FormComponentProps } from "@ant-design/compatible/lib/form";
import * as React from "react";
import "@ant-design/compatible/assets/index.css";
import { Col, Input, Modal, Row } from "antd";
import ComponentLayout from "../../../components/Layout/ComponentLayout";
import { L } from "../../../lib/abpUtility";
import accountService from "../../../services/account/accountService";
import rules from "./requestResetPassword.validation";

const FormItem = Form.Item;

interface IRequestResetPasswordProps extends FormComponentProps {
	visible: boolean;
	onCancel: () => void;
}

interface IRequestResetPasswordState {
	status: RequestResetPasswordStatus;
}

enum RequestResetPasswordStatus {
	Initial = 0,
	Succes = 1,
	Erorr = 2,
}

class RequestResetPassword extends React.Component<
	IRequestResetPasswordProps,
	IRequestResetPasswordState
> {
	state = {
		status: RequestResetPasswordStatus.Initial,
	};

	onRequestRessetPassword = async () => {
		await this.props.form.validateFields(async (err: any, values: any) => {
			if (!err) {
				try {
					await accountService.requestResetPassword(values);
					this.setState({ status: RequestResetPasswordStatus.Succes });
				} catch (err) {
					this.setState({
						status: RequestResetPasswordStatus.Erorr,
					});
				}
			}
		});
	};

	render() {
		const { getFieldDecorator } = this.props.form;
		return (
			<Modal
				visible={this.props.visible}
				cancelText={L("Cancel")}
				okText={L("SendResetPasswordLink")}
				onCancel={this.props.onCancel}
				onOk={this.onRequestRessetPassword}
				okButtonProps={
					this.state.status === RequestResetPasswordStatus.Succes
						? { disabled: true }
						: {}
				}
				title={L("ResetPassword")}
			>
				<Col>
					<Row>
						{this.state.status !== RequestResetPasswordStatus.Succes && (
							<Form>
								<FormItem
									label={L("Email")}
									{...ComponentLayout.formItemLayout}
								>
									{getFieldDecorator("email", { rules: rules.email })(
										<Input />,
									)}
								</FormItem>
							</Form>
						)}

						{this.state.status === RequestResetPasswordStatus.Erorr && (
							<Row className={"error-message"}>Error</Row>
						)}

						{this.state.status === RequestResetPasswordStatus.Succes &&
							L("Success. Check email to reset password")}
					</Row>
				</Col>
			</Modal>
		);
	}
}
export default Form.create<IRequestResetPasswordProps>()(RequestResetPassword);
