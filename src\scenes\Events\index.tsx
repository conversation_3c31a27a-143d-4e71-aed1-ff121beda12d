import { SearchOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Card, Col, Input, Row, Table } from "antd";
import { ColumnProps, TablePaginationConfig } from "antd/lib/table";
import { inject, observer } from "mobx-react";
import * as React from "react";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import { L } from "../../lib/abpUtility";
import { EntityDto } from "../../services/dto/entityDto";
import { ListResultDto } from "../../services/dto/pagedResultDto";
import { CreateOrUpdateEventsInput } from "../../services/events/dto/CreateOrUpdateEventsInput";
import { GetEventsOutput } from "../../services/events/dto/GetEventsOutput";
import EventsStore from "../../stores/eventsStore";
import Stores from "../../stores/storeIdentifier";
import {
	getTablePaginationOptions,
	renderDate,
	renderSearchIcon,
} from "../renderUtils";
import { TableRowSelection } from "antd/lib/table/interface";

export interface IEventsProps {
	eventsStore: EventsStore;
}

export interface IEventsState {
	maxResultCount: number;
	skipCount: number;
	sorting: string;
	searchTextInsideTable: Array<{ index: string; searchText: string }>;
	searchTextTable: string;
	searchedColumnTable: string;
	selectedRows: ListResultDto<EntityDto>;
	searchColumn: string;
	loading: boolean;
}

@inject(Stores.EventsStore)
@observer
class Events extends AppComponentBase<IEventsProps, IEventsState> {
	state = {
		maxResultCount: 10,
		skipCount: 0,
		sorting: "",
		searchTextInsideTable: [{ index: "", searchText: "" }],
		searchTextTable: "",
		searchedColumnTable: "",
		selectedRows: {
			items: [],
		},
		searchColumn: "",
		loading: false,
	};

	async componentDidMount() {
		await this.getAll();
	}

	async getAll() {
		this.setState({ loading: true });
		await this.props.eventsStore.getAll({
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: this.state.sorting,
			searchColumn: this.state.searchColumn,
		});
		this.setState({ loading: false });
	}
	getColumnsSearchProps = (
		dataIndex: keyof GetEventsOutput,
	): ColumnProps<GetEventsOutput> => ({
		filterDropdown: ({
			setSelectedKeys,
			selectedKeys,
			confirm,
			clearFilters,
		}) => {
			return (
				<div style={{ padding: 8 }}>
					<Input
						autoFocus
						placeholder={`Search ${dataIndex}`}
						value={selectedKeys ? selectedKeys[0] : ""}
						onChange={(e) => {
							if (setSelectedKeys)
								setSelectedKeys(e.target.value ? [e.target.value] : []);
						}}
						onPressEnter={() =>
							this.handleSearchTable(selectedKeys || [], dataIndex, confirm)
						}
						style={{ width: 188, marginBottom: 8, display: "block" }}
					/>
					<Button
						type="primary"
						onClick={() =>
							this.handleSearchTable(selectedKeys || [], dataIndex, confirm)
						}
						icon={<SearchOutlined />}
						size="small"
						style={{ width: 90, marginRight: 8 }}
					>
						Search
					</Button>
					<Button
						onClick={() => this.handleReset(dataIndex, clearFilters)}
						size="small"
						style={{ width: 90 }}
					>
						Reset
					</Button>
				</div>
			);
		},
		filterIcon: renderSearchIcon,
		onFilter: (value, record) => {
			if (record[dataIndex] != null && record[dataIndex] !== "") {
				return record[dataIndex]
					.toString()
					.toLowerCase()
					.includes(value.toString().toLowerCase());
			}
			return false;
		},
	});

	handleTableChange = (pagination: TablePaginationConfig) => {
		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
			},
			async () => await this.getAll(),
		);
	};

	handleSearchTable = (
		selectedKeys: React.Key[],
		dataIndex: string,
		confirm?: () => void,
	) => {
		const allSearches = this.state.searchTextInsideTable;
		const filtred = allSearches.filter((x) => x.index !== dataIndex);

		filtred.push({ index: dataIndex, searchText: selectedKeys[0].toString() });
		if (confirm) confirm();
		this.setState({
			searchTextTable: selectedKeys[0].toString(),
			searchedColumnTable: dataIndex,
			searchTextInsideTable: filtred,
		});
	};

	handleReset = (dataIndex: string, clearFilters?: () => void) => {
		const allSearches = this.state.searchTextInsideTable;
		const filtred = allSearches.filter((x) => x.index !== dataIndex);
		if (clearFilters) clearFilters();
		this.setState({
			searchTextTable: "",
			searchTextInsideTable: filtred,
		});
	};
	public render() {
		const { events } = this.props.eventsStore;
		const paginationOptions = getTablePaginationOptions(events?.totalCount);
		const columns = [
			{
				title: L("Guid"),
				dataIndex: "guid",
				key: "guid",
				width: 250,
				sorter: (a: GetEventsOutput, b: GetEventsOutput) =>
					a.guid?.length - b.guid?.length,
			},
			{
				title: L("ShipId"),
				dataIndex: "shipId",
				key: "shipId",
				width: 150,
				sorter: (a: GetEventsOutput, b: GetEventsOutput) => a.shipId - b.shipId,
				...this.getColumnsSearchProps("shipId"),
			},
			{
				title: L("Timestamp"),
				dataIndex: "timestamp",
				key: "timestamp",
				width: 180,
				render: (text: string) => renderDate(text),
				sorter: (a: GetEventsOutput, b: GetEventsOutput) =>
					new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime(),
				...this.getColumnsSearchProps("timestamp"),
			},
			// {
			//   title: L('Version'),
			//   dataIndex: 'version',
			//   key: 'version',
			//   width: 150,
			//   sorter: (a: GetEventsOutput, b: GetEventsOutput) => a.version?.length - b.version?.length,
			//   ...this.getColumnsSearchProps('version'),
			// },
			{
				title: L("Ship Info Id"),
				dataIndex: "shipInfoId",
				key: "shipInfoId",
				width: 150,
				sorter: (a: GetEventsOutput, b: GetEventsOutput) =>
					a.shipInfoId - b.shipInfoId,
				...this.getColumnsSearchProps("shipInfoId"),
			},
			// {
			//   title: L('Event Type'),
			//   dataIndex: 'eventType',
			//   key: 'eventType',
			//   width: 150,
			//   sorter: (a: GetEventsOutput, b: GetEventsOutput) => a.eventType?.length - b.eventType?.length,
			//   ...this.getColumnsSearchProps('eventType'),
			// },
			{
				title: L("Description"),
				dataIndex: "description",
				key: "description",
				width: 150,
				sorter: (a: GetEventsOutput, b: GetEventsOutput) =>
					a.description?.length - b.description?.length,
				...this.getColumnsSearchProps("description"),
			},
			// {
			//   title: L('Recipients List'),
			//   dataIndex: 'recipientsList',
			//   key: 'recipientsList',
			//   width: 180,
			//   sorter: (a: GetEventsOutput, b: GetEventsOutput) => a.recipientsList?.length - b.recipientsList?.length,
			//   ...this.getColumnsSearchProps('recipientsList'),
			// },
			{
				title: L("Payload"),
				dataIndex: "payload",
				key: "payload",
				width: 1000,
				sorter: (a: GetEventsOutput, b: GetEventsOutput) =>
					a.payload?.length - b.payload?.length,
			},
		];

		const rowSelection: TableRowSelection<GetEventsOutput> = {
			fixed: true,
			columnWidth: 60,
			onChange: (_, selectedRows: GetEventsOutput[]) => {
				const selectedRowsItems: EntityDto[] = selectedRows.map((x) => {
					return {
						id: x.id,
					};
				});
				this.setState({
					selectedRows: {
						items: selectedRowsItems,
					},
				});
			},
		};

		return (
			<Card>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey={(record: CreateOrUpdateEventsInput) =>
								record.id.toString()
							}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={this.state.loading}
							dataSource={events === undefined ? [] : events.items}
							onChange={this.handleTableChange}
							rowSelection={rowSelection}
							scroll={{ x: true, y: 400 }}
						/>
					</Col>
				</Row>
				<Chat />
			</Card>
		);
	}
}

export default Events;
