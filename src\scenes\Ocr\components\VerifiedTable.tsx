import { Warning20Regular } from "@fluentui/react-icons";
import { <PERSON><PERSON>, Table, Tooltip } from "antd";
import { inject, observer } from "mobx-react";
import moment from "moment";
import React from "react";
import { L } from "../../../lib/abpUtility";
import { OcrDto } from "../../../services/ocr/dTo/ocrDto";
import OcrStore from "../../../stores/ocrStore";
import Stores from "../../../stores/storeIdentifier";
import { renderDate } from "../../renderUtils";
import { TableRowSelection } from "antd/lib/table/interface";

interface IVerifyOcrTableProps {
	ocrStore: OcrStore;
	ocr: any;
	moveFile: (ocrStore: any) => Promise<void>;
}

@inject(Stores.OcrStore)
@observer
class VerifiedTable extends React.Component<IVerifyOcrTableProps> {
	ocrStore: OcrStore = this.props.ocrStore;

	state = {
		isModalVisible: false,
		selectedFile: null,
		selectedRow: null,
		maxResultCount: 10,
		skipCount: 0,
		ocrId: 0,
		filter: "",
	};
	constructor(props: any) {
		super(props);
		this.state = {
			isModalVisible: false,
			selectedFile: null,
			selectedRow: null,
			maxResultCount: 10,
			skipCount: 0,
			ocrId: 0,
			filter: "",
		};
	}

	public render() {
		const columns = [
			{
				title: L("FileName"),
				dataIndex: "fileName",
				key: "fileName",
				width: 150,
				render: (text: string, record: OcrDto) => {
					return (
						<span style={{ display: "flex", alignItems: "center", gap: "8px" }}>
							{text}
							{record.error && (
								<Tooltip
									placement="right"
									title={
										record.errorMessage.length > 0 ? (
											<div>
												Errors occurred during exporting file:
												<ul>
													{record.errorMessage.map((x) => (
														<li key={x}>{x}</li>
													))}
												</ul>
											</div>
										) : null
									}
								>
									<Warning20Regular color="orange" />
								</Tooltip>
							)}
						</span>
					);
				},
			},
			{
				title: L("Status"),
				dataIndex: "status",
				key: "status",
				width: 150,
			},
			{
				title: L("CreationTime"),
				dataIndex: "creationTime",
				key: "creationTime",
				width: 150,
				sorter: (a: OcrDto, b: OcrDto) =>
					moment(a.creationTime).diff(moment(b.creationTime)),
				render: (text: string, rekord: OcrDto, index: number) =>
					renderDate(text),
			},
			{
				title: L("LastModificationTime"),
				dataIndex: "lastModificationTime",
				key: "lastModificationTime",
				width: 150,
				sorter: (a: OcrDto, b: OcrDto) =>
					moment(a.lastModificationTime).diff(moment(b.lastModificationTime)),
				render: (text: string, rekord: OcrDto, index: number) =>
					renderDate(text),
			},
		];

		const { selectedRow } = this.state;

		const rowSelection: TableRowSelection<OcrDto> = {
			onChange: (selectedRowKeys, selectedRows: OcrDto[]) => {
				this.setState({
					selectedRow:
						selectedRows.length > 0
							? selectedRows[selectedRows.length - 1]
							: null,
				});
			},
		};

		const hasSelected = selectedRow;

		return (
			<>
				<div className="ocr-header__buttons">
					<Button
						type="primary"
						onClick={() => this.props.moveFile([selectedRow])}
						disabled={!hasSelected}
					>
						{L("MoveFile")}
					</Button>
				</div>
				<Table
					bordered={true}
					rowKey={(record: OcrDto) => record.id.toString()}
					columns={columns}
					dataSource={this.props.ocr ? this.props.ocr : []}
					rowSelection={rowSelection}
				/>
			</>
		);
	}
}

export default VerifiedTable;
