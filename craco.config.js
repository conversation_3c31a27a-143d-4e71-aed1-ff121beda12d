const {
	when,
	whenDev,
	whenProd,
	whenCI,
	whenTest,
	ESLINT_MODES,
	POSTCSS_MODES,
} = require("@craco/craco");
const CopyWebpackPlugin = require("copy-webpack-plugin");
const CracoAntDesignPlugin = require("craco-antd");

module.exports = {
	mode: "production",
	eslint: null,
	plugins: [
		{
			plugin: CracoAntDesignPlugin,
			options: {
				customizeTheme: {
					"@primary-color": "#6794E5",
					"@link-color": "#6794E5",
				},
			},
		},
	],
	webpack: {
		alias: {},
		plugins: [],
		configure: (webpackConfig, { env, paths }) => {
			if (!webpackConfig.plugins) {
				config.plugins = [];
			}

			if (process.env.NODE_ENV === "production") {
				webpackConfig.plugins.push(
					new CopyWebpackPlugin([
						{
							from: "node_modules/@aspnet/signalr/dist/browser/signalr.min.js",
						},
						{
							from: "node_modules/abp-web-resources/Abp/Framework/scripts/libs/abp.signalr-client.js",
						},
						{
							from: "src/lib/abp.js",
						},
					]),
				);
			} else {
				webpackConfig.plugins.push(
					new CopyWebpackPlugin([
						{
							from: "node_modules/@aspnet/signalr/dist/browser/signalr.min.js",
						},
						{
							from: "node_modules/abp-web-resources/Abp/Framework/scripts/libs/abp.signalr-client.js",
							to: "dist/abp.signalr-client.js",
						},
						{
							from: "src/lib/abp.js",
						},
					]),
				);
			}

			return webpackConfig;
		},
	},
};
