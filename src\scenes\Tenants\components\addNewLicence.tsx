import * as React from "react";

import { Form } from "@ant-design/compatible";
import "@ant-design/compatible/assets/index.css";

import { Modal, Table } from "antd";

import { FormComponentProps } from "@ant-design/compatible/lib/form";
import moment from "moment";
import { L } from "../../../lib/abpUtility";
import { GetLicenseOutput } from "../../../services/license/dto/getLicenseOutput";

export interface IAddLicenseProps extends FormComponentProps {
	visible: boolean;
	onOk: (ids: number[]) => void;
	licenses: GetLicenseOutput[];
	onCancel: () => void;
}

export interface ILicensesState {
	liceseIds: number[];
	selectedLicenses: number[] | string[];
}

class AddLicense extends React.Component<IAddLicenseProps, ILicensesState> {
	state: ILicensesState = {
		liceseIds: [],
		selectedLicenses: [],
	};

	reset = () => {
		this.setState({ selectedLicenses: [], liceseIds: [] });
	};

	render() {
		const { visible, onOk, onCancel } = this.props;
		const columns = [
			{
				title: L("Expiration date"),
				dataIndex: "expirationDate",
				key: "expirationDate",
				render: (text: string) => (
					<a>{moment(text).format("YYYY-MM-DD hh:mm:ss")}</a>
				),
			},
			{
				title: L("LicenceDescription"),
				dataIndex: "description",
				key: "description",
			},
			{
				title: L("LicenceKey"),
				dataIndex: "key",
				key: "key",
			},
		];
		const rowSelection = {
			selectedRowKeys: this.state.selectedLicenses,
			onChange: (
				selectedRowKeys: React.Key[],
				selectedRows: GetLicenseOutput[],
			) => {
				this.setState({ liceseIds: selectedRows.map((x) => x.id) });
			},
		};

		return (
			<Modal
				bodyStyle={{ padding: 0 }}
				visible={visible}
				onOk={() => {
					if (this.state.selectedLicenses.length >= 1) {
						onOk(this.state.liceseIds);
						this.reset();
					} else {
						onCancel();
					}
				}}
				okText="Add"
				onCancel={onCancel}
				title={L("Tenants")}
				width={550}
			>
				<Table
					rowSelection={rowSelection}
					columns={columns}
					dataSource={this.props.licenses}
					pagination={false}
					rowKey={(license) => {
						return license.key;
					}}
				/>
			</Modal>
		);
	}
}

export default Form.create<IAddLicenseProps>()(AddLicense);
