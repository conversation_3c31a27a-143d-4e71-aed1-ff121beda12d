import * as React from "react";
import "./index.less";

import { PlusOutlined, SettingOutlined } from "@ant-design/icons";

import {
	Button,
	Card,
	Checkbox,
	Col,
	Dropdown,
	Input,
	Menu,
	Modal,
	Row,
	Table,
} from "antd";
import { inject, observer } from "mobx-react";

import { CheckboxChangeEvent } from "antd/lib/checkbox";
import { ColumnProps, TablePaginationConfig } from "antd/lib/table";
import moment from "moment";
import AppComponentBase from "../../components/AppComponentBase";
import { L } from "../../lib/abpUtility";
import { EntityDto } from "../../services/dto/entityDto";
import { ListResultDto } from "../../services/dto/pagedResultDto";
import { GetVoyageOutput } from "../../services/voyage/dto/getVoyageOutput";
import Stores from "../../stores/storeIdentifier";
import VoyageStore from "../../stores/voyageStore";
import { ModalType } from "../ModalConsts";
import {
	getTablePaginationOptions,
	renderCheckboxValue,
	renderDate,
} from "../renderUtils";
import CreateOrUpdateVoyage from "./components/createOrUpdateVoyage";
import { TableRowSelection } from "antd/lib/table/interface";

export interface IVoyageProps {
	voyageStore: VoyageStore;
}

export interface IVoyageState {
	modalVisible: boolean;
	modalDetailsVisibile: boolean;
	maxResultCount: number;
	skipCount: number;
	voyageId: number;
	filter: string;
	isSearchingActive: boolean;
	isSearchingArchived: boolean;
	okButtonDisabled: boolean;
	selectedRows: ListResultDto<EntityDto>;
}

const confirm = Modal.confirm;
const Search = Input.Search;

@inject(Stores.VoyageStore)
@observer
class Voyage extends AppComponentBase<IVoyageProps, IVoyageState> {
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	formRef: any;

	state: IVoyageState = {
		modalVisible: false,
		modalDetailsVisibile: false,
		maxResultCount: 10,
		skipCount: 0,
		voyageId: 0,
		filter: "",
		isSearchingActive: false,
		isSearchingArchived: false,
		okButtonDisabled: false,
		selectedRows: {
			items: [],
		},
	};

	async componentDidMount() {
		await this.getAll();
		await this.props.voyageStore.getVoyageStatuses();
		await this.props.voyageStore.getVoyageInitializationSources();
	}

	async getAll() {
		await this.props.voyageStore.getAll({
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: this.state.filter,
			isActive: this.state.isSearchingActive,
			isArchived: this.state.isSearchingArchived,
		});
	}

	handleTableChange = (pagination: TablePaginationConfig) => {
		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
			},
			async () => await this.getAll(),
		);
	};

	Modal = () => {
		this.setState({
			modalVisible: !this.state.modalVisible,
		});
	};

	async createOrUpdateModalOpen(entityDto: EntityDto) {
		if (entityDto.id === 0) {
			await this.props.voyageStore.createVoyage();
		} else {
			await this.props.voyageStore.get(entityDto);
			await this.props.voyageStore.downloadQrCodeLink(entityDto);
		}

		this.setState({ voyageId: entityDto.id });
		this.Modal();

		this.formRef.props.form.setFieldsValue({
			...this.props.voyageStore.editVoyage,
			roleNames: this.props.voyageStore.editVoyage.roleNames,
		});
	}

	delete(input: EntityDto) {
		const self = this;
		confirm({
			title: L("DoYouWantToDeleteThisItem"),
			onOk() {
				self.props.voyageStore.delete(input);
			},
			onCancel() {},
		});
	}

	downloadQrCode = async (entityDto: EntityDto) => {
		await this.props.voyageStore.downloadQrCodeLink(entityDto);
		const link = document.createElement("a");
		link.href = this.props.voyageStore.qrCodeLink;
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	};

	handleCreate = () => {
		if (this.state.okButtonDisabled === false) {
			this.setState({ okButtonDisabled: true });
			const form = this.formRef.props.form;
			// biome-ignore lint/suspicious/noExplicitAny: <explanation>
			form.validateFields(async (err: any, values: any) => {
				if (err) {
					this.setState({ okButtonDisabled: false });
					return;
				}
					if (this.state.voyageId === 0) {
						await this.props.voyageStore.create(values);
					} else {
						await this.props.voyageStore.update({
							id: this.state.voyageId,
							...values,
						});
					}
				await this.getAll();
				form.resetFields();
				this.setModalVisibleFalse();
			});
		}
	};

	setModalVisibleFalse = () => {
		this.setState({ modalVisible: false });
		this.setState({ okButtonDisabled: false });
	};

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	saveFormRef = (formRef: any) => {
		this.formRef = formRef;
	};

	handleSearch = (value: string) => {
		this.setState({ filter: value }, async () => await this.getAll());
	};

	onChangeActiveCheckbox = (e: CheckboxChangeEvent) => {
		this.setState({ isSearchingActive: e.target.checked }, this.getAll);
	};

	onChangeArchivedCheckbox = (e: CheckboxChangeEvent) => {
		this.setState({ isSearchingArchived: e.target.checked }, this.getAll);
	};

	public render() {
		const { voyages } = this.props.voyageStore;
		const paginationOptions = getTablePaginationOptions(voyages?.totalCount);
		const columns : Array<ColumnProps<GetVoyageOutput>> = [
			{
				title: L("ShipName"),
				dataIndex: "shipName",
				key: "shipName",
				width: 200,
				fixed: "left" as const,
				sorter: (a: GetVoyageOutput, b: GetVoyageOutput) =>
					a.shipName?.length - b.shipName?.length,
			},
			{
				title: L("CallSign"),
				dataIndex: "callSign",
				key: "callSign",
				width: 150,
				sorter: (a: GetVoyageOutput, b: GetVoyageOutput) =>
					a.callSign?.length - b.callSign?.length,
			},
			{
				title: L("VoyageNumber"),
				dataIndex: "voyageNumber",
				key: "voyageNumber",
				width: 120,
				sorter: (a: GetVoyageOutput, b: GetVoyageOutput) =>
					a.voyageNumber?.length - b.voyageNumber?.length,
			},
			{
				title: L("VoyageId"),
				dataIndex: "id",
				key: "id",
				width: 110,
				sorter: (a: GetVoyageOutput, b: GetVoyageOutput) => a.id - b.id,
			},
			{
				title: L("Status"),
				dataIndex: "status",
				key: "status",
				width: 100,
				sorter: (a: GetVoyageOutput, b: GetVoyageOutput) => a.status - b.status,
			},
			{
				title: L("DestinationFrom"),
				dataIndex: "destinationFrom",
				key: "destinationFrom",
				width: 200,
				sorter: (a: GetVoyageOutput, b: GetVoyageOutput) =>
					a.destinationFrom.length - b.destinationFrom.length,
			},
			{
				title: L("DestinationTo"),
				dataIndex: "destinationTo",
				key: "destinationTo",
				width: 200,
				sorter: (a: GetVoyageOutput, b: GetVoyageOutput) =>
					a.destinationTo.length - b.destinationTo.length,
			},
			{
				title: L("IsArchived"),
				dataIndex: "isArchived",
				key: "isArchived",
				width: 120,
				render: renderCheckboxValue,
			},
			{
				title: L("IsActive"),
				dataIndex: "isActive",
				key: "isActive",
				width: 100,
				render: renderCheckboxValue,
			},
			{
				title: L("PlanedStart"),
				dataIndex: "planedStart",
				key: "planedStart",
				width: 150,
				sorter: (a: GetVoyageOutput, b: GetVoyageOutput) =>
					moment(a.planedStart).unix() - moment(b.planedStart).unix(),
				render: (text: string) => renderDate(text),
			},
			{
				title: L("PlanedEnd"),
				dataIndex: "planedEnd",
				key: "planedEnd",
				width: 150,
				sorter: (a: GetVoyageOutput, b: GetVoyageOutput) =>
					moment(a.planedEnd).unix() - moment(b.planedEnd).unix(),
				render: (text: string) => renderDate(text),
			},
			{
				title: L("DeviceId"),
				dataIndex: "deviceId",
				key: "deviceId",
				width: 170,
				sorter: (a: GetVoyageOutput, b: GetVoyageOutput) =>
					a.deviceId.length - b.deviceId.length,
			},
			{
				title: L("VoyageIniatliazationSource"),
				dataIndex: "initializationSource",
				key: "initializationSource",
				width: 150,
				sorter: (a: GetVoyageOutput, b: GetVoyageOutput) =>
					a.initializationSource - b.initializationSource,
			},
			{
				title: L("Actions"),
				fixed: "right" as const,
				render: (text: string, item) => (
					<div>
						<Dropdown
							trigger={["click"]}
							overlay={
								<Menu>
									<Menu.Item
										onClick={() =>
											this.createOrUpdateModalOpen({ id: item.id })
										}
									>
										{L("Edit")}
									</Menu.Item>
									<Menu.Item onClick={() => this.delete({ id: item.id })}>
										{L("Delete")}
									</Menu.Item>
									<Menu.Item
										onClick={() => this.downloadQrCode({ id: item.id })}
									>
										{L("DownloadQrCode")}
									</Menu.Item>
								</Menu>
							}
							placement="bottomLeft"
						>
							<Button type="primary" icon={<SettingOutlined />}>
								{L("Actions")}
							</Button>
						</Dropdown>
					</div>
				),
			},
		];

		const rowSelection: TableRowSelection<GetVoyageOutput> = {
			onChange: (selectedRowKeys: React.Key[], selectedRows: GetVoyageOutput[]) => {
				const selectedRowsItems: EntityDto[] = selectedRows.map((x) => {
					return {
						id: x.id,
					};
				});
				this.setState({
					selectedRows: {
						items: selectedRowsItems,
					},
				});
			},
		};

		return (
			<Card>
				<Row>
					<Col
						xs={{ span: 10, offset: 0 }}
						sm={{ span: 10, offset: 0 }}
						md={{ span: 10, offset: 0 }}
						lg={{ span: 10, offset: 0 }}
						xl={{ span: 10, offset: 0 }}
						xxl={{ span: 10, offset: 0 }}
					>
						<Search
							placeholder={this.L("Filter")}
							onSearch={this.handleSearch}
						/>
						<Checkbox
							checked={this.state.isSearchingActive}
							onChange={this.onChangeActiveCheckbox}
						>
							Only active {this.state.isSearchingActive}
						</Checkbox>
						<Checkbox
							checked={this.state.isSearchingArchived}
							onChange={this.onChangeArchivedCheckbox}
						>
							Include archived
						</Checkbox>
					</Col>
					<Col
						xs={{ span: 13, offset: 0 }}
						sm={{ span: 13, offset: 0 }}
						md={{ span: 13, offset: 0 }}
						lg={{ span: 13, offset: 0 }}
						xl={{ span: 13, offset: 0 }}
						xxl={{ span: 13, offset: 0 }}
						style={{ display: "flex", justifyContent: "right" }}
					>
						<Button
							type="primary"
							shape="circle"
							icon={<PlusOutlined />}
							onClick={() => this.createOrUpdateModalOpen({ id: 0 })}
						/>
					</Col>
				</Row>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							className="customTableVoyages"
							rowKey={(record) => record.id.toString()}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={!voyages}
							dataSource={voyages ? voyages.items : []}
							onChange={this.handleTableChange}
							scroll={{ x: 1500 }}
							rowSelection={rowSelection}
						/>
					</Col>
				</Row>
				<CreateOrUpdateVoyage
					wrappedComponentRef={this.saveFormRef}
					visible={this.state.modalVisible}
					onCancel={this.setModalVisibleFalse}
					modalType={
						this.state.voyageId === 0 ? ModalType.create : ModalType.edit
					}
					onCreate={this.handleCreate}
					roles={this.props.voyageStore.roles}
					voyageStore={this.props.voyageStore}
					okButtonDisabled={this.state.okButtonDisabled}
				/>
			</Card>
		);
	}
}

export default Voyage;
