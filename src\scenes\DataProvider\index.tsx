import { SearchOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Card, Col, Input, Row, Table } from "antd";
import { ColumnProps } from "antd/lib/table";
import { inject, observer } from "mobx-react";
import * as React from "react";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import { L } from "../../lib/abpUtility";
import { CreateOrUpdateDataProviderInput } from "../../services/dataProvider/dto/CreateOrUpdateDataProviderInput";
import { GetDataProviderOutput } from "../../services/dataProvider/dto/GetDataSourceOutput";
import { EntityDto } from "../../services/dto/entityDto";
import { ListResultDto } from "../../services/dto/pagedResultDto";
import DataProviderStore from "../../stores/dataProviderStore";
import Stores from "../../stores/storeIdentifier";
import { getTablePaginationOptions, renderSearchIcon } from "../renderUtils";
import {
	TablePaginationConfig,
	TableRowSelection,
} from "antd/lib/table/interface";

export interface IDataProviderProps {
	dataProviderStore: DataProviderStore;
}

export interface IDataProviderState {
	maxResultCount: number;
	skipCount: number;
	sorting: string;
	searchTextInsideTable: Array<{ index: string; searchText: string }>;
	searchTextTable: string;
	searchedColumnTable: string;
	selectedRows: ListResultDto<EntityDto>;
}

@inject(Stores.DataProviderStore)
@observer
class DataProvider extends AppComponentBase<
	IDataProviderProps,
	IDataProviderState
> {
	state = {
		maxResultCount: 10,
		skipCount: 0,
		sorting: "",
		searchTextInsideTable: [{ index: "", searchText: "" }],
		searchTextTable: "",
		searchedColumnTable: "",
		selectedRows: {
			items: [],
		},
	};

	async componentDidMount() {
		await this.getAll();
	}

	async getAll() {
		await this.props.dataProviderStore.getAll({
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: this.state.sorting,
		});
	}

	getColumnsSearchProps = (
		dataIndex: keyof GetDataProviderOutput,
	): ColumnProps<GetDataProviderOutput> => ({
		filterDropdown: ({
			setSelectedKeys,
			selectedKeys,
			confirm,
			clearFilters,
		}) => {
			return (
				<div style={{ padding: 8 }}>
					<Input
						autoFocus
						placeholder={`Search ${dataIndex}`}
						value={selectedKeys ? selectedKeys[0] : ""}
						onChange={(e) => {
							if (setSelectedKeys)
								setSelectedKeys(e.target.value ? [e.target.value] : []);
						}}
						onPressEnter={() =>
							this.handleSearchTable(selectedKeys || [], dataIndex, confirm)
						}
						style={{ width: 188, marginBottom: 8, display: "block" }}
					/>
					<Button
						type="primary"
						onClick={() =>
							this.handleSearchTable(selectedKeys || [], dataIndex, confirm)
						}
						icon={<SearchOutlined />}
						size="small"
						style={{ width: 90, marginRight: 8 }}
					>
						Search
					</Button>
					<Button
						onClick={() => this.handleReset(dataIndex, clearFilters)}
						size="small"
						style={{ width: 90 }}
					>
						Reset
					</Button>
				</div>
			);
		},
		filterIcon: renderSearchIcon,
		onFilter: (value, record) => {
			if (record[dataIndex] != null && record[dataIndex] !== "") {
				return record[dataIndex]
					.toString()
					.toLowerCase()
					.includes(value.toString().toLowerCase());
			}
			return false;
		},
	});

	handleTableChange = (pagination: TablePaginationConfig) => {
		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
			},
			async () => await this.getAll(),
		);
	};

	handleSearchTable = (
		selectedKeys: React.Key[],
		dataIndex: string,
		confirm?: () => void,
	) => {
		const allSearches = this.state.searchTextInsideTable;
		const filtred = allSearches.filter((x) => x.index !== dataIndex);

		filtred.push({ index: dataIndex, searchText: selectedKeys[0].toString() });
		if (confirm) confirm();
		this.setState({
			searchTextTable: selectedKeys[0].toString(),
			searchedColumnTable: dataIndex,
			searchTextInsideTable: filtred,
		});
	};

	handleReset = (dataIndex: string, clearFilters?: () => void) => {
		const allSearches = this.state.searchTextInsideTable;
		const filtred = allSearches.filter((x) => x.index !== dataIndex);
		if (clearFilters) clearFilters();
		this.setState({
			searchTextTable: "",
			searchTextInsideTable: filtred,
		});
	};

	public render() {
		const { datasProvider } = this.props.dataProviderStore;
		const paginationOptions = getTablePaginationOptions(
			datasProvider?.totalCount,
		);
		const columns = [
			{
				title: L("Name"),
				dataIndex: "name",
				key: "name",
				width: 150,
				sorter: (a: GetDataProviderOutput, b: GetDataProviderOutput) =>
					a.name?.length - b.name?.length,
			},
			{
				title: L("Connection Info"),
				dataIndex: "connectionInfo",
				key: "connectionInfo",
				width: 180,
				sorter: (a: GetDataProviderOutput, b: GetDataProviderOutput) =>
					a.connectionInfo?.length - b.connectionInfo?.length,
			},
			{
				title: L("Payload"),
				dataIndex: "payload",
				key: "payload",
				width: 1000,
			},
		];

		const rowSelection: TableRowSelection<GetDataProviderOutput> = {
			onChange: (_, selectedRows: GetDataProviderOutput[]) => {
				const selectedRowsItems: EntityDto[] = selectedRows.map((x) => {
					return {
						id: x.id,
					};
				});
				this.setState({
					selectedRows: {
						items: selectedRowsItems,
					},
				});
			},
		};

		return (
			<Card>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey={(record: CreateOrUpdateDataProviderInput) =>
								record.id.toString()
							}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={datasProvider === undefined}
							dataSource={
								datasProvider === undefined ? [] : datasProvider.items
							}
							onChange={this.handleTableChange}
							rowSelection={rowSelection}
							scroll={{ x: true }}
						/>
					</Col>
				</Row>
				<Chat />
			</Card>
		);
	}
}

export default DataProvider;
