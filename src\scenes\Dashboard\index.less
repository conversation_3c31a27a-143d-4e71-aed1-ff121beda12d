.dasboardCard-task {
  position: relative;
  background-color: #f9f8f8;
  box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  
  .dashboardCardContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    font-size: 30px;

    .dashboardCardName {
      color: #a9253b;
      font-size: inherit;
      font-weight: bold;
    }

    .dashboardCardCounter {
      color: #3f3f3f;
      font-weight: bold;
      font-size: inherit;
      margin-left: 5px;
      margin-bottom: 5px;
    }
  }
  .dashboardCardIcon {
    position: absolute;
    right: 5px;
    bottom: 5px;
    width: 100px;
    height: 100px;

    path {
      fill: #F1F1F1;
    }
  }
}

.dasboardCard-ticket {
  position: relative;
  background-color: #f9f8f8;
  box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  
  .dashboardCardContainer {
    display: flex;
    align-items: center;
    z-index: 1;
    font-size:30px; 

    .dashboardCardName {
      color: #a9253b;
      font-weight: bold;
    }

    .dashboardCardCounter {
      color: #3f3f3f;
      font-weight: bold;
      margin-left: 5px;
      margin-bottom: 5px;
    }
  }
  .dashboardCardIcon {
    position: absolute;
    right: 5px;
    bottom: 5px;
    width: 100px;
    height: 100px;

    path {
      fill: #F1F1F1;
    }
  }
}

.dasboardCard-comment {
  position: relative;
  background-color: #f9f8f8;
  box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  
  .dashboardCardContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    font-size:30px; 

    .dashboardCardName {
      color: #a9253b;
      font-weight: bold;
    }

    .dashboardCardCounter {
      color: #3f3f3f;
      font-weight: bold;
      margin-left: 5px;
      margin-bottom: 5px;
    }
  }
  .dashboardCardIcon {
    position: absolute;
    right: 5px;
    bottom: 5px;
    width: 100px;
    height: 100px;

    path {
      fill: #F1F1F1;
    }
  }
}
.dashboardCard-wrapper {
  display: flex;
  flex-wrap: wrap;
}
.dashboardCard {
  padding: 10px;
  height: 200px;
}

[class*='dasboardCard-'] {
  height: 100%;
  max-height: 200px;
  overflow-y: auto;
  display: flex;
  justify-content: space-between;
  .ant-card-body {
    display: flex;
    align-items: flex-start;
    width: 100%;
    .dashboardCardName {
      margin-bottom: 5px;
    }
    .dashboardCardData {
      color: #676767;
    }
  }
}

.dashboardBox {
  margin-top: 16px;
  background: #fff;
  min-height: 240px;
  border-radius: 5px;
}

.dashboardBoxLabel {
  margin-top: 16px;
  margin-bottom: 16px;
  font-size: 26px;
  font-weight: 600;
  line-height: 30px;
  letter-spacing: 0em;
  text-align: left;
  color: #3f3f3f;
  font-size: x-large;
  
}

.dashboardCardTinyLine {
  margin-top: 16px;
  background-color: #e91e63;
  border-radius: 5px;
}

.latestSocialTrendsList {
  margin-top: 16px;
  background: #00bcd4;
  border-radius: 5px;
}

.answeredTickeds {
  margin-top: 16px;
  background: #009688;
  border-radius: 5px;
}
.tinyChart-wrapper {
  padding-top: 40px;
}
