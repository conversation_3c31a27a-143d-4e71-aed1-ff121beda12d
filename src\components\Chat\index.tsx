import { Add24Filled, Chat24Regular } from "@fluentui/react-icons";
import React, { useState, useEffect, useRef, useCallback } from "react";
import "./index.less";
import { L } from "../../lib/abpUtility";
import ChatService from "../../services/chat/chatService";
import Utils from "../../utils/utils";
import SmallLoading from "../SmallLoading";

type Message = {
	text: string;
	from: "User" | "Assistant";
	id: string;
};

type Error = {
	displayError: boolean;
	message: string;
	blockSend: boolean;
};

const Chat: React.FC = () => {
	const [messages, setMessages] = useState<Message[]>([]);
	const [inputValue, setInputValue] = useState<string>("");
	const [isOpen, setIsOpen] = useState<boolean>(false);
	const [threadId, setThreadId] = useState<string | null>(null);
	const [isLoadingMessage, setIsLoadingMessage] = useState<boolean>(false);
	const [isLoading, setIsLoading] = useState<boolean>(false);
	const [error, setError] = useState<Error>({
		displayError: false,
		message: "",
		blockSend: false,
	});
	const messagesEndRef = useRef<HTMLDivElement | null>(null);

	const handleSendMessage = async () => {
		if (inputValue) {
			setMessages((prevMessages) => [
				...prevMessages,
				{ text: inputValue, from: "User", id: "" },
			]);

			let localThreadId = threadId;

			if (!localThreadId) {
				localThreadId = await ChatService.startThread();
				setThreadId(localThreadId);
			}

			if (localThreadId) {
				try {
					setIsLoadingMessage(true);
					const response = await ChatService.sendMessage(
						localThreadId,
						inputValue,
					);

					const lastResponse = response[response.length - 1];

					setMessages((prevMessages) => [
						...prevMessages,
						{ text: lastResponse.content, from: "Assistant", id: "" },
					]);
					setInputValue("");
					setError({
						message: "",
						displayError: false,
						blockSend: false,
					});
				} catch {
					setError({
						message: "Sending your message failed. Please trying again",
						displayError: true,
						blockSend: false,
					});
				} finally {
					setIsLoadingMessage(false);
				}
			}
		}
	};

	const toggleChat = async () => {
		if (isOpen) {
			setError({
				message: "",
				displayError: false,
				blockSend: false,
			});
		}
		setIsOpen(!isOpen);

		if (!isOpen) await GetLastChatInfo();
	};

	async function GetLastChatInfo(_retriesLeft = 4) {
		if (_retriesLeft === 0) return;

		setIsLoading(true);

		if (!threadId) {
			try {
				const lastChatInfo = await ChatService.getLastChatInfo();

				if (lastChatInfo?.messages) {
					const formattedMessages = lastChatInfo.messages.map((msg) => ({
						id: msg.messageId,
						text: msg.content,
						from:
							msg.from === "User"
								? "User"
								: ("Assistant" as "User" | "Assistant"),
					}));

					setMessages((prevMessages) => [
						...formattedMessages,
						...prevMessages,
					]);
				}
				setThreadId(lastChatInfo?.threadId || null);
			} catch {
				setError({
					message: "Can not connect to chat service. Trying to reconnect..",
					displayError: true,
					blockSend: true,
				});

				Utils.wait(3).then(() => GetLastChatInfo(_retriesLeft - 1));
			}
		}

		setIsLoading(false);
	}

	const startNewThread = async () => {
		setIsLoadingMessage(true);

		const newThreadId = await ChatService.startThread();
		setThreadId(newThreadId);
		setMessages([]);
		setIsOpen(false);

		setIsLoadingMessage(false);
	};

	const scrollToBottom = useCallback(() => {
		messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
	}, []);

	// biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
	useEffect(() => {
		scrollToBottom();
	}, [messages, scrollToBottom]);

	return (
		<>
			<div className="chat-icon" onClick={toggleChat}>
				<Chat24Regular />
			</div>
			{isOpen && (
				<div className="chat-container">
					<div className="chat-header">
						<span>{L("Chat read@sea")}</span>
						<div className="icons-container">
							<div
								className="minimize-container"
								onClick={() => setIsOpen(false)}
								onKeyPress={(e) => {
									console.log(e.key);
									if (e.key === "Esc") setIsOpen(false);
								}}
							>
								<div className="line-icon" />
							</div>
							<div className="close-icon" onClick={startNewThread}>
								<Add24Filled />
							</div>
						</div>
					</div>
					<div className="chat-messages">
						{messages.map((msg, index) => (
							<div key={msg.id} className={`chat-message ${msg.from}`}>
								{msg.text}
							</div>
						))}
						{isLoadingMessage && <SmallLoading />}
						<div ref={messagesEndRef} />
						{isLoading && (
							<div
								className={`loader-container ${messages.length === 0 ? "center-loader" : "bottom-loader"}`}
							>
								<SmallLoading />
							</div>
						)}
					</div>
					{error.displayError && (
						<div className="chat-error">{error.message}</div>
					)}
					<div className="chat-input">
						<input
							type="text"
							value={!isLoading ? inputValue : ""}
							onChange={(e) => setInputValue(e.target.value)}
							placeholder={L("Please a reply")}
						/>
						<button
							type="submit"
							onClick={handleSendMessage}
							disabled={isLoading || error.blockSend}
							onKeyPress={(e) => {
								if (e.key === "Enter") handleSendMessage();
							}}
						>
							{L("Send")}
						</button>
					</div>
				</div>
			)}
		</>
	);
};

export default Chat;
