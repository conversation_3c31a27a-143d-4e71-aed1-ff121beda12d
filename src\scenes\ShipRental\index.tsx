import { PlusOutlined, SettingOutlined } from "@ant-design/icons";
import {
	<PERSON><PERSON>,
	<PERSON>,
	Col,
	DatePicker,
	Dropdown,
	Menu,
	Modal,
	Row,
} from "antd";
import MenuItem from "antd/lib/menu/MenuItem";
import Table, { ColumnProps } from "antd/lib/table";
import { inject, observer } from "mobx-react";
import AppComponentBase from "../../components/AppComponentBase";
import { L, isGranted } from "../../lib/abpUtility";
import { EntityDto } from "../../services/dto/entityDto";
import ShipRentalStore from "../../stores/shipRentalStore";
import Stores from "../../stores/storeIdentifier";
import { getTablePaginationOptions, renderFilterIcon } from "../renderUtils";

import { WrappedFormUtils } from "@ant-design/compatible/lib/form/Form";
import {
	FilterDropdownProps,
	SorterResult,
	TablePaginationConfig,
} from "antd/lib/table/interface";
import moment from "moment";
import Chat from "../../components/Chat";
import FilterSelect from "../../components/FilterSelect/FilterSelect";
import { FilterByColumn } from "../../models/Sort/SortState";
import { GetRentalGroupByCounterparty } from "../../services/shipRental/dto/GetRentalGroupByCounterparty";
import ShipStore from "../../stores/shipStore";
import UserStore from "../../stores/userStore";
import utils from "../../utils/utils";
import { ModalType } from "../ModalConsts";
import CreateOrUpdateShipRental from "./components/createOrUpdateShipRental";

type RentedShip = GetRentalGroupByCounterparty["rentedShips"][0];

export interface IShipRentalProps {
	userStore: UserStore;
	shipRentalStore: ShipRentalStore;
	shipStore: ShipStore;
}

interface IActiveDateFilters {
	startHireStart?: string;
	startHireEnd?: string;
	stopHireStart?: string;
	stopHireEnd?: string;
}

export interface IShipRentalState {
	maxResultCount: number;
	skipCount: number;
	sorters: SorterResult<GetRentalGroupByCounterparty["rentedShips"][0]>[];
	okButtonDisabled: boolean;
	modalVisible: boolean;
	id: number;
	searchColumn: string;
	loading: boolean;
	filters: Array<FilterByColumn>;
	fetchingFilters: boolean;
	activeDateFilters: IActiveDateFilters;
}

const confirm = Modal.confirm;

@inject(Stores.ShipRentalStore)
@inject(Stores.ShipStore)
@inject(Stores.UserStore)
@observer
class ShipRental extends AppComponentBase<IShipRentalProps, IShipRentalState> {
	formRef?: WrappedFormUtils;

	state: IShipRentalState = {
		maxResultCount: 10,
		skipCount: 0,
		sorters: [],
		okButtonDisabled: false,
		modalVisible: false,
		fetchingFilters: false,
		id: 0,
		filters: [],
		searchColumn: "",
		loading: false,
		activeDateFilters: {
			startHireStart: undefined,
			startHireEnd: undefined,
			stopHireStart: undefined,
			stopHireEnd: undefined,
		},
	};

	async componentDidMount() {
		await Promise.all([
			this.getAll(),
			this.props.shipStore.getShipNames(),
			this.getListCounterparty(),
		]);
	}

	async getListCounterparty() {
		await this.props.userStore.getCharterers({
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: "",
		});
	}

	async getAll() {
		this.setState({ loading: true });
		const sortString = utils.getSorterString(this.state.sorters);

		const [searchColumnString, keywordString] = utils.getFilterStrings([
			...this.state.filters,
		]);

		await this.props.shipRentalStore.getRentalGroupyByCounterparty({
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: keywordString,
			searchColumn: searchColumnString,
			sorting: sortString,
			startHireStart: this.state.activeDateFilters.startHireStart || "",
			startHireEnd: this.state.activeDateFilters.startHireEnd || "",
			stopHireStart: this.state.activeDateFilters.stopHireStart || "",
			stopHireEnd: this.state.activeDateFilters.stopHireEnd || "",
		});
		this.setState({ loading: false });
	}

	handleDateFilter = (
		columnKey: "startDate" | "endDate",
		startDate?: string,
		endDate?: string,
	) => {
		if (columnKey === "startDate") {
			this.setState(
				(prevState) => ({
					activeDateFilters: {
						...prevState.activeDateFilters,
						startHireStart: startDate,
						startHireEnd: endDate,
					},
				}),
				() => {
					this.getAll();
				},
			);
		} else if (columnKey === "endDate") {
			this.setState(
				(prevState) => ({
					activeDateFilters: {
						...prevState.activeDateFilters,
						stopHireStart: startDate,
						stopHireEnd: endDate,
					},
				}),
				() => {
					this.getAll();
				},
			);
		}
	};

	private handleFilter(
		value: string,
		column: string,
		confirm: FilterDropdownProps["confirm"],
	) {
		let currentFilters = [...this.state.filters];
		currentFilters = currentFilters.filter((f) => f.column !== column);
		if (value) {
			currentFilters.push({ value, column: column });
		}

		this.setState({ filters: currentFilters }, async () => {
			await this.getAll();
			confirm?.();
		});
	}

	getColumnSearchProps = (
		dataIndex: string,
		displayName: string,
	): ColumnProps<RentedShip> => ({
		filterDropdown: (props: FilterDropdownProps) => (
			<div>
				{props.visible && (
					<FilterSelect
						{...props}
						loading={this.state.fetchingFilters}
						handleFilter={(value) =>
							this.handleFilter(value, dataIndex, props.confirm)
						}
						title={displayName}
						value={
							this.state.filters.find((x) => x.column === dataIndex)?.value
						}
						options={this.props.shipRentalStore.filters?.map((x) => {
							return { key: x, value: x };
						})}
					/>
				)}
			</div>
		),
		onFilterDropdownVisibleChange: (v) => {
			if (v) this.fetchFilters(dataIndex);
		},
		filterIcon: () =>
			renderFilterIcon(
				this.state.filters.findIndex((x) => x.column === dataIndex) >= 0,
			),
		sortOrder: this.state.sorters.find((x) => x.columnKey === dataIndex)?.order,
	});

	async fetchFilters(property: string) {
		this.setState({ fetchingFilters: true });
		const [searchColumnString, keywordString] = utils.getFilterStrings([
			...this.state.filters,
		]);

		try {
			await this.props.shipRentalStore.getFilters(
				{
					keyword: keywordString,
					searchColumn: searchColumnString,
				},
				property,
			);
		} catch {
			Modal.error({
				title: "Error during fetching",
				content: (
					<div>
						<p>Failed to fetch filters for column</p>
					</div>
				),
			});
		}

		this.setState({ fetchingFilters: false });
	}

	handleTableChange = (
		pagination: TablePaginationConfig,
		filters: Partial<
			Record<keyof GetRentalGroupByCounterparty["rentedShips"][0], string[]>
		>,
		sorter:
			| SorterResult<GetRentalGroupByCounterparty["rentedShips"][0]>
			| SorterResult<GetRentalGroupByCounterparty["rentedShips"][0]>[],
	) => {
		const sorters = utils.getSorters(sorter);
		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
				sorters: sorters,
			},
			async () => await this.getAll(),
		);
	};

	async delete(input: EntityDto) {
		const self = this;
		confirm({
			title: "Do you want to delete these items?",
			async onOk() {
				await self.props.shipRentalStore.delete(input);
			},
			onCancel() {},
		});
		await this.getAll();
	}

	// biome-ignore lint/suspicious/noExplicitAny: Poor type implementation from library
	saveFormRef = (formRef: any) => {
		if (!formRef) return;
		this.formRef = formRef.props.form;
	};

	Modal = () => {
		this.setState({
			modalVisible: !this.state.modalVisible,
		});
	};

	handleCreate = () => {
		if (this.state.okButtonDisabled === false) {
			if (!this.formRef) return;
			const form = this.formRef;

			// biome-ignore lint/suspicious/noExplicitAny: Poor type implementation from library
			form.validateFields(async (err: any, values: any) => {
				if (err) {
					return;
				}
				this.setState({ okButtonDisabled: true });
				try {
					if (this.state.id === 0) {
						await this.props.shipRentalStore.create(values);
					} else {
						await this.props.shipRentalStore.update({
							id: this.state.id,
							...values,
							ship: this.props.shipRentalStore.editShipRentals.ship,
						});
					}
					await this.getAll();
					form.resetFields();
					this.setModalVisibleFalse();
				} catch (ex) {
				} finally {
					this.setState({ okButtonDisabled: false });
				}
			});
		}
	};

	setModalVisibleFalse = () => {
		this.setState({ modalVisible: false });
	};

	async createOrUpdateModalOpen(entityDto: EntityDto) {
		if (entityDto.id === 0) {
			await this.props.shipRentalStore.createShipRental();
		} else {
			await this.props.shipRentalStore.get(entityDto);
		}

		this.setState({ id: entityDto.id });
		this.Modal();

		const shipRentalProps = this.props.shipRentalStore.editShipRentals;

		if (shipRentalProps !== null && shipRentalProps.id !== 0) {
			if (!this.formRef) return;
			this.formRef.setFieldsValue({
				...shipRentalProps,
				startDate: moment(shipRentalProps.startDate),
				endDate: moment(shipRentalProps.endDate),
			});
		}
	}

	detailsRowTable = (record: GetRentalGroupByCounterparty) => {
		const columns: Array<ColumnProps<RentedShip>> = [
			{
				title: L("Ship Name"),
				dataIndex: "shipName",
				key: "ship.shipName",
				sorter: true,
				...this.getColumnSearchProps("ship.shipName", L("Ship Name")),
			},
			{
				title: L("IMO"),
				dataIndex: "imoNumber",
				key: "ship.imoNumber",
				sorter: true,
				...this.getColumnSearchProps("ship.imoNumber", L("IMO")),
			},
			{
				title: L("Start of hire date and time"),
				dataIndex: "startDate",
				key: "startDate",
				render: (text: string) => (
					<p>{moment(text).format("YYYY-MM-DD hh:mm")}</p>
				),
				sorter: true,
				filterDropdown: ({ confirm, clearFilters }: FilterDropdownProps) => (
					<div style={{ padding: 8 }}>
						<DatePicker.RangePicker
							style={{ marginBottom: 8, width: 200 }}
							onChange={(dates) => {
								if (dates && dates.length === 2 && dates[0] && dates[1]) {
									const start = dates[0];
									const end = dates[1];
									this.handleDateFilter(
										"startDate",
										start.toISOString(),
										end.toISOString(),
									);
									confirm?.();
								} else {
									this.handleDateFilter("startDate", undefined, undefined);
									clearFilters?.();
								}
							}}
						/>
					</div>
				),
				filterIcon: renderFilterIcon(
					this.state.activeDateFilters.startHireStart !== undefined &&
						this.state.activeDateFilters.startHireEnd !== undefined,
				),
			},
			{
				title: L("Stop hire date and time"),
				dataIndex: "endDate",
				key: "endDate",
				render: (text: string) => (
					<p>{moment(text).format("YYYY-MM-DD hh:mm")}</p>
				),
				sorter: true,
				filterDropdown: ({ confirm, clearFilters }: FilterDropdownProps) => (
					<div style={{ padding: 8 }}>
						<DatePicker.RangePicker
							style={{ marginBottom: 8, width: 200 }}
							onChange={(dates) => {
								if (dates && dates.length === 2 && dates[0] && dates[1]) {
									const start = dates[0];
									const end = dates[1];
									this.handleDateFilter(
										"endDate",
										start.toISOString(),
										end.toISOString(),
									);
									confirm?.();
								} else {
									this.handleDateFilter("endDate", undefined, undefined);
									clearFilters?.();
								}
							}}
						/>
					</div>
				),
				filterIcon: renderFilterIcon(
					this.state.activeDateFilters.stopHireStart !== undefined &&
						this.state.activeDateFilters.stopHireEnd !== undefined,
				),
			},
			{
				title: L("Actions"),
				fixed: "right" as const,
				width: 150,
				render: (text: string, item: RentedShip) => (
					<div>
						<Dropdown
							trigger={["click"]}
							overlay={
								<Menu>
									{isGranted("Pages.ShipRentals-Edit") && (
										<MenuItem
											onClick={() =>
												this.createOrUpdateModalOpen({ id: item.rentalId })
											}
										>
											{L("Edit")}
										</MenuItem>
									)}
									{isGranted("Pages.ShipRentals-Delete") && (
										<MenuItem
											onClick={() => this.delete({ id: item.rentalId })}
										>
											{L("Delete")}
										</MenuItem>
									)}
								</Menu>
							}
							placement="bottomLeft"
						>
							<Button type="primary" icon={<SettingOutlined />}>
								{L("Actions")}
							</Button>
						</Dropdown>
					</div>
				),
			},
		];

		const hasNoActions =
			!isGranted("Pages.Roles-Edit") && !isGranted("Pages.Roles-Delete");

		if (hasNoActions) {
			columns.pop();
		}

		return (
			<Table
				rowKey={(record: GetRentalGroupByCounterparty["rentedShips"][0]) =>
					record.imoNumber.toString()
				}
				bordered={true}
				columns={columns}
				loading={this.state.loading}
				dataSource={record === undefined ? [] : record.rentedShips}
				onChange={this.handleTableChange}
				scroll={{ x: true }}
			/>
		);
	};

	public render() {
		const { shipRentalsList } = this.props.shipRentalStore;
		const paginationOptions = getTablePaginationOptions(
			shipRentalsList?.totalCount,
		);
		const columns = [
			{
				title: L("Counterparty"),
				dataIndex: "counterParty",
				key: "counterParty.userName",
				sorter: true,
				...(this.getColumnSearchProps(
					"counterParty.userName",
					L("Counterparty"),
				) as unknown as ColumnProps<GetRentalGroupByCounterparty>),
			},
		];

		return (
			<Card>
				<Row>
					<Col
						xs={{ span: 23, offset: 0 }}
						sm={{ span: 23, offset: 0 }}
						md={{ span: 23, offset: 0 }}
						lg={{ span: 23, offset: 0 }}
						xl={{ span: 23, offset: 0 }}
						xxl={{ span: 23, offset: 0 }}
						style={{ display: "flex", justifyContent: "end" }}
					>
						{isGranted("Pages.ShipRentals-Add") && (
							<Button
								type="primary"
								shape="circle"
								icon={<PlusOutlined />}
								style={{
									display: "flex",
									justifyContent: "center",
									alignItems: "center",
								}}
								onClick={() => this.createOrUpdateModalOpen({ id: 0 })}
							/>
						)}
					</Col>
				</Row>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey={(record: GetRentalGroupByCounterparty) =>
								record.counterParty.toString()
							}
							bordered={true}
							columns={columns}
							expandedRowRender={this.detailsRowTable}
							pagination={paginationOptions}
							loading={this.state.loading}
							dataSource={
								shipRentalsList === undefined ? [] : shipRentalsList.items
							}
							scroll={{ x: true }}
						/>
					</Col>
				</Row>
				<CreateOrUpdateShipRental
					listCounterparty={this.props.userStore?.users?.items || []}
					shipRentalStore={this.props.shipRentalStore}
					shipStore={this.props.shipStore}
					wrappedComponentRef={this.saveFormRef}
					visible={this.state.modalVisible}
					onCancel={this.setModalVisibleFalse}
					modalType={this.state.id === 0 ? ModalType.create : ModalType.edit}
					onCreate={this.handleCreate}
					okButtonDisabled={this.state.okButtonDisabled}
					roles={this.props.shipRentalStore.roles}
				/>
				<Chat />
			</Card>
		);
	}
}

export default ShipRental;
