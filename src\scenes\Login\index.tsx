import "./index.less";

import * as React from "react";

import { LockOutlined, UserOutlined } from "@ant-design/icons";

import { Form } from "@ant-design/compatible";
import "@ant-design/compatible/assets/index.css";

import { <PERSON>ton, Card, Checkbox, Col, Input, Modal, Row } from "antd";
import { inject, observer } from "mobx-react";

import AccountStore from "../../stores/accountStore";
import AuthenticationStore from "../../stores/authenticationStore";
import { FormComponentProps } from "@ant-design/compatible/lib/form";
import { L } from "../../lib/abpUtility";
import { Redirect } from "react-router-dom";
import SessionStore from "../../stores/sessionStore";
import Stores from "../../stores/storeIdentifier";
import TenantAvailabilityState from "../../services/account/dto/tenantAvailabilityState";
import rules from "./index.validation";
import RequestResetPassword from "./components/requestResetPassword";
import { formatString } from "../renderUtils";
import Logo from "../../images/LOGO.svg";

const FormItem = Form.Item;
// biome-ignore lint/suspicious/noExplicitAny: no type implemented
declare const abp: any;

export interface ILoginProps extends FormComponentProps {
	authenticationStore: AuthenticationStore;
	sessionStore: SessionStore;
	accountStore: AccountStore;
	history: History;
	// biome-ignore lint/suspicious/noExplicitAny: no type implemented
	location: any;
}

interface ILoginState {
	requestResetPasswordModalVisible: boolean;
	changingTenant: boolean;
	signingIn: boolean;
}

@inject(Stores.AuthenticationStore, Stores.SessionStore, Stores.AccountStore)
@observer
class Login extends React.Component<ILoginProps, ILoginState> {
	state = {
		requestResetPasswordModalVisible: false,
		changingTenant: false,
		signingIn: false,
	};

	requestResetPasswordModalOpen = () => {
		this.setState({
			requestResetPasswordModalVisible: true,
		});
	};

	requestResetPasswordModalHide = () => {
		this.setState({
			requestResetPasswordModalVisible: false,
		});
	};

	changeTenant = async () => {
		const tenancyName = this.props.form.getFieldValue("tenancyName");
		const { loginModel } = this.props.authenticationStore;

		if (!tenancyName) {
			abp.multiTenancy.setTenantIdCookie(undefined);
			await this.props.sessionStore?.getCurrentLoginInformations();

			return;
		}
		this.setState({ changingTenant: true });
		await this.props.accountStore.isTenantAvailable(tenancyName);
		const { tenant } = this.props.accountStore;
		switch (tenant.state) {
			case TenantAvailabilityState.Available:
				abp.multiTenancy.setTenantIdCookie(tenant.tenantId);
				loginModel.tenancyName = tenancyName;
				loginModel.toggleShowModal();
				await this.props.sessionStore?.getCurrentLoginInformations();
				break;
			case TenantAvailabilityState.InActive:
				Modal.error({ title: L("Error"), content: L("TenantIsNotActive") });
				break;
			case TenantAvailabilityState.NotFound:
				Modal.error({
					title: L("Error"),
					content: formatString(
						L("ThereIsNoTenantDefinedWithName{0}"),
						tenancyName,
					),
				});
				break;
		}
		this.setState({ changingTenant: false });
	};

	handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		const { loginModel } = this.props.authenticationStore;
		this.props.form.validateFields(async (err, values) => {
			if (!err) {
				this.setState({ signingIn: true });
				try {
					await this.props.authenticationStore.login(values);
					sessionStorage.setItem(
						"rememberMe",
						loginModel.rememberMe ? "1" : "0",
					);
					const { state } = this.props.location;
					window.location = state ? state.from.pathname : "/";
				} catch (ex) {
					console.error(`Error during log in: ${ex}`);
				}
				this.setState({ signingIn: false });
			}
		});
	};

	public render() {
		const { from } = this.props.location.state || { from: { pathname: "/" } };
		if (this.props.authenticationStore?.isAuthenticated)
			return <Redirect to={from} />;

		const { loginModel } = this.props.authenticationStore;
		const { getFieldDecorator, getFieldValue } = this.props.form;
		return (
			<>
				<Row
					style={{
						justifyContent: "center",
						display: "flex",
						marginTop: "10vh",
					}}
				>
					<div className="welcome">
						<h1
							style={{
								fontSize: "3rem",
								fontWeight: "bold",
								color: "#454545",
								margin: "0",
								verticalAlign: "top",
							}}
						>
							WELCOME TO
						</h1>
						<img
							style={{ width: "200px", height: "42px", marginBottom: "-8px" }}
							src={Logo}
						/>
					</div>
				</Row>
				<Col className="name">
					<Form className="" onSubmit={this.handleSubmit}>
						<Row>
							<Col span={8} offset={8}>
								<Card>
									<Row>
										{this.props.sessionStore.currentLogin.tenant ? (
											<Col span={24} offset={0} style={{ textAlign: "center" }}>
												<Button
													type="link"
													onClick={loginModel.toggleShowModal}
													style={{ fontSize: "16px" }}
												>
													{L("CurrentTenant")}:{" "}
													<b style={{ marginLeft: "8px" }}>
														{
															this.props.sessionStore.currentLogin.tenant
																.tenancyName
														}
													</b>
												</Button>
											</Col>
										) : (
											<Col span={24} offset={0} style={{ textAlign: "center" }}>
												<Button
													type="link"
													onClick={loginModel.toggleShowModal}
													style={{ fontSize: "16px" }}
												>
													{L("NotSelected")}
												</Button>
											</Col>
										)}
									</Row>
								</Card>
							</Col>
						</Row>

						<Row>
							<Modal
								visible={loginModel.showModal}
								onCancel={loginModel.toggleShowModal}
								onOk={this.changeTenant}
								title={L("ChangeTenant")}
								okText={L("OK")}
								cancelText={L("Cancel")}
								okButtonProps={{ loading: this.state.changingTenant }}
							>
								<Row style={{ display: "block" }}>
									<h3>{L("TenancyName")}</h3>
									<Col>
										<FormItem>
											{getFieldDecorator(
												"tenancyName",
												{},
											)(
												<Input
													placeholder={L("TenancyName")}
													prefix={
														<UserOutlined
															style={{ color: "rgba(0,0,0,.25)" }}
														/>
													}
													size="large"
												/>,
											)}
										</FormItem>
										{!getFieldValue("tenancyName") ? (
											<div>{L("LeaveEmptyToSwitchToHost")}</div>
										) : (
											""
										)}
									</Col>
								</Row>
							</Modal>
						</Row>
						<Row style={{ marginTop: 10 }}>
							<Col span={8} offset={8}>
								<Card>
									<div style={{ textAlign: "center" }}>
										<h3 style={{ marginBottom: "1.5rem" }}>
											Verified Auditable Data - Performance Monitoring -
											Emissions Reporting
										</h3>
									</div>
									<FormItem>
										{getFieldDecorator("userNameOrEmailAddress", {
											rules: rules.userNameOrEmailAddress,
										})(
											<Input
												placeholder={L("UserNameOrEmail")}
												prefix={
													<UserOutlined style={{ color: "rgba(0,0,0,.25)" }} />
												}
												size="large"
											/>,
										)}
									</FormItem>

									<FormItem>
										{getFieldDecorator("password", { rules: rules.password })(
											<Input
												placeholder={L("Password")}
												prefix={
													<LockOutlined style={{ color: "rgba(0,0,0,.25)" }} />
												}
												type="password"
												size="large"
											/>,
										)}
									</FormItem>
									<Row style={{ margin: "0px 0px 10px 15px " }}>
										<Col span={12} offset={0}>
											<Checkbox
												checked={loginModel.rememberMe}
												onChange={loginModel.toggleRememberMe}
												style={{ paddingRight: 8 }}
											/>
											{L("RememberMe")}
											<br />
											<Button
												type="link"
												onClick={this.requestResetPasswordModalOpen}
											>
												{L("ForgotPassword")}
											</Button>
										</Col>

										<Col span={8} offset={4}>
											<Button
												type="primary"
												htmlType={"submit"}
												loading={this.state.signingIn}
											>
												{L("LogIn")}
											</Button>
										</Col>
									</Row>
								</Card>
							</Col>
						</Row>
					</Form>

					<RequestResetPassword
						visible={this.state.requestResetPasswordModalVisible}
						onCancel={this.requestResetPasswordModalHide}
					/>
				</Col>
			</>
		);
	}
}

export default Form.create()(Login);
