import { PlusOutlined, SettingOutlined } from "@ant-design/icons";

import { <PERSON><PERSON>, Card, Col, Dropdown, Menu, Modal, Row, Table } from "antd";
import { inject, observer } from "mobx-react";

import { WrappedFormUtils } from "@ant-design/compatible/lib/form/Form";
import { ColumnProps, TablePaginationConfig } from "antd/lib/table";
import { FilterDropdownProps, SorterResult } from "antd/lib/table/interface";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import { L, isGranted } from "../../lib/abpUtility";
import { FilterByColumn } from "../../models/Sort/SortState";
import { EntityDto } from "../../services/dto/entityDto";
import { FuelTypeDto } from "../../services/fuelType/dto/fuelTypeDto";
import { PagedMeterResultRequestFuelTypeDto } from "../../services/fuelType/dto/pagedFilterAndSortedRequestFuelType";
import FuelTypeStore from "../../stores/fuelTypeStore";
import MeterStore from "../../stores/meterStore";
import Stores from "../../stores/storeIdentifier";
import utils from "../../utils/utils";
import { ModalType } from "../ModalConsts";
import { getTablePaginationOptions, renderFilterIcon } from "../renderUtils";
import CreateOrUpdateFuelType from "./components/createOrUpdateFuelType";
import FilterSelect from "../../components/FilterSelect/FilterSelect";

export interface IFuelTypeProps {
	fuelTypeStore: FuelTypeStore;
	meterStore: MeterStore;
}

export interface IFuelTypeState {
	modalVisible: boolean;
	loading: boolean;
	fetchingFilters: boolean;
	maxResultCount: number;
	skipCount: number;
	filter: string;
	sorters: SorterResult<FuelTypeDto>[];
	filters: Array<FilterByColumn>;
	sending: boolean;
	okButtonDisabled: boolean;
	id: number;
}

const confirm = Modal.confirm;

type PreviousState = {
	sorters: IFuelTypeState["sorters"];
	filters: IFuelTypeState["filters"];
};

@inject(Stores.FuelTypeStore)
@inject(Stores.MeterStore)
@observer
class FuelType extends AppComponentBase<IFuelTypeProps, IFuelTypeState> {
	formRef?: WrappedFormUtils<FuelTypeDto>;

	state: IFuelTypeState = {
		modalVisible: false,
		loading: false,
		fetchingFilters: false,
		maxResultCount: 10,
		skipCount: 0,
		filter: "",
		sorters: [],
		filters: [],
		sending: false,
		okButtonDisabled: false,
		id: 0,
	};

	async componentDidMount() {
		const prevState = this.extractPreviousState();

		this.setState({ ...prevState }, async () => {
			await this.getAll();
		});
	}

	extractPreviousState(): PreviousState {
		let state: PreviousState = {
			sorters: [],
			filters: [],
		};

		const prevState =
			utils.getSortAndFilterFromStorage<PreviousState>("fuel-filters");

		if (prevState)
			state = {
				...prevState,
			};

		utils.removeStateFromStorage("fuel-filters");

		return state;
	}

	private handleFilter(value: string, column: string) {
		let currentFilters = [...this.state.filters];
		currentFilters = currentFilters.filter((f) => f.column !== column);

		if (value) {
			currentFilters.push({ value, column: column });
		}

		this.setState({ filters: currentFilters }, async () => {
			await this.getAll();
		});
	}


	getColumnSearchProps = (
		dataIndex: string,
		displayName: string,
	): ColumnProps<FuelTypeDto> => ({
		filterDropdown: (props: FilterDropdownProps) => (
			<div>
				{props.visible && (
					<FilterSelect
						{...props}
						loading={this.state.fetchingFilters}
						handleFilter={(value) => this.handleFilter(value, dataIndex)}
						title={displayName}
						value={
							this.state.filters.find((x) => x.column === dataIndex)?.value
						}
						options={this.props.fuelTypeStore.filters?.map((x) => {
							return { key: x, value: x };
						})}
					/>
				)}
			</div>
		),
		onFilterDropdownVisibleChange: (v) => {
			if (v) this.fetchFilters(dataIndex);
		},
		filterIcon: () =>
			renderFilterIcon(
				this.state.filters.findIndex((x) => x.column === dataIndex) >= 0,
			),
		sortOrder: this.state.sorters.find((x) => x.columnKey === dataIndex)?.order,
	});

	componentWillUnmount(): void {
		const settings: PreviousState = {
			sorters: this.state.sorters,
			filters: this.state.filters,
		};

		utils.saveSortAndFilterToStorage("fuel-filters", settings);
	}

	async getAll() {
		const sortString = utils.getSorterString(this.state.sorters);

		const [searchColumnString, keywordString] = utils.getFilterStrings([
			...this.state.filters,
		]);

		const filterData: PagedMeterResultRequestFuelTypeDto = {
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: keywordString,
			searchColumn: searchColumnString,
			sorting: sortString,
		};

		this.setState({ loading: true });
		await this.props.fuelTypeStore.getAll(filterData);
		this.setState({ loading: false });
	}

	handleTableChange = (
		pagination: TablePaginationConfig,
		filters: Partial<Record<keyof FuelTypeDto, string[]>>,
		sorter: SorterResult<FuelTypeDto> | SorterResult<FuelTypeDto>[],
	) => {
		const sorters = utils.getSorters(sorter);
		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
				sorters: sorters,
			},
			async () => await this.getAll(),
		);
	};

	Modal = () => {
		this.setState({
			modalVisible: !this.state.modalVisible,
		});
	};

	handleSearch = (value: string) => {
		this.setState({ filter: value }, async () => await this.getAll());
	};

	delete(input: EntityDto) {
		const self = this;
		confirm({
			title: "Do you want to delete these items?",
			onOk() {
				self.props.fuelTypeStore.delete(input);
			},
			onCancel() { },
		});
	}

	// biome-ignore lint/suspicious/noExplicitAny: Poor type implementation from library
	saveFormRef = (formRef: any) => {
		if (!formRef) return;
		this.formRef = formRef.props.form;
	};

	handleCreate = () => {
		if (this.state.okButtonDisabled === false) {
			const form = this.formRef;
			if (!form) return;

			// biome-ignore lint/suspicious/noExplicitAny: Poor type implementation from library
			form.validateFields(async (err: any, values) => {
				if (err) {
					this.setState({ okButtonDisabled: false });
					return;
				}
				this.setState({ okButtonDisabled: true });
				if (this.state.id === 0) {
					await this.props.fuelTypeStore.create(values);
				} else {
					await this.props.fuelTypeStore.update({
						...values,
						id: this.state.id,
					});
				}
				await this.getAll();
				form.resetFields();
				this.setModalVisibleFalse();
			});
		}
	};

	setModalVisibleFalse = () => {
		this.setState({ modalVisible: false, okButtonDisabled: false });
	};

	async createOrUpdateModalOpen(entityDto: EntityDto) {
		if (entityDto.id === 0) {
			await this.props.fuelTypeStore.createFuelType();
		} else {
			await this.props.fuelTypeStore.get(entityDto);
		}

		this.setState({ id: entityDto.id });
		this.Modal();
		if (!this.formRef) return;

		this.formRef.setFieldsValue({
			...this.props.fuelTypeStore.editFuelType,
		});
	}

	async fetchFilters(property: string) {
		this.setState({ fetchingFilters: true });
		const [searchColumnString, keywordString] = utils.getFilterStrings([
			...this.state.filters,
		]);

		try {
			await this.props.fuelTypeStore.getFilters(
				{
					keyword: keywordString,
					searchColumn: searchColumnString,
				},
				property,
			);
		} catch {
			Modal.error({
				title: "Error during fetching",
				content: (
					<div>
						<p>Failed to fetch filters for column</p>
					</div>
				),
			});
		}

		this.setState({ fetchingFilters: false });
	}

	public render() {
		const { fuelTypes } = this.props.fuelTypeStore;
		const { fuelTypes: fuelTypeEnums } = this.props.meterStore;
		const paginationOptions = getTablePaginationOptions(fuelTypes?.totalCount);

		const columns: Array<ColumnProps<FuelTypeDto>> = [
			{
				title: L("FuelType"),
				dataIndex: "type",
				key: "type",
				width: 150,
				sorter: { multiple: 1 },
				sortOrder: this.state.sorters.find((x) => x.columnKey === "type")
					?.order,
				...this.getColumnSearchProps("type", L("FuelType"))
			},
			{
				title: `${L("FuelDensity")} (Kg/cbm)`,
				dataIndex: "densityValue",
				key: "densityValue",
				width: 200,
				sorter: { multiple: 2 },
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "densityValue",
				)?.order,
			},
			{
				title: `${L("FuelLcv")} (KJ/Kg)`,
				dataIndex: "lcv",
				key: "lcv",
				width: 200,
				sorter: { multiple: 3 },
				sortOrder: this.state.sorters.find((x) => x.columnKey === "lcv")?.order,
			},
			{
				title: `${L("CO₂eq Emissions")} (gCO₂eq/MJ)`,
				dataIndex: "co2Emissions",
				key: "co2Emissions",
				width: 200,
				sorter: { multiple: 4 },
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "co2Emissions",
				)?.order,
			},
			{
				title: `${L("Cf")} (gCO₂/g fuel)`,
				dataIndex: "co2Factor",
				key: "co2Factor",
				width: 200,
				sorter: { multiple: 5 },
				sortOrder: this.state.sorters.find((x) => x.columnKey === "co2Factor")
					?.order,
			},
			{
				title: `${L("Cf")} (gCH₄/g fuel)`,
				dataIndex: "ch4Factor",
				key: "ch4Factor",
				width: 200,
				sorter: { multiple: 6 },
				sortOrder: this.state.sorters.find((x) => x.columnKey === "ch4Factor")
					?.order,
			},
			{
				title: `${L("Cf")} (gN₂O/g fuel)`,
				dataIndex: "n2oFactor",
				key: "n2oFactor",
				width: 200,
				sorter: { multiple: 7 },
				sortOrder: this.state.sorters.find((x) => x.columnKey === "n2oFactor")
					?.order,
			},
			{
				title: `${L("Cslip")} (% of Fuel mass)`,
				dataIndex: "cslip",
				key: "cslip",
				width: 200,
				sorter: { multiple: 8 },
				sortOrder: this.state.sorters.find((x) => x.columnKey === "cslip")
					?.order,
			},
			{
				title: L("Actions"),
				fixed: "right" as const,
				width: 150,
				render: (text: string, item: FuelTypeDto) => (
					<div>
						<Dropdown
							trigger={["click"]}
							overlay={
								<Menu>
									{isGranted("Pages.FuelTypes-Edit") && (
										<Menu.Item
											onClick={() =>
												this.createOrUpdateModalOpen({ id: item.id })
											}
										>
											{L("Edit")}
										</Menu.Item>
									)}
									{isGranted("Pages.FuelTypes-Delete") && (
										<Menu.Item onClick={() => this.delete({ id: item.id })}>
											{L("Delete")}
										</Menu.Item>
									)}
								</Menu>
							}
							placement="bottomLeft"
						>
							<Button type="primary" icon={<SettingOutlined />}>
								{L("Actions")}
							</Button>
						</Dropdown>
					</div>
				),
			},
		];

		const hasNoActions =
			!isGranted("Pages.FuelTypes-Edit") &&
			!isGranted("Pages.FuelTypes-Delete");

		if (hasNoActions) {
			columns.pop();
		}

		return (
			<Card>
				<Row>
					<Col
						xs={{ span: 23, offset: 0 }}
						sm={{ span: 23, offset: 0 }}
						md={{ span: 23, offset: 0 }}
						lg={{ span: 23, offset: 0 }}
						xl={{ span: 23, offset: 0 }}
						xxl={{ span: 23, offset: 0 }}
						style={{ display: "flex", justifyContent: "end" }}
					>
						{isGranted("Pages.FuelTypes-Add") && (
							<Button
								type="primary"
								shape="circle"
								icon={<PlusOutlined />}
								style={{
									display: "flex",
									justifyContent: "center",
									alignItems: "center",
								}}
								onClick={() => this.createOrUpdateModalOpen({ id: 0 })}
							/>
						)}
					</Col>
				</Row>
				<Row>
					{/* <Col sm={{ span: 10, offset: 0 }}>
            <Search placeholder={this.L('Filter')} onSearch={this.handleSearch} />
          </Col> */}
				</Row>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey={(record: FuelTypeDto) => record.id.toString()}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={fuelTypes === undefined || this.state.loading}
							dataSource={fuelTypes === undefined ? [] : fuelTypes.items}
							onChange={this.handleTableChange}
							scroll={{ y: 850 }}
						/>
					</Col>
				</Row>

				<CreateOrUpdateFuelType
					wrappedComponentRef={this.saveFormRef}
					visible={this.state.modalVisible}
					onCancel={() =>
						this.setState({
							modalVisible: false,
						})
					}
					modalType={ModalType.edit}
					onCreate={this.handleCreate}
					fuelTypeEnums={fuelTypeEnums}
				/>
				<Chat />
			</Card>
		);
	}
}

export default FuelType;
