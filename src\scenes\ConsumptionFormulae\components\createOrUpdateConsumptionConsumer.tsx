import { Form } from "@ant-design/compatible";
import { CheckboxValueType } from "antd/lib/checkbox/Group";
import * as React from "react";
import ConsumptionConsumerStore from "../../../stores/consumptionConsumerStore";
import ShipStore from "../../../stores/shipStore";
import { ModalFormComponentProps } from "../../modalFormComponentProps";
import "@ant-design/compatible/assets/index.css";
import { Checkbox, Input, Modal, Select, Tabs } from "antd";
import FormItem from "antd/lib/form/FormItem";
import ComponentLayout from "../../../components/Layout/ComponentLayout";
import { L } from "../../../lib/abpUtility";
import MeterConfigurationStore from "../../../stores/meterConfigurationStore";
import { FormWidths } from "../../ViewSettingsConsts";
import rules from "./createOrUpdateConsumptionConsumer.validation";

const { Option } = Select;

export interface ICreateOrUpdateConsumptionConsumerProps
	extends ModalFormComponentProps {
	consumptionConsumerStore: ConsumptionConsumerStore;
	shipStore: ShipStore;
	meterConfigurationStore: MeterConfigurationStore;
}

export interface ICreateOrUpdateConsumptionConsumerState {
	shipName: string;
	shipId: number;
	metersMinus: CheckboxValueType[];
	metersPlus: CheckboxValueType[];
}

class CreateOrUpdateConsumptionConsumer extends React.Component<
	ICreateOrUpdateConsumptionConsumerProps,
	ICreateOrUpdateConsumptionConsumerState
> {
	state = {
		shipName: "",
		shipId: 0,
		metersMinus: [],
		metersPlus: [],
	};

	async componentDidMount() {
		await this.props.shipStore.getShipNames();
	}

	async componentDidUpdate(
		prevProps: ICreateOrUpdateConsumptionConsumerProps,
		prevState: ICreateOrUpdateConsumptionConsumerState,
	) {
		const allShips = this.props.shipStore.allShipNames;
		if (
			allShips?.[0] &&
			this.props.form.getFieldValue("shipId") <= 0 &&
			this.state.shipId === 0
		) {
			this.props.form.setFieldsValue({ shipId: allShips[0].id });
			this.setState({
				shipId: allShips[0].id,
			});
		}

		if (
			this.props.consumptionConsumerStore.updateMode &&
			(this.state.shipId === undefined || this.state.shipId <= 0) &&
			this.props.form.getFieldValue("shipId")
		) {
			this.setState({
				shipId: this.props.form.getFieldValue("shipId"),
			});
		}

		if (this.state.shipId <= 0) {
			return;
		}

		if (this.state.shipId !== prevState.shipId) {
			await this.props.meterConfigurationStore.metersForShip(
				this.state.shipId,
				"All Meter Types",
			);
			if (!(prevState.shipId <= 0 || prevState.shipId === undefined)) {
				this.props.form.setFieldsValue({ metersMinus: [], metersPlus: [] });
			}
			this.forceUpdate();
		}
	}

	checkShipName() {
		const { getFieldValue } = this.props.form;
		const shipName = getFieldValue("shipId");
		if (shipName != null) {
			this.setState(shipName);
		}
	}

	render() {
		const { getFieldDecorator } = this.props.form;
		const { visible, onCancel, onCreate, okButtonDisabled } = this.props;
		const allShipNames = this.props.shipStore.allShipNames;
		const initialValues =
			this.props.consumptionConsumerStore.editConsumptionConsumer;
		const allMetersForShip =
			this.props.meterConfigurationStore.allMetersForShip;

		const metersPlusOptions = allMetersForShip
			? allMetersForShip.filter((x) => {
					if (x.archived) return false;
					if (!this.props.form.getFieldValue("metersMinus")) return true;
					return !this.props.form.getFieldValue("metersMinus").includes(x.guid);
				})
			: [];

		const metersMinusOptions = allMetersForShip
			? allMetersForShip.filter((x) => {
					if (x.archived) return false;
					if (!this.props.form.getFieldValue("metersPlus")) return true;
					return !this.props.form.getFieldValue("metersPlus").includes(x.guid);
				})
			: [];

		return (
			<Modal
				visible={visible}
				cancelText={L("Cancel")}
				okText={L("OK")}
				onCancel={onCancel}
				onOk={onCreate}
				okButtonProps={{ loading: okButtonDisabled }}
				title={L("Setup consumption formula")}
			>
				<Form layout="vertical">
					<Tabs defaultActiveKey={"general"} size={"small"} tabBarGutter={64}>
						<Tabs.TabPane tab={L("General")} key="general">
							<Form.Item
								label={L("Name of formula")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("name", { rules: rules.formulaeName })(
									<Input style={{ width: FormWidths.wide }} />,
								)}
							</Form.Item>
							{allShipNames && allShipNames.length > 0 && (
								<Form.Item
									label={L("ShipName")}
									{...ComponentLayout.formItemLayout}
								>
									{getFieldDecorator("shipId", { rules: rules.shipName })(
										<Select
											allowClear
											showSearch
											style={{ width: "250px" }}
											onChange={(value) => {
												if (value) {
													const selectedShip = allShipNames.find(
														(ship) => ship.id === value,
													);
													if (selectedShip) {
														this.setState({
															shipId: selectedShip.id,
															shipName: selectedShip.shipName,
														});
													}
												} else {
													this.setState({ shipId: 0, shipName: "" });
												}
											}}
											filterOption={(input, option) => {
												const optionText = option?.props.children;
												return (
													typeof optionText === "string" &&
													optionText.toLowerCase().includes(input.toLowerCase())
												);
											}}
											placeholder={L("Select ship name")}
										>
											{allShipNames.map((x) => (
												<Option key={x.id} value={x.id}>
													{x.shipName}
												</Option>
											))}
										</Select>,
									)}
								</Form.Item>
							)}

							<Form.Item
								label={L("Meters +")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("metersPlus", { rules: rules.metersPlus })(
									<Select
										{...ComponentLayout.formItemLayout}
										mode="multiple"
										style={{ width: FormWidths.wide }}
										placeholder="Select meters"
										value={["d371ee0b-22a0-4b2c-bae4-3e0cc5e05456"]}
									>
										{metersPlusOptions.map((x) => (
											<Option key={x.guid} value={x.guid}>
												{x.name}
											</Option>
										))}
									</Select>,
								)}
							</Form.Item>
							<Form.Item
								label={L("Meters -")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("metersMinus", { rules: rules.metersMinus })(
									<Select
										{...ComponentLayout.formItemLayout}
										mode="multiple"
										style={{ width: FormWidths.wide }}
										placeholder="Select meters"
										onChange={(values) => {
											this.props.form.setFieldsValue({ metersMinus: values });
										}}
									>
										{metersMinusOptions.map((x) => (
											<Option key={x.guid} value={x.guid}>
												{x.name}
											</Option>
										))}
									</Select>,
								)}
							</Form.Item>
						</Tabs.TabPane>
						<Tabs.TabPane tab={L("Settings")} key={"setting"}>
							<FormItem
								label={L("IsActive")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("isActive", {
									initialValue: initialValues?.isActive,
									valuePropName: "checked",
								})(
									<Checkbox style={{ width: FormWidths.wide }}>
										{L("IsActive")}
									</Checkbox>,
								)}
							</FormItem>

							<FormItem
								label={L("IsAutoCalculation")}
								{...ComponentLayout.formItemLayout}
							>
								{getFieldDecorator("isAutoCalculation", {
									initialValue: initialValues?.isAutoCalculation,
									valuePropName: "checked",
								})(
									<Checkbox style={{ width: FormWidths.wide }}>
										{L("IsAutoCalculation")}
									</Checkbox>,
								)}
							</FormItem>
						</Tabs.TabPane>
					</Tabs>
				</Form>
			</Modal>
		);
	}
}

export default Form.create<ICreateOrUpdateConsumptionConsumerProps>()(
	CreateOrUpdateConsumptionConsumer,
);
