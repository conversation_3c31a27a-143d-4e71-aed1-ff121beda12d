import { action, observable } from "mobx";

import { EntityDto } from "../services/dto/entityDto";
import type { PagedResultDto } from "../services/dto/pagedResultDto";
import type { CreateOrUpdateMailRecipientInput } from "../services/mailRecipient/dto/createOrUpdateMailRecipientInput";
import { GetMailRecipientOutput } from "../services/mailRecipient/dto/getMailRecipientOutput";
import mailRecipientService from "../services/mailRecipient/mailRecipientService";
import type { PagedUserResultRequestDto } from "../services/user/dto/PagedUserResultRequestDto";
import { GetRoles } from "../services/user/dto/getRolesOuput";
import ShipStore from "./shipStore";

class MailRecipientStore {
	@observable mailRecipients!: PagedResultDto<GetMailRecipientOutput>;
	@observable editMailRecipient!: CreateOrUpdateMailRecipientInput;
	@observable mailLevel!: { [key: number]: string };
	@observable roles: GetRoles[] = [];

	shipStore: ShipStore = new ShipStore();

	@action
	async create(createMailRecipientInput: CreateOrUpdateMailRecipientInput) {
		const result = await mailRecipientService.create(createMailRecipientInput);
		this.mailRecipients.items.push(result);
	}

	@action
	async update(updateMailRecipientInput: CreateOrUpdateMailRecipientInput) {
		const result = await mailRecipientService.update(updateMailRecipientInput);
		this.mailRecipients.items = this.mailRecipients.items.map(
			(x: GetMailRecipientOutput) => {
				if (x.id === updateMailRecipientInput.id) x = result;
				return x;
			},
		);
	}

	@action
	async delete(entityDto: EntityDto) {
		await mailRecipientService.delete(entityDto);
		this.mailRecipients.items = this.mailRecipients.items.filter(
			(x: GetMailRecipientOutput) => x.id !== entityDto.id,
		);
	}

	@action
	async get(entityDto: EntityDto) {
		const result = await mailRecipientService.get(entityDto);
		this.editMailRecipient = result;
	}

	@action
	async createMailRecipient() {
		this.editMailRecipient = {
			name: "",
			email: "",
			mailLevel: "",
			id: 0,
			shipId: 0,
		};
	}

	@action
	async getAll(pagedFilterAndSortedRequest: PagedUserResultRequestDto) {
		const result = await mailRecipientService.getAll(
			pagedFilterAndSortedRequest,
		);
		this.mailRecipients = result;
	}
}

export default MailRecipientStore;
