import { Modal } from "antd";
import axios from "axios";
import { L } from "../lib/abpUtility";
import AppConsts from "./../lib/appconst";

const qs = require("qs");

declare let abp: any;

const httpCsv = axios.create({
	baseURL: AppConsts.remoteServiceBaseUrl,
	timeout: 30000,
	responseType: "blob",
	paramsSerializer: (params) =>
		qs.stringify(params, {
			encode: false,
		}),
});

httpCsv.interceptors.request.use(
	(config) => {
		if (abp.auth.getToken()) {
			config.headers.common.Authorization = `Bearer ${abp.auth.getToken()}`;
		}

		config.headers.common[".AspNetCore.Culture"] = abp.utils.getCookieValue(
			"Abp.Localization.CultureName",
		);
		config.headers.common["Abp.TenantId"] =
			abp.multiTenancy.getTenantIdCookie();

		return config;
	},
	(error) => Promise.reject(error),
);

httpCsv.interceptors.response.use(
	(response) => {
		const url = window.URL.createObjectURL(new Blob([response.data]));
		const link = document.createElement("a");
		link.href = url;
		const fileUrl: string = response.config.url ?? "";
		if (fileUrl.includes("ConsumptionResult")) {
			link.setAttribute("download", "ConsumptionResults.csv");
		} else if (fileUrl.includes("Draft")) {
			link.setAttribute("download", "Drafts.csv");
		} else if (fileUrl.includes("MeterReading")) {
			link.setAttribute("download", "MeterReadings.csv");
		} else {
			link.setAttribute("download", "file.csv");
		}
		document.body.appendChild(link);
		link.click();
		return response;
	},
	(error) => {
		if (
			!!error.response &&
			!!error.response.data.error &&
			!!error.response.data.error.message &&
			error.response.data.error.details
		) {
			Modal.error({
				title: error.response.data.error.message,
				content: error.response.data.error.details,
			});
		} else if (
			!!error.response &&
			!!error.response.data.error &&
			!!error.response.data.error.message
		) {
			Modal.error({
				title: L("LoginFailed"),
				content: error.response.data.error.message,
			});
		} else if (!error.response) {
			Modal.error({ content: L("UnknownError") });
		}

		setTimeout(() => {}, 1000);

		return Promise.reject(error);
	},
);

export default httpCsv;
