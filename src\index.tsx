import "./index.less";

import * as moment from "moment";
import * as React from "react";
import * as ReactDOM from "react-dom";

import { Provider } from "mobx-react";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import App from "./App";
import registerServiceWorker from "./registerServiceWorker";
import abpUserConfigurationService from "./services/abpUserConfigurationService";
import initializeStores from "./stores/storeInitializer";
import Utils from "./utils/utils";

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
declare let abp: any;

Utils.setLocalization();

abpUserConfigurationService.getAll().then((data) => {
	Utils.extend(true, abp, data.data.result);
	abp.clock.provider = Utils.getCurrentClockProvider(
		data.data.result.clock.provider,
	);

	moment.locale(abp.localization.currentLanguage.name);

	if (abp.clock.provider.supportsMultipleTimezone) {
		moment.tz.setDefault(abp.timing.timeZoneInfo.iana.timeZoneId);
	}

	const stores = initializeStores();

	ReactDOM.render(
		<Provider {...stores}>
			<BrowserRouter>
				<App />
			</BrowserRouter>
		</Provider>,
		document.getElementById("root") as HTMLElement,
	);

	registerServiceWorker();
});
