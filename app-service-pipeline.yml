# Docker
# Build and push an image to Azure Container Registry
# https://docs.microsoft.com/azure/devops/pipelines/languages/docker

trigger:
  branches:
      include:
        - master
        - release/*
        - develop

resources:
  - repo: self

stages:
- stage: Build
  displayName: Build
  jobs:  
  - job: Build
    displayName: Build
    steps:
      - checkout: self
      - task: NodeTool@0
        inputs:
          versionSource: 'spec'
          versionSpec: '18.19.x'
      - task: CmdLine@2
        inputs:
          script: 'npm install --force'
      - task: DownloadSecureFile@1
        inputs:
          secureFile: 'env-config.js'
      - task: CopyFiles@2
        inputs:
          SourceFolder: '$(Agent.TempDirectory)'
          Contents: 'env-config.js'
          TargetFolder: '$(Build.SourcesDirectory)/public'
          OverWrite: true
      - task: Npm@1
        inputs:
          command: 'custom'
          workingDir: '$(Build.SourcesDirectory)'
          customCommand: 'run build --openssl-legacy-provider'  
      - task: ArchiveFiles@2
        displayName: 'Archive build'
        inputs:
          rootFolderOrFile: 'build'
          includeRootFolder: false
          archiveType: 'zip'
          archiveFile: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'
          replaceExistingArchive: true
      - task: PublishBuildArtifacts@1
        inputs:
          PathtoPublish: '$(Build.ArtifactStagingDirectory)'
          ArtifactName: 'drop'
          publishLocation: 'Container'