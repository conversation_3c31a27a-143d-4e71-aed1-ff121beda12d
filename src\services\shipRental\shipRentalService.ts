import { EntityDto } from "../dto/entityDto";
import { PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoints";
import http from "../httpService";
import { CreateOrUpdateShipRentalInput } from "./dto/CreateOrUpdateShipRentalInput";
import { GetRentalGroupByCounterparty } from "./dto/GetRentalGroupByCounterparty";
import { PagedFilterAndSortedRequestShipRental } from "./dto/PagedFilterAndSortedRequestShipRental";
import { UpdateShipRentalInput } from "./dto/UpadteShipRentalInput";

class ShipRentalService {
	public async create(createShipRentalInput: CreateOrUpdateShipRentalInput) {
		const result = await http.post(
			Endpoint.ShipRental.Create,
			createShipRentalInput,
		);
		return result.data.result;
	}

	public async update(updateShipRentalInput: UpdateShipRentalInput) {
		const result = await http.put(
			Endpoint.ShipRental.Update,
			updateShipRentalInput,
		);
		return result.data.result;
	}

	public async delete(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.ShipRental.Delete, {
			params: entityDto,
		});
		return result.data;
	}

	public async get(
		entityDto: EntityDto,
	): Promise<CreateOrUpdateShipRentalInput> {
		const result = await http.get(Endpoint.ShipRental.Get, {
			params: entityDto,
		});
		return result.data.result;
	}

	public async getAll(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequestShipRental,
	): Promise<PagedResultDto<CreateOrUpdateShipRentalInput>> {
		const result = await http.get(Endpoint.ShipRental.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}

	public async getRentalGroupyByCounterparty(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequestShipRental,
	): Promise<PagedResultDto<GetRentalGroupByCounterparty>> {
		const result = await http.get(
			Endpoint.ShipRental.GetRentalGroupyByCounterparty,
			{ params: pagedFilterAndSortedRequest },
		);
		return result.data.result;
	}

	public async getFilters(
		pagedFilterAndSortedRequest: { keyword: string; searchColumn: string },
		property: string,
	) {
		const result = await http.get(Endpoint.ShipRental.Filters, {
			params: { ...pagedFilterAndSortedRequest, propertyName: property },
		});
		return result.data.result;
	}
}

export default new ShipRentalService();
