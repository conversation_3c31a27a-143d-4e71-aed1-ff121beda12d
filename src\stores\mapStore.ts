import { action, observable } from "mobx";
import { ShipPositionDto } from "../services/map/dto/ShipPositionDto";
import mapService from "../services/map/mapService";
import { ShipTraceCammelCase } from "../services/notifications/dto/ShipTrace";

class MapStore {
	@observable ShipLastPositions!: ShipPositionDto[];
	@observable ShipLastPosition!: ShipTraceCammelCase[];

	@action
	async getShipLastPositions() {
		const result = await mapService.getShipPositions();
		this.ShipLastPositions = result;
	}

	@action
	async getShipLastPosition(shipId: number) {
		const result = await mapService.getShipPosition(shipId);
		this.ShipLastPosition = result;
	}
}

export default MapStore;
