import { Card, Col, Row, Table, TablePaginationConfig } from "antd";
import { inject, observer } from "mobx-react";
import * as React from "react";
import AppComponentBase from "../../components/AppComponentBase";
import { L } from "../../lib/abpUtility";
import { PagedUserResultRequestDto } from "../../services/user/dto/PagedUserResultRequestDto";
import IntegrationStore from "../../stores/integrationStore";
import Stores from "../../stores/storeIdentifier";
import { getTablePaginationOptions, renderCheckboxValue } from "../renderUtils";

export interface IIntegrationsProps {
	integrationStore: IntegrationStore;
}

export interface IIntegrationsState {
	modalVisible: boolean;
	maxResultCount: number;
	skipCount: number;
}

@inject(Stores.IntegrationStore)
@observer
class Integrations extends AppComponentBase<
	IIntegrationsProps,
	IIntegrationsState
> {
	formRef: any;

	state: IIntegrationsState = {
		modalVisible: false,
		maxResultCount: 10,
		skipCount: 0,
	};

	async componentDidMount() {
		await this.getAll();
	}

	async getAll() {
		await this.props.integrationStore.getAll({} as PagedUserResultRequestDto);
	}

	handleTableChange = (pagination: TablePaginationConfig) => {
		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
			},
			async () => await this.getAll(),
		);
	};

	public render() {
		const { integrations } = this.props.integrationStore ?? [];
		const paginationOptions = getTablePaginationOptions(
			integrations?.totalCount,
		);

		const columns = [
			{
				title: L("IntegrationIntegratorName"),
				dataIndex: "integratorName",
				key: "integratorName",
				width: 150,
			},
			{
				title: L("IntegrationDataType"),
				dataIndex: "dataType",
				key: "dataType",
				width: 150,
			},
			{
				title: L("IntegrationActive"),
				dataIndex: "active",
				key: "active",
				width: 150,
				render: renderCheckboxValue,
			},
		];

		return (
			<Card>
				<Row>
					<Col
						xs={{ span: 4, offset: 0 }}
						sm={{ span: 4, offset: 0 }}
						md={{ span: 4, offset: 0 }}
						lg={{ span: 2, offset: 0 }}
						xl={{ span: 2, offset: 0 }}
						xxl={{ span: 2, offset: 0 }}
					/>
					<Col
						xs={{ span: 14, offset: 0 }}
						sm={{ span: 15, offset: 0 }}
						md={{ span: 15, offset: 0 }}
						lg={{ span: 1, offset: 21 }}
						xl={{ span: 1, offset: 21 }}
						xxl={{ span: 1, offset: 21 }}
					/>
				</Row>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey={(record) => record.id.toString()}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={integrations  === undefined}
							dataSource={integrations === undefined ? [] : integrations.items}
						/>
					</Col>
				</Row>
			</Card>
		);
	}
}

export default Integrations;
