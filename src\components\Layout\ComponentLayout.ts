// biome-ignore lint/complexity/noStaticOnlyClass: <explanation>
class ComponentLayout {
	static formItemLayout = {
		labelCol: {
			xs: { span: 11 },
			sm: { span: 11 },
			md: { span: 11 },
			lg: { span: 11 },
			xl: { span: 11 },
			xxl: { span: 11 },
		},
		wrapperCol: {
			xs: { span: 12 },
			sm: { span: 12 },
			md: { span: 12 },
			lg: { span: 12 },
			xl: { span: 12 },
			xxl: { span: 12 },
		},
	};

	static tailFormItemLayout = {
		labelCol: {
			xs: { span: 6 },
			sm: { span: 6 },
			md: { span: 6 },
			lg: { span: 6 },
			xl: { span: 6 },
			xxl: { span: 6 },
		},
		wrapperCol: {
			xs: { span: 18 },
			sm: { span: 18 },
			md: { span: 18 },
			lg: { span: 18 },
			xl: { span: 18 },
			xxl: { span: 18 },
		},
	};

	static roleFormItemLayout = {
		labelCol: {
			xs: { span: 6 },
			sm: { span: 6 },
			md: { span: 6 },
			lg: { span: 6 },
			xl: { span: 6 },
			xxl: { span: 6 },
		},
		wrapperCol: {
			xs: { span: 24 },
			sm: { span: 24 },
			md: { span: 24 },
			lg: { span: 24 },
			xl: { span: 24 },
			xxl: { span: 24 },
		},
	};
}

export default ComponentLayout;
