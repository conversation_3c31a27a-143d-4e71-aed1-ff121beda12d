import { L } from "../../../lib/abpUtility";

const secretPinCheck = (rule: any, value: string, callback: any) => {
	if (value.length > 0) {
		const re = /^[0-9\b]+$/;
		if (value.length !== 4 || !re.test(value)) {
			return callback(L("PleaseInputSecretPin"));
		}
	}
	return callback();
};

export const rules = {
	voyageNumber: [{ required: true, message: L("PleaseInputVoyageNumber") }],
	destinationFrom: [{ required: true, message: L("PleaseInputCityDeparture") }],
	destinationTo: [{ required: true, message: L("PleaseInputCityDestination") }],
	shipId: [{ required: true, message: L("PleaseSelectShip") }],
	secretPin: [
		{ required: true, message: L("PleaseInputSecretPin") },
		{ validator: secretPinCheck },
	],
	planedStart: [
		{
			required: true,
			message: L("PleaseInputDate"),
			type: "object",
			whitespace: true,
		},
	],
	start: (required: boolean) => [
		{
			required: required,
			message: L("PleaseInputDate"),
			type: "object",
			whitespace: true,
		},
	],
	planedEnd: [
		{
			required: true,
			message: L("PleaseInputDate"),
			type: "object",
			whitespace: true,
		},
	],
	end: [
		{
			required: false,
			message: L("PleaseInputDate"),
			type: "object",
			whitespace: true,
		},
	],
};

export default rules;
