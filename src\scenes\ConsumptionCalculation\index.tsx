import "./index.less";

import { Form } from "@ant-design/compatible";
import * as React from "react";
import "@ant-design/compatible/assets/index.css";
import {
	<PERSON><PERSON>,
	Card,
	Col,
	DatePicker,
	InputNumber,
	Row,
	Spin,
	Table,
	TablePaginationConfig,
} from "antd";
import FormItem from "antd/lib/form/FormItem";
import { inject, observer } from "mobx-react";
import moment, { Moment } from "moment";
import AppComponentBase from "../../components/AppComponentBase";
import ComponentLayout from "../../components/Layout/ComponentLayout";
import TrustDropdown from "../../components/TrustDropdown";
import { L } from "../../lib/abpUtility";
import consumptionCalculationService from "../../services/consumptionCalculation/consumptionCalculationService";
import { ConsumptionCalculationDto } from "../../services/consumptionCalculation/dto/calculationResultAutoInput";
import { ConsumptionResultDto } from "../../services/consumptionResult/dto/consumptionResultDto";
import ConsumptionCalculationStore from "../../stores/consumptionCalculationStore";
import ConsumptionConsumerStore from "../../stores/consumptionConsumerStore";
import MeterReadingStore from "../../stores/meterReadingStore";
import Stores from "../../stores/storeIdentifier";
import VoyageStore from "../../stores/voyageStore";
import { ModalFormComponentProps } from "../modalFormComponentProps";
import {
	getTablePaginationOptions,
} from "../renderUtils";
import rules from "./index.validation";
import { TableRowSelection } from "antd/lib/table/interface";

export interface IConsumptionCalculationProps extends ModalFormComponentProps {
	meterReadingStore: MeterReadingStore;
	voyageStore: VoyageStore;
	consumptionConsumerStore: ConsumptionConsumerStore;
	consumptionCalculationStore: ConsumptionCalculationStore;
}

export interface IConsumptionCalculationState {
	modalVisible: boolean;
	maxResultCount: number;
	skipCount: number;
	filter: string;
	sending: boolean;
	selectedRows: Array<ConsumptionCalculationDto>;
	calculationResults: Array<ConsumptionCalculationDto>;
	calculateDisabled: boolean;
}

const { RangePicker } = DatePicker;

interface ConsumptionCalulationFormProps {
	range: [Moment, Moment] | FormErrorProps;
	voyageId: number;
	groupingPeriodInMinutes: number | FormErrorProps;
}

interface FormErrorProps {
	errors: Array<{ field: string; message: string }>;
}

@inject(Stores.MeterReadingStore)
@inject(Stores.VoyageStore)
@inject(Stores.ConsumptionConsumerStore)
@inject(Stores.ConsumptionCalculationStore)
@observer
class ConsumptionCalculation extends AppComponentBase<
	IConsumptionCalculationProps,
	IConsumptionCalculationState
> {
	formRef: any;

	state = {
		modalVisible: false,
		maxResultCount: 10,
		skipCount: 0,
		filter: "",
		sending: false,
		selectedRows: [],
		calculationResults: [],
		calculateDisabled: false,
	};

	async componentDidMount() {
		await this.getAll();
		await this.props.consumptionCalculationStore.getConfig();
	}

	async getAll() {
		await this.props.voyageStore.getAllWithIdAndNumber();
	}

	handleTableChange = (pagination: TablePaginationConfig) => {
		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
			},
			async () => await this.getAll(),
		);
	};

	Modal = () => {
		this.setState({
			modalVisible: !this.state.modalVisible,
		});
	};

	saveFormRef = (formRef: any) => {
		this.formRef = formRef;
	};

	prepareData = (): Array<ConsumptionResultDto> => {
		const { consumptionCalculations } = this.props.consumptionCalculationStore;
		return (
			consumptionCalculations?.items?.map((x) => x.consumptionResult) ?? []
		);
	};

	onGroupingPeriodInMinutes = (e: number | undefined) => {
		this.props.form.validateFields(
			async (
				errors: ConsumptionCalulationFormProps,
				values: ConsumptionCalulationFormProps,
			) => {
				if (typeof e === "number") {
					this.props.consumptionCalculationStore.setGroupingPeriod(e);
				}
			},
		);
	};

	saveConsumptionResults = async () => {
		console.log("this.state.selectedRows");
		console.log(this.state.selectedRows);
		const voyageId: number = this.props.form.getFieldValue("voyageId");
		await consumptionCalculationService.saveAutoCalculationResults({
			items: this.state.selectedRows,
			voyageId: voyageId,
		});
		this.setState({
			selectedRows: [],
		});
		this.props.consumptionCalculationStore.consumptionCalculations = {
			voyageId: voyageId,
			items: [],
		};
	};

	public render() {
		const dataSource: Array<ConsumptionResultDto> = this.prepareData();

		const columns = [
			{
				title: L("Consumer"),
				dataIndex: "consumer",
				key: "consumer",
				sorter: (a: ConsumptionResultDto, b: ConsumptionResultDto) =>
					a.consumer?.length - b.consumer?.length,
				width: 150,
			},
			{
				title: L("FirstReadingDate"),
				dataIndex: "firstReadingDate",
				key: "firstReadingDate",
				sorter: (a: ConsumptionResultDto, b: ConsumptionResultDto) =>
					new Date(a.firstReadingDate).getTime() -
					new Date(b.firstReadingDate).getTime(),
				width: 150,
			},
			{
				title: L("LastReadingDate"),
				dataIndex: "lastReadingDate",
				key: "lastReadingDate",
				sorter: (a: ConsumptionResultDto, b: ConsumptionResultDto) =>
					new Date(a.lastReadingDate).getTime() -
					new Date(b.lastReadingDate).getTime(),
				width: 150,
			},
			{
				title: L("Period"),
				dataIndex: "period",
				key: "period",
				width: 150,
				sorter: (a: ConsumptionResultDto, b: ConsumptionResultDto) =>
					a.period.localeCompare(b.period, undefined, {
						numeric: true,
						sensitivity: "base",
					}),
			},
			{
				title: L("ConsumptionTotalUnit"),
				dataIndex: "consumptionTotal",
				key: "consumptionTotal",
				sorter: (a: ConsumptionResultDto, b: ConsumptionResultDto) =>
					a.consumptionTotal?.length - b.consumptionTotal?.length,
				width: 150,
			},
			{
				title: L("ConsumptionPerHourUnit"),
				dataIndex: "consumptionPerHour",
				key: "consumptionPerHour",
				sorter: (a: ConsumptionResultDto, b: ConsumptionResultDto) =>
					a.consumptionPerHour?.length - b.consumptionPerHour?.length,
				width: 150,
			},
			{
				title: L("Consumption24hUnit"),
				dataIndex: "consumption24h",
				key: "consumption24h",
				sorter: (a: ConsumptionResultDto, b: ConsumptionResultDto) =>
					a.consumption24h?.length - b.consumption24h?.length,
				width: 150,
			},
			{
				title: L("Density"),
				dataIndex: "density",
				key: "density",
				sorter: (a: ConsumptionResultDto, b: ConsumptionResultDto) =>
					a.density?.length - b.density?.length,
				width: 150,
			},
		];

		const { selectedRows } = this.state;

		const rowSelection: TableRowSelection<ConsumptionResultDto> = {
			onChange: (
				selectedRowKeys,
				selectedRows: ConsumptionResultDto[],
			) => {
				console.log(
					`selectedRowKeys: ${selectedRowKeys}`,
					"selectedRows: ",
					selectedRows,
				);
				const selectedConsumptionCalculations =
					this.props.consumptionCalculationStore.consumptionCalculations?.items?.filter(
						(x) =>
							(selectedRowKeys as string[]).includes(x.calculationResult.uuid),
					);
				this.setState({
					selectedRows: selectedConsumptionCalculations,
				});
			},
			selectedRowKeys: (selectedRows as ConsumptionCalculationDto[]).map(
				(x) => x.consumptionResult.uuid,
			),
		};

		const { allVoyages } = this.props.voyageStore;
		const paginationOptions = getTablePaginationOptions(
			allVoyages?.length ?? 0,
		);

		const dataVoyages: string[] = allVoyages?.map(
			(voyage) => voyage.voyageNumber,
		);
		const customDataVoyages: Map<number, string> = new Map<number, string>(
			allVoyages?.map((voyage) => [voyage.id, `${voyage.voyageNumber}`]),
		);
		// var voyageInitialValue: number = allVoyages ? allVoyages[0]?.id : null;

		const { getFieldDecorator } = this.props.form;

		const { calculateDisabled } = this.state;
		const { config } = this.props.consumptionCalculationStore;

		return (
			<Card className="consumption-results">
				<Row>
					<Col
						xs={{ span: 10, offset: 0 }}
						sm={{ span: 10, offset: 0 }}
						md={{ span: 10, offset: 0 }}
						lg={{ span: 5, offset: 0 }}
						xl={{ span: 5, offset: 0 }}
						xxl={{ span: 5, offset: 0 }}
					/>
					<Col
						xs={{ span: 14, offset: 0 }}
						sm={{ span: 15, offset: 0 }}
						md={{ span: 15, offset: 0 }}
						lg={{ span: 1, offset: 21 }}
						xl={{ span: 1, offset: 21 }}
						xxl={{ span: 1, offset: 21 }}
					/>
				</Row>
				<Row>
					<Col
						xs={{ span: 14, offset: 0 }}
						sm={{ span: 15, offset: 0 }}
						md={{ span: 15, offset: 0 }}
						lg={{ span: 1, offset: 21 }}
						xl={{ span: 1, offset: 21 }}
						xxl={{ span: 1, offset: 21 }}
					/>
				</Row>
				<Row style={{ marginTop: 10 }}>
					<div className="consumption-calculation-results-header__buttons">
						<FormItem
							{...ComponentLayout.formItemLayout}
							label={L("Grouping Period In Minutes")}
						>
							{getFieldDecorator("groupingPeriodInMinutes", {
								initialValue: config?.groupingPeriodInMinutes,
								rules: rules.groupingPeriodInMinutes,
							})(<InputNumber<number> onChange={(e) => this.onGroupingPeriodInMinutes(e || undefined)} />)}
						</FormItem>
						<FormItem label={L("Voyage")} {...ComponentLayout.formItemLayout}>
							<TrustDropdown
								data={dataVoyages}
								customData={customDataVoyages}
								placeholder={L("Voyage")}
								formRef={this.props.form}
								fieldDecoratorKey={"voyageId"}
								rules={rules.voaygeId}
								initialValue={allVoyages ? allVoyages[0].id : null}
							/>
						</FormItem>
						<FormItem {...ComponentLayout.formItemLayout} label={L("RangeUTC")}>
							{getFieldDecorator("range", {
								initialValue: [moment().add(-3, "days"), moment()],
								rules: rules.to,
							})(
								<RangePicker
									showTime={{ format: "HH:mm" }}
									format="YYYY-MM-DD HH:mm"
									placeholder={["From UTC Time", "To UTC Time"]}
								/>,
							)}
						</FormItem>
					</div>
				</Row>
				<Row>
					{this.props.consumptionCalculationStore.consumptionCalculations?.items
						?.length > 0 && (
						<Button
							type="primary"
							disabled={this.state.selectedRows.length === 0}
							onClick={() => this.saveConsumptionResults()}
						>
							{L("Save Selected Consumption Results")}
						</Button>
					)}
				</Row>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey={(record) => record.uuid}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={dataSource  === undefined}
							dataSource={dataSource}
							onChange={this.handleTableChange}
							rowSelection={rowSelection}
							// scroll={{ x: true }}
						/>
					</Col>
				</Row>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Button
							type="primary"
							disabled={calculateDisabled}
						>
							{L("Start calc")}
						</Button>
						{calculateDisabled && <Spin />}
					</Col>
				</Row>
			</Card>
		);
	}
}

export default Form.create<IConsumptionCalculationProps>()(
	ConsumptionCalculation,
);
