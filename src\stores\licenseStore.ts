import { action, computed, observable } from "mobx";
import { EntityDto } from "../services/dto/entityDto";
import type { PagedFilterAndSortedRequest } from "../services/dto/pagedFilterAndSortedRequest";
import type { PagedResultDto } from "../services/dto/pagedResultDto";
import type { GetLicenseOutput } from "../services/license/dto/getLicenseOutput";
import type {
	LicenceTenantDto,
	ManyLicenceTenantDto,
} from "../services/license/dto/licenceTenantDto";
import licenseService from "../services/license/licenseService";

class LicenseStore {
	@observable licenses!: PagedResultDto<GetLicenseOutput>;
	@observable tenantLicenses!: PagedResultDto<GetLicenseOutput>;
	@observable licenseModel!: GetLicenseOutput;

	@computed get getFreeLicenses() {
		return this.licenses?.items.filter(
			(x) => !this.tenantLicenses?.items.map((y) => y.key).includes(x.key),
		);
	}

	@action
	async create(license: GetLicenseOutput) {
		const result = await licenseService.create(license);
		this.licenses = result;
	}

	@action
	async createLicence() {
		this.licenseModel = {
			key: "",
			description: "",
			expirationDate: "",
			id: 0,
		};
	}

	@action
	async getAll(pagedFilterAndSortedRequest: PagedFilterAndSortedRequest) {
		const result = await licenseService.getAll(pagedFilterAndSortedRequest);
		this.licenses = result;
	}

	@action
	async get(entityDto: EntityDto) {
		const result = await licenseService.get(entityDto);
		this.licenseModel = result;
	}

	@action
	async delete(entityDto: EntityDto) {
		await licenseService.delete(entityDto);
		this.licenses.items = this.licenses.items.filter(
			(x: GetLicenseOutput) => x.id !== entityDto.id,
		);
	}

	@action
	async update(licence: GetLicenseOutput) {
		const result = await licenseService.update(licence);
		this.licenses.items = this.licenses.items.map((x: GetLicenseOutput) => {
			if (x.id === licence.id) x = result;
			return x;
		});
	}

	@action
	async getAssignedLicences(entityDto: EntityDto) {
		const result = await licenseService.getAssignedLicences(entityDto);
		this.tenantLicenses = result;
	}

	@action
	async assignLicence(manyLicenceTenant: ManyLicenceTenantDto) {
		await licenseService.assignLicence(manyLicenceTenant);
	}

	@action
	async unassignLicence(licenceTenant: LicenceTenantDto) {
		await licenseService.unassignLicence(licenceTenant);
	}
}

export default LicenseStore;
