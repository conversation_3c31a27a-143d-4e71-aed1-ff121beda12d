import { AuditedEntityDto } from "../../dto/auditedEntityDto";

export interface GetAllMeterOutput extends AuditedEntityDto {
	barcode: string;
	name: string;
	meterType: number;
	digitsCount: number;
	decimalPoint: number;
	measureType: number;
	measurementFrequencyPerDay: number;
	shipName: string;
	callSign: string;
	consumerType: number;
	additionalScanHours: string[];
	controlImageUrl: string;
}
