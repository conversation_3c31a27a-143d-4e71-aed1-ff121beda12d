import { Layout } from "antd";
import React from "react";
import { AzureMapsProvider } from "react-azure-maps";
import { ShipPositionDto } from "../../../services/map/dto/ShipPositionDto";
import Map from "./map";

export interface MapWrapperProps {
	ShipLastPositions: ShipPositionDto[];
}

const MapWrapper: React.FC<MapWrapperProps> = ({ ShipLastPositions }) => {
	const { Content } = Layout;

	const layoutStyle: React.CSSProperties = {
		width: "100%",
		height: "100%",
		display: "flex",
	};

	const contentStyle: React.CSSProperties = {
		flex: 1,
	};

	return (
		<Layout style={layoutStyle}>
			<Content style={contentStyle}>
				<AzureMapsProvider>
					<Map ShipLastPositions={ShipLastPositions} />
				</AzureMapsProvider>
			</Content>
		</Layout>
	);
};

export default MapWrapper;
