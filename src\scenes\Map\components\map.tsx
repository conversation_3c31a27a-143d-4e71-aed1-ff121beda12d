import {
	AuthenticationType,
	HtmlMarker,
	data,
	layer,
	source,
} from "azure-maps-control";
import React, { useContext, useEffect, useMemo, useState } from "react";
import {
	AzureMap,
	AzureMapPopup,
	AzureMapsContext,
	IAzureMapOptions,
	IAzureMapsContextProps,
} from "react-azure-maps";
import AppConsts from "../../../lib/appconst";
import { ShipPositionDto } from "../../../services/map/dto/ShipPositionDto";
import mapService from "../../../services/map/mapService";
import { getSpeedColor } from "../../Notifications/components/notificationMapWrapper";
import { azureMapControls } from "../common/azureMapControls";
import MapShipPopup from "./mapShipPopUp";

export interface MapProps {
	ShipLastPositions: ShipPositionDto[];
}

function getColor(fdate: Date): string {
	const currentTime = new Date();
	const diffInHours =
		(currentTime.getTime() - fdate.getTime()) / (1000 * 60 * 60);
	switch (true) {
		case diffInHours < 13:
			return "#03C04A";
		case diffInHours < 48:
			return "#FFA500";
		default:
			return "#FF0000";
	}
}

function getShipIcon(heading: Date): string {
	const color = getColor(new Date(heading));
	return `<svg width="31" height="27" viewBox="0 0 124 108" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M61.9208 0L92.8812 53.625C106.641 77.4583 89.4412 107.25 61.9208 107.25C34.4005 107.25 17.2002 77.4584 30.9604 53.625L61.9208 0Z" fill="#232323"/>
    <circle cx="62" cy="73" r="20" fill="${color}"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M56.1937 61.4314C56.1937 60.8399 56.6733 60.3603 57.2649 60.3603H66.5482C67.1398 60.3603 67.6194 60.8399 67.6194 61.4314V64.6449H70.8328C71.4244 64.6449 71.904 65.1245 71.904 65.7161V72.4305L74.2888 73.2814C74.5762 73.384 74.8062 73.6043 74.9211 73.887C75.036 74.1697 75.0249 74.488 74.8905 74.762L71.7268 81.2115H71.9716C72.5239 81.2115 72.9716 81.6593 72.9716 82.2115C72.9716 82.7638 72.5239 83.2115 71.9716 83.2115H51.9768C51.4245 83.2115 50.9768 82.7638 50.9768 82.2115C50.9768 81.6593 51.4245 81.2115 51.9768 81.2115H52.2299L49.2203 74.7421C49.0939 74.4703 49.0871 74.1579 49.2017 73.8809C49.3163 73.6039 49.5417 73.3875 49.8232 73.2845L51.9091 72.5207V65.7161C51.9091 65.1245 52.3887 64.6449 52.9803 64.6449H56.1937V61.4314ZM54.5926 81.2115H69.3406L72.4387 74.8959L62.7621 71.4433C62.2122 71.2471 61.611 71.2496 61.0628 71.4503L51.6544 74.8954L54.5926 81.2115ZM58.336 64.6449H65.477V62.5026H58.336V64.6449ZM69.7617 71.6662V66.7872H54.0514V71.7362L60.3262 69.4386C61.3443 69.0658 62.4608 69.0612 63.482 69.4256L69.7617 71.6662Z" fill="#3F3F3F"/>
    </svg>
    `;
}

const Map: React.FC<MapProps> = ({ ShipLastPositions }) => {
	const { mapRef, isMapReady } =
		useContext<IAzureMapsContextProps>(AzureMapsContext);
	const [isVisible, setIsVisible] = useState(false);
	const [popupPosition, setPopupPosition] = useState<data.Position>(
		new data.Position(0, 0),
	);
	const [popupContent, setPopupContent] = useState<ShipPositionDto>();

	const option: IAzureMapOptions = {
		authOptions: {
			authType: AuthenticationType.subscriptionKey,
			subscriptionKey: AppConsts.azureMapSubscriptionKey,
		},
		center: [0, 0],
		zoom: 4,
		language: "en-US",
	};

	useEffect(() => {
		if (isMapReady && mapRef) {
			ShipLastPositions.forEach((shipPosition: ShipPositionDto) => {
				const outerDiv = document.createElement("div");

				const newDiv = document.createElement("div");
				newDiv.innerHTML = getShipIcon(shipPosition.timestamp);
				newDiv.style.transform = `rotate(${shipPosition.heading}deg)`;
				newDiv.style.width = "31px";
				newDiv.style.maxHeight = "27px";

				outerDiv.appendChild(newDiv);

				const svgLayer = new HtmlMarker({
					htmlContent: outerDiv,
					position: [shipPosition.longitude, shipPosition.latitude],
					anchor: "center",
				});

				mapRef.events.add("click", svgLayer, async () => {
					setPopupContent(shipPosition);
					setPopupPosition(
						new data.Position(shipPosition.longitude, shipPosition.latitude),
					);
					setIsVisible(true);

					mapService.getShipPosition(shipPosition.shipId).then((shipTraces) => {
						const features: any[] = [];

						for (let i = 1; i < shipTraces.length; i++) {
							const currentPoint = shipTraces[i];
							const previousPoint = shipTraces[i - 1];

							const coordinates: [number, number][] = [
								[previousPoint.longitude, previousPoint.latitude],
								[currentPoint.longitude, currentPoint.latitude],
							];

							const lineString: any = {
								type: "LineString",

								coordinates: coordinates,
							};

							const feature: any = {
								type: "Feature",
								properties: {
									myColor: getSpeedColor(currentPoint.sog),
								},
								geometry: lineString,
								id: i,
							};

							features.push(feature);
						}

						const layerData = {
							type: "FeatureCollection",
							features: features,
						};

						const dataSource = new source.DataSource(
							`${shipPosition.shipId}-${shipPosition.latitude}-${shipPosition.longitude * Math.random()}`,
						);
						mapRef.sources.add(dataSource);
						dataSource.add(layerData);

						const lineLayer = new layer.LineLayer(
							dataSource,
							`${shipPosition.shipId}-${shipPosition.latitude}-${shipPosition.longitude}`,
							{
								strokeColor: ["get", "myColor"],
								strokeWidth: 5,
								visible: true,
							},
						);

						mapRef.layers.add(lineLayer);
					});
				});
				mapRef.markers.add(svgLayer);
			});
		}
	}, [ShipLastPositions, isMapReady]);

	const preparePopupOptions = (pointPosition: any, offset = -10) => {
		const options = {
			position: pointPosition,
			pixelOffset: [0, offset],
		};
		return options;
	};

	const memoizedMapPopup = useMemo(() => {
		if (popupContent && mapRef) {
			const traceExists = mapRef.layers.getLayerById(
				`${popupContent.shipId}-${popupContent.latitude}-${popupContent.longitude}`,
			);
			console.log(traceExists);
			if (traceExists && !isVisible)
				mapRef.layers.remove(
					`${popupContent.shipId}-${popupContent.latitude}-${popupContent.longitude}`,
				);
		}
		return (
			<AzureMapPopup
				isVisible={isVisible}
				options={preparePopupOptions(popupPosition)}
				events={[
					{
						eventName: "close",
						callback() {
							setIsVisible(false);
						},
					},
				]}
				popupContent={<MapShipPopup shipPosition={popupContent} />}
			/>
		);
	}, [popupPosition, isVisible]);

	return (
		<AzureMap
			styles={{ width: "100%", height: "100%" }}
			options={option}
			controls={azureMapControls}
		>
			{memoizedMapPopup}
		</AzureMap>
	);
};

export default Map;
