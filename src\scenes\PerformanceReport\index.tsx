import { SearchOutlined } from "@ant-design/icons";
import {
	<PERSON><PERSON>,
	Col,
	DatePicker,
	Input,
	Modal,
	Row,
	Select,
	Tooltip,
} from "antd";
import Table, { ColumnProps } from "antd/lib/table";
import {
	FilterDropdownProps,
	FilterValue,
	SorterResult,
	TablePaginationConfig,
	TableRowSelection,
} from "antd/lib/table/interface";
import { inject, observer } from "mobx-react";
import moment, { Moment } from "moment";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import { L } from "../../lib/abpUtility";
import AppConsts from "../../lib/appconst";
import { EntityDto } from "../../services/dto/entityDto";
import { ListResultDto } from "../../services/dto/pagedResultDto";
import { GetMeterReadingSetPermormanceDataOutput } from "../../services/meterReadingSet/dto/getMeterReadingSetPerformanceDataOutput";
import { ParamRange } from "../../services/meterReadingSet/meterReadingSetService";
import MeterReadingSetStore from "../../stores/meterReadingSetStore";
import ShipStore from "../../stores/shipStore";
import Stores from "../../stores/storeIdentifier";
import utils from "../../utils/utils";
import { renderDate, renderFilterIcon, renderSearchIcon } from "../renderUtils";
import ShipInfoDisplay from "./components/ShipInfoDisplay";

export interface IPerformanceReportProps {
	shipStore: ShipStore;
	meterReadingSetStore: MeterReadingSetStore;
}

export interface IPerformanceReportState {
	shipId: number;
	selectedRows: ListResultDto<EntityDto>;
	selectedRecords: number[];
	searchTextInsideTable: Array<{ index: string; searchText: string }>;
	windSpeedRange: ParamRange<number>;
	wavesHeightRange: ParamRange<number>;
	sogRange: ParamRange<number>;
	avgSogRange: ParamRange<number>;
	stwRange: ParamRange<number>;
	avgStwRange: ParamRange<number>;
	draftRange: ParamRange<number>;
	durationRange: ParamRange<number>;
	dateRange: ParamRange<Moment>;
	loading: boolean;
	exportingReport: boolean;
	sorters: SorterResult<GetMeterReadingSetPermormanceDataOutput>[];
}

type RangeProps<T> = {
	[K in keyof IPerformanceReportState]: IPerformanceReportState[K] extends ParamRange<T>
		? K
		: never;
}[keyof IPerformanceReportState];

type PreviousState = {
	shipId: IPerformanceReportState["shipId"];
	filters?: Record<string, ParamRange<number | Moment>>;
	sorters?: SorterResult<GetMeterReadingSetPermormanceDataOutput>[];
};

const { Option } = Select;

@inject(Stores.ShipStore)
@inject(Stores.MeterReadingSetStore)
@observer
class PerformanceReport extends AppComponentBase<
	IPerformanceReportProps,
	IPerformanceReportState
> {
	state: IPerformanceReportState = {
		selectedRows: {
			items: [],
		},
		sorters: [],
		searchTextInsideTable: [{ index: "", searchText: "" }],
		shipId: 0,
		selectedRecords: [],
		windSpeedRange: { from: undefined, to: undefined },
		wavesHeightRange: { from: undefined, to: undefined },
		sogRange: { from: undefined, to: undefined },
		avgSogRange: { from: undefined, to: undefined },
		stwRange: { from: undefined, to: undefined },
		avgStwRange: { from: undefined, to: undefined },
		draftRange: { from: undefined, to: undefined },
		durationRange: { from: undefined, to: undefined },
		dateRange: { from: undefined, to: undefined },
		loading: false,
		exportingReport: false,
	};

	async componentDidMount() {
		await this.props.shipStore.getShipNames();
		const prevState = this.extractPreviousState();

		let newState: Record<string, unknown> = { shipId: prevState.shipId };
		if (prevState.filters) {
			const filters = prevState.filters;
			for (const element of Object.values(filters)) {
				if (typeof element.from === "string" && element.from !== undefined)
					element.from = moment(element.from);
				if (typeof element.to === "string" && element.to !== undefined)
					element.to = moment(element.to);
			}
			newState = { ...newState, ...filters };
		}

		if (prevState.sorters)
			newState = { ...newState, sorters: prevState.sorters };

		// biome-ignore lint/suspicious/noExplicitAny: Compiler issues
		this.setState({ ...newState } as any);

		if (prevState.shipId !== 0) {
			utils.insertUrlParam("shipId", prevState.shipId.toString());
			await this.props.shipStore.get({ id: prevState.shipId });
		}
	}

	extractPreviousState(): PreviousState {
		const { allShipNames } = this.props.shipStore;
		if (!allShipNames || allShipNames.length === 0) {
			return { shipId: 0 };
		}
		let defaultShip = allShipNames[0].id;

		const paramShipId = utils.getUrlParam("shipId");
		if (
			paramShipId &&
			!Number.isNaN(Number.parseInt(paramShipId)) &&
			allShipNames.findIndex((x) => x.id === Number.parseInt(paramShipId)) >= 0
		) {
			defaultShip = Number.parseInt(paramShipId);
		}

		const savedState = utils.getSortAndFilterFromStorage<PreviousState>(
			AppConsts.sortCacheKeys.Performance,
		);

		window.localStorage.removeItem(AppConsts.sortCacheKeys.Performance);

		if (savedState) return savedState;
		return { shipId: defaultShip };
	}

	async componentDidUpdate(
		_: Readonly<IPerformanceReportProps>,
		prevState: Readonly<IPerformanceReportState>,
	): Promise<void> {
		if (prevState.shipId !== this.state.shipId) {
			this.setState({ selectedRecords: [], selectedRows: { items: [] } });
			await this.handleUploadData();
		}
	}

	updateSelectedRecords = (
		selectedRows: GetMeterReadingSetPermormanceDataOutput[],
	) => {
		const selectedRecords = selectedRows.map((row) => row.id);
		this.setState({ selectedRecords });
	};

	async getPerformanceReport() {
		this.setState({ loading: true });
		const {
			stwRange,
			avgStwRange,
			sogRange,
			avgSogRange,
			wavesHeightRange,
			windSpeedRange,
			draftRange,
			durationRange,
			dateRange,
		} = this.state;

		try {
			await this.props.meterReadingSetStore.getPerformanceReport(
				this.state.shipId,
				stwRange,
				avgStwRange,
				sogRange,
				avgSogRange,
				wavesHeightRange,
				windSpeedRange,
				draftRange,
				durationRange,
				dateRange,
			);
		} catch (ex) {
			console.log(ex);
			Modal.error({
				title: "Error",
				content: (
					<div>
						<p>Fetchingh data failed. Please try again</p>
					</div>
				),
				onOk() {},
			});
		}
		this.setState({ loading: false });
	}

	async exportPerformanceReport() {
		await this.props.meterReadingSetStore.exportPerformanceReport(
			this.state.shipId,
			this.state.selectedRecords,
		);
	}

	handleGenerateReport = async () => {
		this.setState({ exportingReport: true });
		try {
			await this.exportPerformanceReport();
		} catch (error) {
			Modal.error({
				title: "Generate Report Error",
				content: (
					<div>
						<p>Generate Report Error</p>
					</div>
				),
				onOk() {},
			});
		}
		this.setState({ exportingReport: false });
	};

	handleUploadData = async () => {
		try {
			await this.getPerformanceReport();
		} catch (error) {
			console.log(error);
			Modal.error({
				title: "Load Data Error",
				content: (
					<div>
						<p>Data Upload Error</p>
					</div>
				),
				onOk() {},
			});
		}
	};

	getColumnSearchNumberProps = (dataIndex: RangeProps<number>) => ({
		filterDropdown: ({
			confirm,
			selectedKeys,
			setSelectedKeys,
		}: FilterDropdownProps) => (
			<div style={{ padding: 8 }}>
				<div style={{ display: "flex" }}>
					<Input
						placeholder={"From"}
						value={this.state[dataIndex].from}
						type="number"
						onChange={(e) =>
							this.setState({
								...this.state,
								[dataIndex]: {
									...this.state[dataIndex],
									from: Number.parseFloat(e.target.value),
								},
							})
						}
						style={{ width: 100, marginBottom: 8, display: "block" }}
					/>
					<Input
						placeholder={"To"}
						value={this.state[dataIndex].to}
						type="number"
						onChange={(e) =>
							this.setState({
								...this.state,
								[dataIndex]: {
									...this.state[dataIndex],
									to: Number.parseFloat(e.target.value),
								},
							})
						}
						style={{ width: 100, marginBottom: 8, display: "block" }}
					/>
				</div>
				<Button
					type="primary"
					onClick={() => {
						this.handleSearchTable();
						setSelectedKeys([...new Set([...selectedKeys, dataIndex])]);
						confirm?.();
					}}
					icon={<SearchOutlined />}
					size="small"
					style={{ width: 90, marginRight: 8 }}
				>
					Search
				</Button>
				<Button
					onClick={() => {
						setSelectedKeys([...selectedKeys.filter((x) => x !== dataIndex)]);
						confirm?.();
						this.handleReset(dataIndex);
					}}
					size="small"
					style={{ width: 90 }}
				>
					Reset
				</Button>
			</div>
		),
		filterIcon: () =>
			renderSearchIcon(
				this.state[dataIndex].from !== undefined &&
					this.state[dataIndex].to !== undefined,
			),
	});

	getColumnSearchDateProps = (dataIndex: RangeProps<Moment>) => ({
		filterDropdown: () => (
			<div style={{ padding: 8 }}>
				<DatePicker.RangePicker
					style={{ marginBottom: "8px", width: "250px" }}
					value={[
						this.state[dataIndex].from || null,
						this.state[dataIndex].to || null,
					]}
					onChange={(dates) => {
						if (dates)
							this.setState({
								...this.state,
								[dataIndex]: {
									from: dates[0] === null ? undefined : dates[0],
									to: dates[1] === null ? undefined : dates[1],
								},
							});
					}}
				/>
				<div style={{ display: "flex", justifyContent: "center" }}>
					<Button
						type="primary"
						onClick={() => this.handleSearchTable()}
						icon={<SearchOutlined />}
						size="small"
						style={{ width: 90, marginRight: 8 }}
					>
						Search
					</Button>
					<Button
						onClick={() => this.handleReset(dataIndex)}
						size="small"
						style={{ width: 90 }}
					>
						Reset
					</Button>
				</div>
			</div>
		),
		filterIcon: renderSearchIcon,
	});

	componentWillUnmount(): void {
		const {
			selectedRecords,
			searchTextInsideTable,
			selectedRows,
			shipId,
			loading,
			exportingReport,
			sorters,
			...filters
		} = this.state;

		const settings = {
			shipId: this.state.shipId,
			filters: filters,
			sorters: sorters,
		};

		utils.saveSortAndFilterToStorage(
			AppConsts.sortCacheKeys.Performance,
			settings,
		);
	}

	handleSearchTable = async () => {
		await this.getPerformanceReport();
	};

	handleReset = (dataIndex: RangeProps<unknown>) => {
		this.setState(
			{ ...this.state, [dataIndex]: { from: undefined, to: undefined } },
			async () => await this.getPerformanceReport(),
		);
	};

	handleTableChange = (
		_: TablePaginationConfig,
		__: Record<string, FilterValue | null>,
		sorter:
			| SorterResult<GetMeterReadingSetPermormanceDataOutput>
			| SorterResult<GetMeterReadingSetPermormanceDataOutput>[],
	) => {
		if (Array.isArray(sorter))
			this.setState({
				sorters: sorter.map((s) => {
					return { columnKey: s.columnKey, order: s.order };
				}),
			});
		else {
			this.setState({
				sorters: [{ columnKey: sorter.columnKey, order: sorter.order }],
			});
		}
	};

	public render() {
		const { allShipNames } = this.props.shipStore;

		const exportErrorMessages: string[] = [];

		if (this.state.selectedRecords.length === 0) {
			exportErrorMessages.push("At least 1 selected record");
		}

		const columns: Array<ColumnProps<GetMeterReadingSetPermormanceDataOutput>> =
			[
				{
					title: L("Trial Start Date/ Time (UTC)"),
					dataIndex: "startDate",
					key: "startDate",
					width: 270,
					render: (text: string) => renderDate(text, true),
					sortOrder: this.state.sorters.find((x) => x.columnKey === "startDate")
						?.order,
					sorter: {
						compare: (a, b) =>
							new Date(a.startDate).getTime() - new Date(b.startDate).getTime(),
						multiple: 1,
					},
					filterDropdown: ({ confirm, clearFilters }) => (
						<div style={{ padding: 8 }}>
							<DatePicker.RangePicker
								style={{ marginBottom: 8, width: 200 }}
								value={[
									this.state.dateRange.from || null,
									this.state.dateRange.to || null,
								]}
								onChange={(dates) => {
									if (dates && dates.length === 2 && dates[0] && dates[1]) {
										this.setState(
											{
												dateRange: {
													from: dates[0],
													to: dates[1],
												},
											},
											() => {
												confirm?.();
												this.getPerformanceReport();
											},
										);
									} else {
										this.setState(
											{
												dateRange: { from: undefined, to: undefined },
											},
											() => {
												clearFilters?.();
												this.getPerformanceReport();
											},
										);
									}
								}}
							/>
						</div>
					),
					filterIcon: () =>
						renderFilterIcon(
							this.state.dateRange.from !== undefined &&
								this.state.dateRange.to !== undefined,
						),
				},
				{
					title: L("Trial duration (h)"),
					dataIndex: "trailDuration",
					key: "trailDuration",
					width: 220,
					sortOrder: this.state.sorters.find(
						(x) => x.columnKey === "trailDuration",
					)?.order,
					sorter: {
						compare: (
							a: GetMeterReadingSetPermormanceDataOutput,
							b: GetMeterReadingSetPermormanceDataOutput,
						) => a.trailDuration - b.trailDuration,
						multiple: 2,
					},
					render: (
						text: string,
						item: GetMeterReadingSetPermormanceDataOutput,
					) => {
						const duration = item.trailDuration;
						if (Number.isNaN(duration) || !Number.isFinite(duration)) {
							return <span>-</span>;
						}
						return <span>{(Math.round(duration * 10) / 10).toFixed(1)}</span>;
					},
					...this.getColumnSearchNumberProps("durationRange"),
				},
				{
					title: L("Draft (m)"),
					dataIndex: "draft",
					key: "draft",
					width: 220,
					sortOrder: this.state.sorters.find((x) => x.columnKey === "draft")
						?.order,
					sorter: {
						compare: (
							a: GetMeterReadingSetPermormanceDataOutput,
							b: GetMeterReadingSetPermormanceDataOutput,
						) => a.draft - b.draft,
						multiple: 3,
					},
					render: (
						text: string,
						item: GetMeterReadingSetPermormanceDataOutput,
					) => {
						const draft = item.draft;
						if (Number.isNaN(draft) || !Number.isFinite(draft)) {
							return <span />;
						}
						return <span>{(Math.round(draft * 10) / 10).toFixed(1)}</span>;
					},
					...this.getColumnSearchNumberProps("draftRange"),
				},
				{
					title: L("SOG avg Knots"),
					dataIndex: "avgSog",
					key: "avgSog",
					width: 220,
					sortOrder: this.state.sorters.find((x) => x.columnKey === "avgSog")
						?.order,
					sorter: {
						compare: (
							a: GetMeterReadingSetPermormanceDataOutput,
							b: GetMeterReadingSetPermormanceDataOutput,
						) => a.avgSog - b.avgSog,
						multiple: 4,
					},
					render: (
						text: string,
						item: GetMeterReadingSetPermormanceDataOutput,
					) => {
						const avgSog = item.avgSog;
						if (Number.isNaN(avgSog) || !Number.isFinite(avgSog)) {
							return <span />;
						}
						return <span>{(Math.round(avgSog * 10) / 10).toFixed(1)}</span>;
					},
					...this.getColumnSearchNumberProps("avgSogRange"),
				},
				{
					title: L("SOG +/- range Knots"),
					dataIndex: "sog",
					key: "sog",
					width: 220,
					sortOrder: this.state.sorters.find((x) => x.columnKey === "sog")
						?.order,
					sorter: {
						compare: (
							a: GetMeterReadingSetPermormanceDataOutput,
							b: GetMeterReadingSetPermormanceDataOutput,
						) => a.sog - b.sog,
						multiple: 5,
					},
					render: (
						text: string,
						item: GetMeterReadingSetPermormanceDataOutput,
					) => {
						const sog = item.sog;
						if (Number.isNaN(sog) || !Number.isFinite(sog)) {
							return <span />;
						}
						return <span>{(Math.round(sog * 10) / 10).toFixed(1)}</span>;
					},
					...this.getColumnSearchNumberProps("sogRange"),
				},
				{
					title: L("STW avg Knots"),
					dataIndex: "avgStw",
					key: "avgStw",
					width: 220,
					sortOrder: this.state.sorters.find((x) => x.columnKey === "avgStw")
						?.order,
					sorter: {
						compare: (
							a: GetMeterReadingSetPermormanceDataOutput,
							b: GetMeterReadingSetPermormanceDataOutput,
						) => a.avgStw - b.avgStw,
						multiple: 6,
					},
					render: (
						text: string,
						item: GetMeterReadingSetPermormanceDataOutput,
					) => {
						const avgStwValue = item.avgStw;
						if (Number.isNaN(avgStwValue) || !Number.isFinite(avgStwValue)) {
							return <span />;
						}
						return (
							<span>{(Math.round(avgStwValue * 10) / 10).toFixed(1)}</span>
						);
					},
					...this.getColumnSearchNumberProps("avgStwRange"),
				},
				{
					title: L("STW +/- range Knots"),
					dataIndex: "stw",
					key: "stw",
					width: 220,
					sortOrder: this.state.sorters.find((x) => x.columnKey === "stw")
						?.order,
					sorter: {
						compare: (
							a: GetMeterReadingSetPermormanceDataOutput,
							b: GetMeterReadingSetPermormanceDataOutput,
						) => a.stw - b.stw,
						multiple: 7,
					},
					render: (
						text: string,
						item: GetMeterReadingSetPermormanceDataOutput,
					) => {
						const stwValue = item.stw;
						if (Number.isNaN(stwValue) || !Number.isFinite(stwValue)) {
							return <span />;
						}
						return <span>{(Math.round(stwValue * 10) / 10).toFixed(1)}</span>;
					},
					...this.getColumnSearchNumberProps("stwRange"),
				},
				{
					title: L("Wind Speed avg (knots)"),
					dataIndex: "windSpeed",
					key: "windSpeed",
					width: 220,
					sortOrder: this.state.sorters.find((x) => x.columnKey === "windSpeed")
						?.order,
					sorter: {
						compare: (
							a: GetMeterReadingSetPermormanceDataOutput,
							b: GetMeterReadingSetPermormanceDataOutput,
						) => a.windSpeed - b.windSpeed,
						multiple: 8,
					},
					render: (
						text: string,
						item: GetMeterReadingSetPermormanceDataOutput,
					) => (
						<span>
							{item.windSpeed !== null && !Number.isNaN(Number(item.windSpeed))
								? (Math.round(Number(item.windSpeed) * 10) / 10).toFixed(1)
								: ""}
						</span>
					),
					...this.getColumnSearchNumberProps("windSpeedRange"),
				},
				{
					title: L("Sig Wave Height (m)"),
					dataIndex: "waveHeight",
					key: "waveHeight",
					width: 220,
					sortOrder: this.state.sorters.find(
						(x) => x.columnKey === "waveHeight",
					)?.order,
					sorter: {
						compare: (
							a: GetMeterReadingSetPermormanceDataOutput,
							b: GetMeterReadingSetPermormanceDataOutput,
						) => a.waveHeight - b.waveHeight,
						multiple: 9,
					},
					render: (
						text: string,
						item: GetMeterReadingSetPermormanceDataOutput,
					) => (
						<span>
							{item.waveHeight !== null && typeof item.waveHeight === "number"
								? (Math.round(item.waveHeight * 10) / 10).toFixed(1)
								: ""}
						</span>
					),
					...this.getColumnSearchNumberProps("wavesHeightRange"),
				},
			];

		const rowSelection: TableRowSelection<GetMeterReadingSetPermormanceDataOutput> =
			{
				onChange: (
					_: unknown,
					selectedRows: GetMeterReadingSetPermormanceDataOutput[],
				) => {
					this.updateSelectedRecords(selectedRows);
					const selectedRowsItems: EntityDto[] = selectedRows.map((x) => {
						return {
							id: x.id,
						};
					});
					this.setState({
						selectedRows: {
							items: selectedRowsItems,
						},
					});
				},
				selectedRowKeys: this.state.selectedRows.items.map((x) =>
					x.id.toString(),
				),
			};

		return (
			<>
				<div
					style={{
						display: "flex",
						flexDirection: "column",
						gap: "10px",
						marginBottom: "5px",
					}}
				>
					<Row gutter={[12, 5]}>
						<Col xs={20} sm={20} md={12} xl={8} xxl={4} className="gutter-row">
							<ShipInfoDisplay
								withoutMaxWidth
								title={"IMO"}
								value={this.props.shipStore.editShip?.imoNumber}
							/>
						</Col>
						<Col xs={20} sm={20} md={12} xl={8} xxl={4} className="gutter-row">
							<ShipInfoDisplay
								withoutMaxWidth
								title={"Vessel Type"}
								value={this.props.shipStore.editShip?.type}
							/>
						</Col>
						<Col xs={20} sm={20} md={12} xl={8} xxl={4} className="gutter-row">
							<ShipInfoDisplay
								withoutMaxWidth
								title={"Vessel SDWT"}
								value={this.props.shipStore.editShip?.sdwt}
							/>
						</Col>
						<Col xs={20} sm={20} md={12} xl={8} xxl={4} className="gutter-row">
							<ShipInfoDisplay
								title={"Vessel GT"}
								value={this.props.shipStore.editShip?.gt}
							/>
						</Col>
						<Col xs={20} sm={20} md={12} xl={8} xxl={6} className="gutter-row">
							<ShipInfoDisplay
								withoutMaxWidth
								title={"Scrubber Fitted(Type)"}
								value={this.props.shipStore.editShip?.scrubber}
							/>
						</Col>
					</Row>
					<Row gutter={12}>
						<Col xs={20} sm={20} md={12} xxl={4}>
							<div style={{ display: "flex", alignItems: "center" }}>
								<span className="data-title">Vessel Name</span>
								{allShipNames && (
									<Select
										showSearch={allShipNames.length !== 0}
										className="data-value"
										style={{
											height: "min-content",
										}}
										value={this.state.shipId}
										onChange={(value: number) => {
											this.setState({ shipId: value });
											this.props.shipStore.get({ id: value });
											utils.insertUrlParam("shipId", value.toString());
										}}
									>
										{allShipNames.length === 0 && (
											<Option key={0} value={0} disabled>
												---
											</Option>
										)}
										{allShipNames.map((x) => (
											<Option key={x.id} value={x.id}>
												{x.shipName}
											</Option>
										))}
									</Select>
								)}
							</div>
						</Col>
					</Row>
					<div>
						<Tooltip
							placement="right"
							destroyTooltipOnHide
							title={
								exportErrorMessages.length > 0 ? (
									<div>
										Missing data:
										<ul>
											{exportErrorMessages.map((x) => (
												<li key={x}>{x}</li>
											))}
										</ul>
									</div>
								) : null
							}
						>
							<Button
								style={{ marginBottom: 10, width: "fit-content" }}
								type="primary"
								loading={this.state.exportingReport}
								onClick={() => this.handleGenerateReport()}
								disabled={exportErrorMessages.length !== 0}
							>
								Generate Report
							</Button>
						</Tooltip>
					</div>
				</div>

				<Table
					rowKey={(record) => record.id.toString()}
					bordered={true}
					className="performanceReport"
					columns={columns}
					dataSource={
						this.props.meterReadingSetStore.meterReadingSetPerformanceData
					}
					onChange={this.handleTableChange}
					rowSelection={rowSelection}
					scroll={{ y: 700 }}
					loading={this.state.loading}
					pagination={{ position: ["bottomCenter"] }}
				/>
				<Chat />
			</>
		);
	}
}

export default PerformanceReport;
