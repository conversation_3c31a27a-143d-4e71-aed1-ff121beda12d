import { action, observable } from "mobx";
import type { GetPortOutput } from "../services/port/dto/getPortOutput";
import portService from "../services/port/portService";

class PortStore {
	@observable ports: Map<number, string> = new Map();
	@observable selectedPort!: GetPortOutput;

	@action
	async getAll(keyword: string) {
		const result = await portService.getAll(keyword);
		this.ports = result;
	}

	@action
	async getByCode(code: string) {
		const result = await portService.getByCode(code);
		this.selectedPort = result;
	}
}

export default PortStore;
