import {
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>,
	Select,
	Table,
	Tooltip,
} from "antd";
import { ColumnProps } from "antd/lib/table";
import {
	FilterDropdownProps,
	FilterValue,
	SorterResult,
	TablePaginationConfig,
	TableRowSelection,
} from "antd/lib/table/interface";
import { inject, observer } from "mobx-react";
import moment, { isMoment, Moment } from "moment";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import FilterSelect from "../../components/FilterSelect/FilterSelect";
import { L } from "../../lib/abpUtility";
import {
	EventSource,
	GetMeterReadingSetOutput,
} from "../../services/meterReadingSet/dto/getMeterReadingSetOutput";
import EventsStore from "../../stores/eventsStore";
import MeterReadingSetStore from "../../stores/meterReadingSetStore";
import ShipStore from "../../stores/shipStore";
import Stores from "../../stores/storeIdentifier";
import utils from "../../utils/utils";
import ShipInfoDisplay from "../PerformanceReport/components/ShipInfoDisplay";
import { voyageTypeCustomData } from "../enumUtils";
import { renderDate, renderFilterIcon } from "../renderUtils";
import "./index.less";

export interface IEmissionReportProps {
	shipStore: ShipStore;
	meterReadingSetStore: MeterReadingSetStore;
	eventsStore: EventsStore;
}

export interface IEmissionReportState {
	startDate: Moment | null;
	endDate: Moment | null;
	shipId: number;
	selectedRows: GetMeterReadingSetOutput[];
	voyageType: string;
	notificationId: number;
	selectedRecords: number[];
	loading: boolean;
	filterLoading: boolean;
	filter: string;
	exportingReport: boolean;
	sorters: SorterResult<GetMeterReadingSetOutput>[];
}

type PreviousState = {
	shipId: IEmissionReportState["shipId"];
	startDate: IEmissionReportState["startDate"];
	endDate: IEmissionReportState["endDate"];
	sorters: IEmissionReportState["sorters"];
};

const { Option } = Select;

@inject(Stores.ShipStore)
@inject(Stores.MeterReadingSetStore)
@inject(Stores.EventsStore)
@observer
class EmissionReport extends AppComponentBase<
	IEmissionReportProps,
	IEmissionReportState
> {
	state: IEmissionReportState = {
		selectedRows: [],
		shipId: 0,
		startDate: null,
		endDate: null,
		voyageType: "",
		notificationId: 0,
		selectedRecords: [],
		loading: false,
		filterLoading: false,
		filter: "",
		exportingReport: false,
		sorters: [],
	};

	async componentDidMount() {
		await this.props.shipStore.getShipNames();

		const prevState = this.extractPreviousState();

		if (prevState.shipId > 0)
			await this.props.shipStore.get({ id: prevState.shipId });

		this.setState({ ...prevState });
	}

	extractPreviousState(): PreviousState {
		const { allShipNames } = this.props.shipStore;
		let state: PreviousState = {
			shipId: 0,
			startDate: null,
			sorters: [],
			endDate: null,
		};
		if (!allShipNames || allShipNames.length === 0) {
			return state;
		}

		state.shipId = allShipNames[0].id;

		const paramShipId = utils.getUrlNumericParam("shipId");
		if (
			paramShipId &&
			allShipNames.findIndex((x) => x.id === paramShipId) >= 0
		) {
			state.shipId = paramShipId;
		}

		const startDate = utils.getUrlMomentParam("startDate");
		state.startDate = startDate;

		const endDate = utils.getUrlMomentParam("endDate");
		state.endDate = endDate;

		const prevState =
			utils.getSortAndFilterFromStorage<PreviousState>("emission-filters");

		if (prevState)
			state = {
				...prevState,
				startDate: isMoment(prevState.startDate)
					? moment(prevState.startDate)
					: null,
				endDate: isMoment(prevState.endDate) ? moment(prevState.endDate) : null,
			};

		utils.removeStateFromStorage("emission-filters");

		return state;
	}

	componentWillUnmount(): void {
		const settings: PreviousState = {
			sorters: this.state.sorters,
			shipId: this.state.shipId,
			endDate: this.state.endDate,
			startDate: this.state.startDate,
		};

		utils.saveSortAndFilterToStorage("emission-filters", settings);
	}

	componentDidUpdate(
		prevProps: Readonly<IEmissionReportProps>,
		prevState: Readonly<IEmissionReportState>,
	): void {
		let shouldReload = false;
		if (this.state.shipId !== 0 && prevState.shipId !== this.state.shipId) {
			shouldReload = true;
		}

		if (this.state.startDate && prevState.startDate !== this.state.startDate) {
			shouldReload = true;
		}

		if (this.state.endDate && prevState.endDate !== this.state.endDate) {
			shouldReload = true;
		}

		if (shouldReload) this.handleUploadData();
	}

	async getEmissionReport() {
		if (!this.state.startDate) return;
		if (!this.state.endDate) return;
		this.setState({ loading: true });

		await this.props.meterReadingSetStore.getEmissionReport(
			this.state.shipId,
			this.state.startDate.format("YYYY-MM-DD HH:mm:ss"),
			this.state.endDate.format("YYYY-MM-DD HH:mm:ss"),
		);

		this.setState({ loading: false });
	}

	async exportEmissionReport() {
		await this.props.meterReadingSetStore.exportEmissionReport(
			this.state.shipId,
			this.state.voyageType,
			this.state.selectedRows.map((x) => x.id),
		);
	}

	updateSelectedRecords = (selectedRows: GetMeterReadingSetOutput[]) => {
		const selectedRecords = selectedRows.map((row) => row.id);
		this.setState({ selectedRecords, selectedRows: selectedRows });
	};

	handleGenerateReport = async () => {
		try {
			this.setState({ exportingReport: true });
			await this.exportEmissionReport();
		} catch (error) {
			Modal.error({
				title: "Generate Report Error",
				content: (
					<div>
						<p>Generate Report Error</p>
					</div>
				),
				onOk() {},
			});
		} finally {
			this.setState({ exportingReport: false });
		}
	};

	handleUploadData = async () => {
		try {
			await this.getEmissionReport();
		} catch (error) {
			// biome-ignore lint/suspicious/noExplicitAny: <explanation>
			const errorMessage = (error as any).response?.data?.result;
			Modal.error({
				title: "Load Data Error",
				content: (
					<div>
						<p>{errorMessage ? errorMessage : "Data Load Error"}</p>
					</div>
				),
				onOk() {},
			});
			this.setState({ loading: false });
		}
	};

	handleTableChange = (
		_: TablePaginationConfig,
		__: Record<string, FilterValue | null>,
		sorter:
			| SorterResult<GetMeterReadingSetOutput>
			| SorterResult<GetMeterReadingSetOutput>[],
	) => {
		const sorters = utils.getSorters(sorter);
		this.setState({ sorters });
	};

	getRowsBetween = (selectedRows: GetMeterReadingSetOutput[]) => {
		if (selectedRows.length < 2) return selectedRows;

		const { meterReadingsSets } = this.props.meterReadingSetStore;
		const firstIndex = meterReadingsSets.findIndex(
			(x) =>
				x.id ===
				selectedRows.find(
					(x, i) => i === Math.min(...selectedRows.map((x, i) => i)),
				)?.id,
		);

		const lastIndex = meterReadingsSets.findIndex(
			(x) =>
				x.id ===
				selectedRows.find(
					(x, i) => i === Math.max(...selectedRows.map((x, i) => i)),
				)?.id,
		);

		return meterReadingsSets.filter(
			(x, i) =>
				i >= Math.min(firstIndex, lastIndex) &&
				i <= Math.max(firstIndex, lastIndex),
		);
	};

	public render() {
		const { meterReadingsSets } = this.props.meterReadingSetStore;
		const { allShipNames } = this.props.shipStore;

		const exportErrorMessages: string[] = [];

		if (!this.state.voyageType) {
			exportErrorMessages.push("Voyage type");
		}

		if (this.state.selectedRecords.length < 2) {
			exportErrorMessages.push("2 selected records");
		}

		const columns: Array<ColumnProps<GetMeterReadingSetOutput>> = [
			{
				title: L("Vessel Status (Current)"),
				dataIndex: "place",
				key: "place",
				sortOrder: this.state.sorters.find((x) => x.columnKey === "place")
					?.order,
				width: 150,
				render: (text: string, item: GetMeterReadingSetOutput) => {
					if (item.eventSource === EventSource.Server)
						return <span>{item.place}</span>;
					if (item.place === "At Port")
						return (
							<span>
								{item.place} - {item.position.portName}
							</span>
						);
					if (item.position.country)
						return (
							<span>
								{item.place} - {item.position.country}
							</span>
						);

					return <span>{item.place}</span>;
				},
				sorter: {
					compare: (a: GetMeterReadingSetOutput, b: GetMeterReadingSetOutput) =>
						(a.place || "").localeCompare(b.place || ""),
					multiple: 4,
				},
			},
			{
				title: L("Completed by OBU Date/Time (UTC)"),
				dataIndex: "firstReadingDate",
				key: "firstReadingDate",
				width: 200,
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "firstReadingDate",
				)?.order,
				render: (text: string) => renderDate(text, true),
				sorter: {
					compare: (a: GetMeterReadingSetOutput, b: GetMeterReadingSetOutput) =>
						new Date(a.firstReadingDate).getTime() -
						new Date(b.firstReadingDate).getTime(),
					multiple: 3,
				},
			},
			{
				title: L("Nearest AIS position (Lat/Long)"),
				dataIndex: "position",
				key: "position",
				width: 200,
				render: (text: string, item: GetMeterReadingSetOutput) => (
					<>
						<div
							style={{
								display: "flex",
								justifyContent: "space-between",
								alignItems: "center",
							}}
						>
							<span>Latitude</span>
							<div>{item.position.latitude}</div>
						</div>
						<div
							style={{
								display: "flex",
								justifyContent: "space-between",
								alignItems: "center",
							}}
						>
							<span>Longitude</span>
							<div>{item.position.longitude}</div>
						</div>
					</>
				),
			},
			{
				title: L("Nearest AIS position (Date/Time)"),
				dataIndex: "closestAISPoint",
				key: "closestAISPoint",
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "closestAISPoint",
				)?.order,
				render: (text: string) => renderDate(text, true),
				width: 200,
				sorter: {
					compare: (a: GetMeterReadingSetOutput, b: GetMeterReadingSetOutput) =>
						new Date(a.closestAISPoint).getTime() -
						new Date(b.closestAISPoint).getTime(),
					multiple: 2,
				},
			},
			{
				title: L("Vessel Status (Since previous line in report)"),
				dataIndex: "aisStatus",
				key: "aisStatus",
				width: 150,
				sortOrder: this.state.sorters.find((x) => x.columnKey === "aisStatus")
					?.order,
				sorter: {
					compare: (a: GetMeterReadingSetOutput, b: GetMeterReadingSetOutput) =>
						(a.aisStatus || "").localeCompare(b.aisStatus || ""),
					multiple: 1,
				},
				filterDropdown: (props: FilterDropdownProps) => (
					<div>
						{props.visible && (
							<FilterSelect
								{...props}
								loading={this.state.filterLoading}
								handleFilter={(value) => {
									this.setState({ filter: value });
									props.setSelectedKeys(["aisStatus"]);
									props.confirm();
								}}
								title={"test"}
								value={this.state.filter}
								options={this.props.meterReadingSetStore.meterReadingsSets
									?.map((x) => x.aisStatus)
									.filter(
										(value, index, array) => array.indexOf(value) === index,
									)
									.map((x) => {
										return { key: x, value: x };
									})}
							/>
						)}
					</div>
				),
				onFilterDropdownVisibleChange: (v) => {
					//Fix for dropdown disappering after first open
					this.setState({ filterLoading: v }, async () =>
						setTimeout(() => this.setState({ filterLoading: !v }), 50),
					);
				},
				filterIcon: () => renderFilterIcon(!!this.state.filter),
				onFilter: (value, record) => {
					return !this.state.filter || record.aisStatus === this.state.filter;
				},
			},
		];

		const rowSelection: TableRowSelection<GetMeterReadingSetOutput> = {
			selectedRowKeys: this.state.selectedRows.map((x) => x.id.toString()),
			onChange: (selectedRowKeys, selectedRows: GetMeterReadingSetOutput[]) => {
				if (this.state.selectedRows.length > selectedRows.length)
					this.updateSelectedRecords(selectedRows);
				else this.updateSelectedRecords(this.getRowsBetween(selectedRows));
			},
		};

		return (
			<>
				<div
					style={{
						display: "flex",
						flexDirection: "column",
						gap: "10px",
						marginBottom: "5px",
					}}
				>
					<Row gutter={[12, 6]}>
						<Col
							style={{ display: "flex", alignItems: "center" }}
							xs={20}
							sm={20}
							md={12}
							xxl={5}
						>
							<span className="data-title">Vessel Name</span>
							{allShipNames && (
								<Select
									showSearch={allShipNames.length !== 0}
									className="data-value"
									value={this.state.shipId}
									onChange={(value: number) => {
										this.setState({ shipId: value });
										this.props.shipStore.get({ id: value });
										utils.insertUrlParam("shipId", value.toString());
									}}
								>
									{allShipNames.length === 0 && (
										<Option key={0} value={0} disabled>
											---
										</Option>
									)}
									{allShipNames.map((x) => (
										<Option key={x.id} value={x.id} data-shipName={x.shipName}>
											{x.shipName}
										</Option>
									))}
								</Select>
							)}
						</Col>
						<Col xs={20} sm={20} md={12} xxl={3}>
							<ShipInfoDisplay
								title="IMO"
								value={this.props.shipStore.editShip?.imoNumber}
							/>
						</Col>
						<Col xs={20} sm={20} md={12} xxl={5}>
							<ShipInfoDisplay
								title="Vessel Type"
								value={this.props.shipStore.editShip?.type}
							/>
						</Col>
						<Col xs={20} sm={20} md={12} xxl={4}>
							<ShipInfoDisplay
								title="Vessel SDWT"
								value={this.props.shipStore.editShip?.sdwt}
							/>
						</Col>
						<Col xs={20} sm={20} md={12} xxl={4}>
							<ShipInfoDisplay
								title="Vessel GT"
								value={this.props.shipStore.editShip?.gt}
							/>
						</Col>
					</Row>
					<Row gutter={[12, 6]}>
						<Col xs={24} xxl={8}>
							<div style={{ display: "flex", flex: 1, alignItems: "center" }}>
								<span className="data-title">Date range</span>
								<DatePicker.RangePicker
									disabled={this.state.shipId === 0}
									className="data-value"
									value={[this.state.startDate, this.state.endDate]}
									onChange={(dates) => {
										const [startDate, endDate] = dates || [];
										this.setState({
											startDate: startDate || null,
											endDate: endDate || null,
										});
										if (startDate)
											utils.insertUrlParam(
												"startDate",
												startDate.format("YYYY-MM-DD"),
											);

										if (endDate)
											utils.insertUrlParam(
												"endDate",
												endDate.format("YYYY-MM-DD"),
											);
									}}
								/>
							</div>
						</Col>
						<Col xs={24} xxl={9}>
							<div style={{ display: "flex", flex: 1, alignItems: "center" }}>
								<span className="data-title">Voyage Type</span>
								<Select
									className="data-value"
									onChange={(value: string) => {
										this.setState({ voyageType: value });
									}}
									disabled={this.state.selectedRecords.length === 0}
								>
									{Array.from(voyageTypeCustomData.entries()).map(
										([key, value]) => (
											<Option key={key} value={key}>
												{value}
											</Option>
										),
									)}
								</Select>
							</div>
						</Col>
						<Col>
							<Tooltip
								placement="right"
								title={
									exportErrorMessages.length > 0 ? (
										<div>
											Missing data:
											<ul>
												{exportErrorMessages.map((x) => (
													<li key={x}>{x}</li>
												))}
											</ul>
										</div>
									) : null
								}
							>
								<Button
									type="primary"
									onClick={() => this.handleGenerateReport()}
									loading={this.state.exportingReport}
									disabled={exportErrorMessages.length !== 0}
								>
									Generate Report
								</Button>
							</Tooltip>
						</Col>
					</Row>
				</div>
				<Table
					rowKey={(record) => record.id.toString()}
					bordered={true}
					className="emissionReport"
					columns={columns}
					dataSource={meterReadingsSets || []}
					rowSelection={rowSelection}
					loading={this.state.loading}
					onChange={this.handleTableChange}
					pagination={{ position: ["bottomCenter"] }}
				/>
				<Chat />
			</>
		);
	}
}

export default EmissionReport;
