import { PagedFilterAndSortedRequest } from "../dto/pagedFilterAndSortedRequest";
import { PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoints";
import http from "../httpService";
import { AbpResponse } from "./../dto/abpResponse";
import { GetPortOutput } from "./dto/getPortOutput";

class PortService {
	public async getAll(keyword: string): Promise<Map<number, string>> {
		const result = await http.get(Endpoint.Port.GetAll, {
			params: {
				skipCount: 0,
				maxResultCount: 100,
				keyword: keyword,
			} as PagedFilterAndSortedRequest,
		});
		return new Map(
			(result.data.result as PagedResultDto<GetPortOutput>).items.map(
				(x: GetPortOutput) => [x.id, x.name],
			),
		);
	}

	public async getByCode(code: string): Promise<GetPortOutput> {
		const result = await http.get<AbpResponse<GetPortOutput>>(
			Endpoint.Port.GetByCode,
			{
				params: { code },
			},
		);
		return result.data.result;
	}
}

export default new PortService();
