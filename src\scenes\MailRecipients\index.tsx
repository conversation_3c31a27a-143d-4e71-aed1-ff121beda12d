import { WrappedFormUtils } from "@ant-design/compatible/lib/form/Form";
import {
	PlusOutlined,
	SearchOutlined,
	SettingOutlined,
} from "@ant-design/icons";
import { <PERSON><PERSON>, Card, Col, Dropdown, Input, Menu, Modal, Row } from "antd";
import Table, { ColumnProps } from "antd/lib/table";
import { inject, observer } from "mobx-react";
import * as React from "react";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import { L } from "../../lib/abpUtility";
import { EntityDto } from "../../services/dto/entityDto";
import { ListResultDto } from "../../services/dto/pagedResultDto";
import { GetMailRecipientOutput } from "../../services/mailRecipient/dto/getMailRecipientOutput";
import MailRecipientStore from "../../stores/mailRecipientStore";
import Stores from "../../stores/storeIdentifier";
import { ModalType } from "../ModalConsts";
import { getTablePaginationOptions, renderSearchIcon } from "../renderUtils";
import CreateOrUpdateMailRecipients from "./components/createOrUpdateMailRecipients";
import {
	FilterDropdownProps,
	TablePaginationConfig,
	TableRowSelection,
} from "antd/lib/table/interface";

export interface IMailRecipientsProps {
	mailRecipientStore: MailRecipientStore;
}

export interface IMailRecipientState {
	modalVisible: boolean;
	maxResultCount: number;
	skipCount: number;
	filter: string;
	sorting: string;
	selectedRows: ListResultDto<EntityDto>;
	id: number;
	okButtonDisabled: boolean;
	searchTextInsideTable: Array<{ index: string; searchText: string }>;
	searchTextTable: string;
	searchedColumnTable: string;
}

const confirm = Modal.confirm;

@inject(Stores.MailRecipientStore)
@observer
class MailRecipients extends AppComponentBase<
	IMailRecipientsProps,
	IMailRecipientState
> {
	formRef?: WrappedFormUtils;

	state: IMailRecipientState = {
		maxResultCount: 10,
		skipCount: 0,
		modalVisible: false,
		filter: "",
		sorting: "",
		selectedRows: {
			items: [],
		},
		id: 0,
		okButtonDisabled: false,
		searchTextInsideTable: [{ index: "", searchText: "" }],
		searchTextTable: "",
		searchedColumnTable: "",
	};

	async componentDidMount() {
		await Promise.all([this.getAll()]);
	}

	async getAll() {
		await this.props.mailRecipientStore.getAll({
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: this.state.filter,
		});
	}

	Modal = () => {
		this.setState({
			modalVisible: !this.state.modalVisible,
		});
	};

	async createOrUpdateModalOpen(entityDto: EntityDto) {
		if (entityDto.id === 0) {
			await this.props.mailRecipientStore.createMailRecipient();
		} else {
			await this.props.mailRecipientStore.get(entityDto);
		}

		this.setState({ id: entityDto.id });
		this.Modal();
		if (!this.formRef) return;

		this.formRef.setFieldsValue({
			...this.props.mailRecipientStore?.editMailRecipient,
		});
	}

	delete(input: EntityDto) {
		const self = this;
		confirm({
			title: "Do you want to delete these items?",
			onOk() {
				self.props.mailRecipientStore.delete(input);
			},
			onCancel() {},
		});
	}

	handleCreate = () => {
		if (this.state.okButtonDisabled === false) {
			const form = this.formRef;
			if (!form) return;
			// biome-ignore lint/suspicious/noExplicitAny: Poor type implementation from library
			form.validateFields(async (err: any, values: any) => {
				if (err) {
					this.setState({ okButtonDisabled: false });
					return;
				}
				this.setState({ okButtonDisabled: true });
				if (this.state.id === 0) {
					await this.props.mailRecipientStore.create(values);
				} else {
					await this.props.mailRecipientStore.update({
						id: this.state.id,
						...values,
					});
				}
				await this.getAll();
				form.resetFields();
				this.setModalVisibleFalse();
			});
		}
	};

	setModalVisibleFalse = () => {
		this.setState({ modalVisible: false, okButtonDisabled: false });
	};

	// biome-ignore lint/suspicious/noExplicitAny: Poor type implementation from library
	saveFormRef = (formRef: any) => {
		if (!formRef) return;
		this.formRef = formRef.props.form;
	};

	handleSearch = (value: string) => {
		this.setState({ filter: value }, async () => await this.getAll());
	};

	getColumnSearchProps = (
		dataIndex: keyof GetMailRecipientOutput,
	): ColumnProps<GetMailRecipientOutput> => ({
		filterDropdown: (props: FilterDropdownProps) => (
			<div style={{ padding: 8 }}>
				<Input
					ref={(node) => {
						if (!node) return;
					}}
					autoFocus
					placeholder={`Search ${dataIndex}`}
					value={props.selectedKeys ? props.selectedKeys[0] : ""}
					onChange={(e) => {
						if (!props.setSelectedKeys) return;
						props.setSelectedKeys(e.target.value ? [e.target.value] : []);
					}}
					onPressEnter={() =>
						this.handleSearchTable(
							props.selectedKeys || [],
							dataIndex,
							props.confirm,
						)
					}
					style={{ width: 188, marginBottom: 8, display: "block" }}
				/>
				<Button
					type="primary"
					onClick={() =>
						this.handleSearchTable(
							props.selectedKeys || [],
							dataIndex,
							props.confirm,
						)
					}
					icon={<SearchOutlined />}
					size="small"
					style={{ width: 90, marginRight: 8 }}
				>
					Search
				</Button>
				<Button
					onClick={() => this.handleReset(dataIndex, props.clearFilters)}
					size="small"
					style={{ width: 90 }}
				>
					Reset
				</Button>
			</div>
		),
		filterIcon: renderSearchIcon,
		onFilter: (value, record) => {
			if (record[dataIndex] != null && record[dataIndex] !== "") {
				return record[dataIndex]
					.toString()
					.toLowerCase()
					.includes(value.toString().toLowerCase());
			}
			return false;
		},
	});

	handleTableChange = (pagination: TablePaginationConfig) => {
		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
			},
			async () => await this.getAll(),
		);
	};

	handleSearchTable = (
		selectedKeys: React.Key[],
		dataIndex: string,
		confirm?: () => void,
	) => {
		if (dataIndex === "title") {
			this.setState(
				{ sorting: selectedKeys[0].toString() },
				async () => await this.getAll(),
			);
			return;
		}
		const allSearches = this.state.searchTextInsideTable;
		const filtred = allSearches.filter((x) => x.index !== dataIndex);

		filtred.push({ index: dataIndex, searchText: selectedKeys[0].toString() });
		if (confirm) confirm();
		this.setState({
			searchTextTable: selectedKeys[0].toString(),
			searchedColumnTable: dataIndex,
			searchTextInsideTable: filtred,
		});
	};

	handleReset = (dataIndex: string, clearFilters?: () => void) => {
		const allSearches = this.state.searchTextInsideTable;
		const filtred = allSearches.filter((x) => x.index !== dataIndex);
		if (clearFilters) clearFilters();
		this.setState({
			searchTextTable: "",
			searchTextInsideTable: filtred,
		});
	};

	public render() {
		const { mailRecipients } = this.props.mailRecipientStore;
		const paginationOptions = getTablePaginationOptions(
			mailRecipients?.totalCount,
		);
		const columns = [
			{
				title: L("Mail Recipient Name"),
				dataIndex: "name",
				key: "name",
				width: 150,
				sorter: (a: GetMailRecipientOutput, b: GetMailRecipientOutput) =>
					a.name?.length - b.name?.length,
			},
			{
				title: L("Email"),
				dataIndex: "email",
				key: "email",
				width: 150,
				sorter: (a: GetMailRecipientOutput, b: GetMailRecipientOutput) =>
					a.email?.length - b.email?.length,
			},
			{
				title: L("Mail Recipient Level"),
				dataIndex: "mailLevel",
				key: "mailLevel",
				width: 150,
				sorter: (a: GetMailRecipientOutput, b: GetMailRecipientOutput) =>
					a.mailLevel?.length - b.mailLevel?.length,
			},
			{
				title: L("Actions"),
				width: 300,
				fixed: "right" as const,
				render: (text: string, item: GetMailRecipientOutput) => (
					<div>
						<Dropdown
							trigger={["click"]}
							overlay={
								<Menu>
									<Menu.Item
										onClick={() => {
											this.createOrUpdateModalOpen({ id: item.id });
										}}
									>
										{L("Edit")}
									</Menu.Item>
									<Menu.Item onClick={() => this.delete({ id: item.id })}>
										{L("Delete")}
									</Menu.Item>
								</Menu>
							}
							placement="bottomLeft"
						>
							<Button type="primary" icon={<SettingOutlined />}>
								{L("Actions")}
							</Button>
						</Dropdown>
					</div>
				),
			},
		];

		const rowSelection: TableRowSelection<GetMailRecipientOutput> = {
			onChange: (_, selectedRows: GetMailRecipientOutput[]) => {
				const selectedRowsItems: EntityDto[] = selectedRows.map((x) => {
					return {
						id: x.id,
					};
				});
				this.setState({
					selectedRows: {
						items: selectedRowsItems,
					},
				});
			},
		};

		return (
			<Card>
				<Row>
					<Col
						xs={{ span: 23, offset: 0 }}
						sm={{ span: 23, offset: 0 }}
						md={{ span: 23, offset: 0 }}
						lg={{ span: 23, offset: 0 }}
						xl={{ span: 23, offset: 0 }}
						xxl={{ span: 23, offset: 0 }}
						style={{ display: "flex", justifyContent: "end" }}
					>
						<Button
							type="primary"
							shape="circle"
							icon={<PlusOutlined />}
							style={{
								display: "flex",
								justifyContent: "center",
								alignItems: "center",
							}}
							onClick={() => this.createOrUpdateModalOpen({ id: 0 })}
						/>
					</Col>
				</Row>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey={(record: GetMailRecipientOutput) => record.id.toString()}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={mailRecipients === undefined}
							dataSource={
								mailRecipients === undefined ? [] : mailRecipients.items
							}
							onChange={this.handleTableChange}
							rowSelection={rowSelection}
							scroll={{ x: true }}
						/>
					</Col>
				</Row>
				<CreateOrUpdateMailRecipients
					mailRecipientsStore={this.props.mailRecipientStore}
					wrappedComponentRef={this.saveFormRef}
					visible={this.state.modalVisible}
					onCancel={() => this.setState({ modalVisible: false })}
					modalType={this.state.id === 0 ? ModalType.create : ModalType.edit}
					onCreate={this.handleCreate}
					okButtonDisabled={this.state.okButtonDisabled}
					roles={this.props.mailRecipientStore.roles}
				/>
				<Chat />
			</Card>
		);
	}
}

export default MailRecipients;
