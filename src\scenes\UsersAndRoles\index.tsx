import * as React from "react";

import { PlusOutlined, SettingOutlined } from "@ant-design/icons";

import {
	<PERSON><PERSON>,
	Card,
	Col,
	Dropdown,
	Input,
	Menu,
	Modal,
	Row,
	Table,
} from "antd";
import { inject, observer } from "mobx-react";

import AppComponentBase from "../../components/AppComponentBase";
import { L } from "../../lib/abpUtility";
import { EntityDto } from "../../services/dto/entityDto";
import Stores from "../../stores/storeIdentifier";
import UserStore from "../../stores/userStore";
import { ModalType } from "../ModalConsts";
import CreateOrUpdateUser from "../Users/<USER>/createOrUpdateUser";
import { getTablePaginationOptions, renderCheckboxValue } from "../renderUtils";
import { ColumnProps, TablePaginationConfig } from "antd/lib/table";
import { GetUserOutput } from "../../services/user/dto/getUserOutput";

export interface IUserProps {
	userStore: UserStore;
}

export interface IUserState {
	modalVisible: boolean;
	maxResultCount: number;
	skipCount: number;
	userId: number;
	filter: string;
}

const confirm = Modal.confirm;
const Search = Input.Search;

@inject(Stores.UserStore)
@observer
class UsersAndRoles extends AppComponentBase<IUserProps, IUserState> {
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	formRef: any;

	state = {
		modalVisible: false,
		maxResultCount: 10,
		skipCount: 0,
		userId: 0,
		filter: "",
	};

	async componentDidMount() {
		await this.getAll();
	}

	async getAll() {
		await this.props.userStore.getAll({
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: this.state.filter,
		});
	}

	handleTableChange = (pagination: TablePaginationConfig) => {
		this.setState(
			{
				// biome-ignore lint/style/noNonNullAssertion: <explanation>
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount!,
				maxResultCount: pagination.pageSize ?? 10,
			},
			async () => await this.getAll(),
		);
	};

	Modal = () => {
		this.setState({
			modalVisible: !this.state.modalVisible,
		});
	};

	async createOrUpdateModalOpen(entityDto: EntityDto) {
		if (entityDto.id === 0) {
			await this.props.userStore.createUser();
			await this.props.userStore.getRoles();
		} else {
			await this.props.userStore.get(entityDto);
			await this.props.userStore.getRoles();
		}

		this.setState({ userId: entityDto.id });
		this.Modal();

		this.formRef.props.form.setFieldsValue({
			...this.props.userStore.editUser,
			roleNames: this.props.userStore.editUser.roleNames,
		});
	}

	delete(input: EntityDto) {
		const self = this;
		confirm({
			title: "Do you Want to delete these items?",
			onOk() {
				self.props.userStore.delete(input);
			},
			onCancel() {
				console.log("Cancel");
			},
		});
	}

	handleCreate = () => {
		const form = this.formRef.props.form;

		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
		form.validateFields(async (err: any, values: any) => {
			if (err) {
				return;
			}
			if (this.state.userId === 0) {
				await this.props.userStore.create(values);
			} else {
				await this.props.userStore.update({
					id: this.state.userId,
					...values,
				});
			}

			await this.getAll();
			this.setState({ modalVisible: false });
			form.resetFields();
		});
	};

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	saveFormRef = (formRef: any) => {
		this.formRef = formRef;
	};

	handleSearch = (value: string) => {
		this.setState({ filter: value }, async () => await this.getAll());
	};

	public render() {
		const { users } = this.props.userStore;
		const paginationOptions = getTablePaginationOptions(users?.totalCount);
		const columns: Array<ColumnProps<GetUserOutput>> = [
			{
				title: L("UserName"),
				dataIndex: "userName",
				key: "userName",
				width: 150,
				render: (text: string) => <div>{text}</div>,
			},
			{
				title: L("FullName"),
				dataIndex: "name",
				key: "name",
				width: 150,
				render: (text: string) => <div>{text}</div>,
			},
			{
				title: L("EmailAddress"),
				dataIndex: "emailAddress",
				key: "emailAddress",
				width: 150,
				render: (text: string) => <div>{text}</div>,
			},
			{
				title: L("IsActive"),
				dataIndex: "isActive",
				key: "isActive",
				width: 150,
				render: renderCheckboxValue,
			},
			{
				title: L("Actions"),
				width: 150,
				render: (text: string, item) => (
					<div>
						<Dropdown
							trigger={["click"]}
							overlay={
								<Menu>
									<Menu.Item
										onClick={() =>
											this.createOrUpdateModalOpen({ id: item.id })
										}
									>
										{L("Edit")}
									</Menu.Item>
									<Menu.Item onClick={() => this.delete({ id: item.id })}>
										{L("Delete")}
									</Menu.Item>
								</Menu>
							}
							placement="bottomLeft"
						>
							<Button type="primary" icon={<SettingOutlined />}>
								{L("Actions")}
							</Button>
						</Dropdown>
					</div>
				),
			},
		];

		return (
			<Card>
				<Row>
					<Col
						xs={{ span: 10, offset: 0 }}
						sm={{ span: 10, offset: 0 }}
						md={{ span: 10, offset: 0 }}
						lg={{ span: 10, offset: 0 }}
						xl={{ span: 10, offset: 0 }}
						xxl={{ span: 10, offset: 0 }}
					>
						<Search
							placeholder={this.L("Filter")}
							onSearch={this.handleSearch}
						/>
					</Col>
					<Col
						xs={{ span: 13, offset: 0 }}
						sm={{ span: 13, offset: 0 }}
						md={{ span: 13, offset: 0 }}
						lg={{ span: 13, offset: 0 }}
						xl={{ span: 13, offset: 0 }}
						xxl={{ span: 13, offset: 0 }}
						style={{ display: "flex", justifyContent: "right" }}
					>
						<Button
							type="primary"
							shape="circle"
							icon={<PlusOutlined />}
							onClick={() => this.createOrUpdateModalOpen({ id: 0 })}
						/>
					</Col>
				</Row>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey={(record) => record.id.toString()}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={users === undefined}
							dataSource={users === undefined ? [] : users.items}
							onChange={this.handleTableChange}
						/>
					</Col>
				</Row>
				<CreateOrUpdateUser
					wrappedComponentRef={this.saveFormRef}
					visible={this.state.modalVisible}
					loading={false}
					onCancel={() =>
						this.setState({
							modalVisible: false,
						})
					}
					modalType={
						this.state.userId === 0 ? ModalType.edit : ModalType.create
					}
					onCreate={this.handleCreate}
					roles={this.props.userStore.roles}
				/>
			</Card>
		);
	}
}

export default UsersAndRoles;
