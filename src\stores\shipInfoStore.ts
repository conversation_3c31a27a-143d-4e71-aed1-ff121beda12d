import { action, observable } from "mobx";
import { EntityDto } from "../services/dto/entityDto";
import type { PagedResultDto } from "../services/dto/pagedResultDto";
import type { CreateOrUpdateShipInfoInput } from "../services/shipInfo/dto/CreateOrUpdateShipInfoInput";
import { GetShipInfoOutput } from "../services/shipInfo/dto/GetShipInfoOutput";
import type { PagedFilterAndSortedRequestShipInfo } from "../services/shipInfo/dto/PagedFilterAndSortedRequestShipInfo";
import type { UpdateShipInfoInput } from "../services/shipInfo/dto/updateShipInfoInput";
import shipInfoService from "../services/shipInfo/shipInfoService";

class ShipInfoStore {
	@observable shipsInfo!: PagedResultDto<GetShipInfoOutput>;
	@observable editShipInfo!: CreateOrUpdateShipInfoInput;

	@action
	async create(createOrUpdateShipInput: CreateOrUpdateShipInfoInput) {
		const result = await shipInfoService.create(createOrUpdateShipInput);
		this.shipsInfo.items.push(result);
	}

	@action
	async update(updateShipInfoInput: UpdateShipInfoInput) {
		const result = await shipInfoService.update(updateShipInfoInput);
		this.shipsInfo.items = this.shipsInfo.items.map((x: GetShipInfoOutput) => {
			if (x.id === updateShipInfoInput.id) x = result;
			return x;
		});
	}

	@action
	async delete(entityDto: EntityDto) {
		await shipInfoService.delete(entityDto);
		this.shipsInfo.items = this.shipsInfo.items.filter(
			(x: GetShipInfoOutput) => x.id !== entityDto.id,
		);
	}

	@action
	async get(entityDto: EntityDto) {
		const result = await shipInfoService.get(entityDto);
		this.editShipInfo = result;
	}

	@action
	async getAll(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequestShipInfo,
	) {
		const result = await shipInfoService.getAll(pagedFilterAndSortedRequest);
		this.shipsInfo = result;
	}
}

export default ShipInfoStore;
