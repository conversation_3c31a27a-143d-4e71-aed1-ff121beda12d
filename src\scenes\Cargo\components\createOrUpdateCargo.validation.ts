import { ValidationRule } from "@ant-design/compatible/lib/form";
import { L } from "../../../lib/abpUtility";

const rules: Record<string, ValidationRule[]> = {
	shipName: [{ required: true, message: L("Please select ship") }],
	dateOfTransport: [
		{ required: true, message: L("Please select date of transport") },
	],
	cargo: [
		{ required: true, message: L("Please input cargo quantity") },
		{
			validator(rule, value, callback, source, options) {
				if (!value || value === "") return Promise.resolve();
				if (Number.isNaN(value) || Number.isNaN(Number.parseInt(value))) {
					return Promise.reject("Must be a valid number");
				}

				if (Number.parseInt(value) < 0) {
					return Promise.reject("Must be a non negative value");
				}

				return Promise.resolve();
			},
		},
	],
};

export default rules;
