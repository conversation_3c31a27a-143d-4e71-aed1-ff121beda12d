import "./AppLayout.less";
import { Layout } from "antd";
import * as React from "react";
import DocumentTitle from "react-document-title";
import { Redirect, Switch } from "react-router-dom";
import Header from "../../components/Header";
import ProtectedRoute from "../../components/Router/ProtectedRoute";
import utils from "../../utils/utils";
import LazyImage from "../LazyImage/LazyImage";
import { appRouters } from "../Router/router.config";
import SiderMenu from "../SiderMenu";

const { Content } = Layout;

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
declare let abp: any;

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
class AppLayout extends React.Component<any> {
	render() {
		const {
			history,
			location: { pathname },
		} = this.props;

		const isHome = pathname === "/home";

		const layout = (
			<Layout
				style={{ minHeight: "100vh", maxWidth: "100vw", overflowX: "hidden" }}
			>
				{isHome && (
					<LazyImage
						placeholderSrc={"/background-small.png"}
						placeholderStyle={{ filter: "blur(25px)" }}
						src={"/background.png"}
						style={{ position: "absolute", minHeight: "100vh", width: "100%" }}
					/>
				)}
				<SiderMenu history={history} />
				<Layout style={{ background: isHome ? "transparent" : "" }}>
					<Layout.Header
						style={{
							background: "inherit",
							minHeight: "fit-content",
							padding: 0,
						}}
					>
						<Header titlePage={utils.getPageHeader(pathname)} />
					</Layout.Header>
					<Content style={{ margin: 16, zIndex: 1 }}>
						<Switch>
							{appRouters
								.filter((item) => !item.isLayout)
								.map((route) => (
									<ProtectedRoute
										key={route.path}
										path={route.path}
										component={route.component}
										permission={route.permission}
									/>
								))}

							{abp.session.userId === 1 ? null : (
								<Redirect from="/" to="/home" />
							)}
						</Switch>
					</Content>
				</Layout>
			</Layout>
		);

		return (
			<DocumentTitle title={utils.getPageTitle(pathname)}>
				{layout}
			</DocumentTitle>
		);
	}
}

export default AppLayout;
