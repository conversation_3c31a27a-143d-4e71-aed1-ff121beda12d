import Form, { FormComponentProps } from "@ant-design/compatible/lib/form";
import FormItem from "antd/lib/form/FormItem";
import * as React from "react";
import ComponentLayout from "../../../components/Layout/ComponentLayout";
import { L } from "../../../lib/abpUtility";
import DeviceStore from "../../../stores/deviceStore";
import { FormWidths } from "../../ViewSettingsConsts";
import { deviceStatusesCustomData } from "../../enumUtils";
import rules from "./createOrUpdateDevice.validation";
import { Modal, Select } from "antd";

const { Option } = Select;

export interface ICreateOrUpdateDeviceRegistrationProps
	extends FormComponentProps {
	visible: boolean;
	loading: boolean;
	onCancel: () => void;
	modalType: string;
	onCreate: () => void;
	deviceStore: DeviceStore;
}

class CreateOrUpdateDeviceRegistration extends React.Component<ICreateOrUpdateDeviceRegistrationProps> {
	render() {
		const { getFieldDecorator } = this.props.form;
		const { visible, onCancel, onCreate } = this.props;
		const deviceStatuses = deviceStatusesCustomData;

		return (
			<Modal
				visible={visible}
				cancelText={L("Cancel")}
				okText={L("OK")}
				onCancel={onCancel}
				onOk={onCreate}
				title={"Device"}
				okButtonProps={{ loading: this.props.loading }}
			>
				<Form layout="vertical">
					<FormItem
						label={L("Status")}
						{...ComponentLayout.formItemLayout}
						labelAlign={"left"}
					>
						{getFieldDecorator("status", {
							rules: rules.status,
							initialValue: this.props.form.getFieldValue("status") ?? 0,
						})(
							<Select
								{...ComponentLayout.formItemLayout}
								showSearch
								style={{ width: FormWidths.wide }}
								placeholder={""}
								optionFilterProp="children"
								filterOption={(input, option) =>
									option?.props ?
									option.props.children
										.toLowerCase()
										.indexOf(input.toLocaleLowerCase()) > 0
										:
										false
								}
							>
								{[...deviceStatuses].map(([key, value], i) => (
									<Option key={key} value={key}>
										{value}
									</Option>
								))}
							</Select>,
						)}
					</FormItem>
				</Form>
			</Modal>
		);
	}
}

export default Form.create<ICreateOrUpdateDeviceRegistrationProps>()(
	CreateOrUpdateDeviceRegistration,
);
