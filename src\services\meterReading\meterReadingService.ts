import { EntityDto } from "../dto/entityDto";
import { ListResultDto, PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoints";
import httpCsv from "../httpCsvService";
import http from "../httpService";
import { PagedMeterReadingResultRequestDto } from "./dto/PagedMeterReadingResultRequestDto";
import { CreateOrUpdateMeterReadingInput } from "./dto/createOrUpdateMeterReadingInput";
import { GetFaopPerformance } from "./dto/getFaopPerformance";
import { GetFaultLogs } from "./dto/getFaultLogs";
import { GetLastMeterReadings } from "./dto/getLastMeterReadings";
import { GetMeterReadingOutput } from "./dto/getMeterReadingOutput";
import { GetMovingAverageOutput } from "./dto/getMovingAverageOutput";
import { MeterReadingCountPerDayForShipOutDto } from "./dto/meterReadingCountPerDayForShipOutDto";
import { sendCsvDto } from "./dto/sendCsvDto";
import { SyncInformationMeterReadingDto } from "./dto/syncInformationMeterReadingDto";
import { UpdateValue } from "./dto/updateValue";
class MeterReadingService {
	public async getTenantSummary() {
		const result = await http.get(Endpoint.MeterReading.GetTenantSummary);
		return result.data.result;
	}

	public async create(
		createMeterReadingInput: CreateOrUpdateMeterReadingInput,
	) {
		const result = await http.post(
			Endpoint.MeterReading.Create,
			createMeterReadingInput,
		);
		return result.data.result;
	}

	public async update(
		updateMeterReadingInput: CreateOrUpdateMeterReadingInput,
	) {
		const result = await http.put(
			Endpoint.MeterReading.Update,
			updateMeterReadingInput,
		);
		return result.data.result;
	}

	public async updateValue(updateMeterReadingInput: UpdateValue) {
		const result = await http.put(
			Endpoint.MeterReading.UpdateValue,
			updateMeterReadingInput,
		);
		return result.data.result;
	}

	public async delete(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.MeterReading.Delete, {
			params: entityDto,
		});
		return result.data;
	}

	public async deleteRange(entities: ListResultDto<EntityDto>) {
		const result = await http.post(Endpoint.MeterReading.DeleteRange, entities);
		return result.data;
	}

	public async get(
		entityDto: EntityDto,
	): Promise<CreateOrUpdateMeterReadingInput> {
		const result = await http.get(Endpoint.MeterReading.Get, {
			params: entityDto,
		});
		return result.data.result;
	}

	public async getFilters(
		filters: { keyword: string; searchColumn: string },
		property: string,
	): Promise<string[]> {
		const result = await http.get(Endpoint.MeterReading.Filters, {
			params: { ...filters, propertyName: property },
		});
		return result.data.result;
	}

	public async getAll(
		pagedFilterAndSortedRequest: PagedMeterReadingResultRequestDto,
	): Promise<PagedResultDto<GetMeterReadingOutput>> {
		const result = await http.get(Endpoint.MeterReading.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}

	public async getAllIntegrationSyncMeterReading(
		entityDto: EntityDto,
	): Promise<PagedResultDto<SyncInformationMeterReadingDto>> {
		const result = await http.get(
			Endpoint.MeterReading.GetAllIntegrationSyncMeterReading,
			{ params: entityDto },
		);
		return result.data.result;
	}

	public async getMeterReadingCountPerDayForAllShips(): Promise<
		MeterReadingCountPerDayForShipOutDto[]
	> {
		const result = await http.get(
			Endpoint.MeterReading.GetMeterReadingCountPerDayForAllShips,
		);
		return result.data.result;
	}

	public async getMovingAverage(): Promise<GetMovingAverageOutput> {
		const result = await http.get(Endpoint.MeterReading.GetMovingAverage);
		return result.data.result;
	}

	public async getLastMeterReadings(): Promise<Array<GetLastMeterReadings>> {
		const result = await http.get(Endpoint.MeterReading.GetLastMeterReadings);
		return result.data.result;
	}

	public async getFaopPerformance(
		dayPeriod: number,
	): Promise<Array<GetFaopPerformance>> {
		const result = await http.get(Endpoint.MeterReading.GetFaopPerformance, {
			params: { DaysPeriod: dayPeriod },
		});
		return result.data.result;
	}

	public async getFaultLogs(dayPeriod: number): Promise<Array<GetFaultLogs>> {
		const result = await http.get(Endpoint.MeterReading.GetFaultLogs, {
			params: { DaysPeriod: dayPeriod },
		});
		return result.data.result;
	}

	public async getMeterReadingValidationTypes() {
		const result = await http.get(
			Endpoint.MeterReading.GetMeterReadingValidationTypes,
		);
		return result.data.result;
	}

	public async downloadCsv(entities: ListResultDto<EntityDto>) {
		const result = await httpCsv.post(Endpoint.MeterReading.SendCsv, entities);
		return result.data.result;
	}

	public async downloadAllCsv(filter: sendCsvDto) {
		const result = await httpCsv.post(Endpoint.MeterReading.SendAllCsv, filter);
		return result.data.result;
	}

	public async getReadingGsStatuses() {
		const result = await http.get(Endpoint.MeterReading.GetReadingGsStatuses);
		return result.data.result;
	}
	public async getScanTriggerReasons() {
		const result = await http.get(Endpoint.MeterReading.GetScanTriggerReasons);
		return result.data.result;
	}

	public async getMeterNames() {
		const result = await http.get(Endpoint.MeterConfiguration.GetMeterNames);
		return result.data.result;
	}

	public async resendToGs(entities: ListResultDto<EntityDto>) {
		const result = await http.post(Endpoint.MeterReading.ReSendToGs, entities);
		return result.data.result;
	}

	public async download(from?: string, to?: string): Promise<string> {
		const result = await http.post(Endpoint.MeterReading.Download, undefined, {
			params: { from, to },
		});
		return result.data.result;
	}

	public async toggleStatus(id: EntityDto["id"]) {
		await http.post(Endpoint.MeterReading.ToggleStatus, undefined, {
			params: { readingId: id },
		});
	}
}

export default new MeterReadingService();
