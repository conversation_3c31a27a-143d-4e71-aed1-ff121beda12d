import { EntityDto } from "../../dto/entityDto";

export interface DraftDto extends EntityDto {
	timezone: string;
	uuid: string;
	addingDate: string;
	forwardDraft: number;
	aftDraft: number;
	draftVoyageStatus: DraftVoyageStatus;
	serverStatus: DraftSendStatus;
	gsStatus: GsStatus;
	shipName: string;
	callSign: string;
	voyageNumber: string;
}

export enum DraftVoyageStatus {
	StartVoyage = 1,
	InVoyage = 2,
	EndVoyage = 3,
}

export enum DraftSendStatus {
	Sent = 1,
	Unsent = 2,
}

export enum GsStatus {
	NotDefined = 0,
	Sent = 1,
	Unsent = 2,
}
