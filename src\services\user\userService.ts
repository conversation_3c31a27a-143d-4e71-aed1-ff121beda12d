import { EntityDto } from "../../services/dto/entityDto";
import { PagedResultDto } from "../../services/dto/pagedResultDto";
import Endpoint from "../endpoints";
import http from "../httpService";
import { PagedUserResultRequestDto } from "./dto/PagedUserResultRequestDto";
import { ChangeLanguagaInput } from "./dto/changeLanguageInput";
import { CreateOrUpdateUserInput } from "./dto/createOrUpdateUserInput";
import { GetAllUserOutput } from "./dto/getAllUserOutput";
import { UpdateUserInput } from "./dto/updateUserInput";

class UserService {
	public async create(createUserInput: CreateOrUpdateUserInput) {
		const result = await http.post(Endpoint.User.Create, createUserInput);
		return result.data.result;
	}

	public async update(updateUserInput: UpdateUserInput) {
		const result = await http.put(Endpoint.User.Update, updateUserInput);
		return result.data.result;
	}

	public async delete(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.User.Delete, {
			params: entityDto,
		});
		return result.data;
	}

	public async getRoles() {
		const result = await http.get(Endpoint.User.GetRoles);
		return result.data.result.items;
	}

	public async changeLanguage(changeLanguageInput: ChangeLanguagaInput) {
		const result = await http.post(
			Endpoint.User.ChangeLanguage,
			changeLanguageInput,
		);
		return result.data;
	}

	public async get(entityDto: EntityDto): Promise<CreateOrUpdateUserInput> {
		const result = await http.get(Endpoint.User.Get, { params: entityDto });
		return result.data.result;
	}

	public async getAll(
		pagedFilterAndSortedRequest: PagedUserResultRequestDto,
		role?: string,
	): Promise<PagedResultDto<GetAllUserOutput>> {
		const result = await http.get(Endpoint.User.GetAll, {
			params: { ...pagedFilterAndSortedRequest, role },
		});
		return result.data.result;
	}

	public async getCharteres(
		pagedFilterAndSortedRequest: PagedUserResultRequestDto,
	): Promise<PagedResultDto<GetAllUserOutput>> {
		const result = await http.get(Endpoint.User.GetCharteres, {
			params: { ...pagedFilterAndSortedRequest },
		});
		return result.data.result;
	}
}

export default new UserService();
