import { PagedResultDto } from "../../services/dto/pagedResultDto";
import { EntityDto } from "../dto/entityDto";
import { PagedFilterAndSortedRequest } from "../dto/pagedFilterAndSortedRequest";
import http from "../httpService";
import { GetLicenseOutput } from "./dto/getLicenseOutput";
import { LicenceTenantDto, ManyLicenceTenantDto } from "./dto/licenceTenantDto";

class LicenseService {
	public async create(createLicenseInput: GetLicenseOutput) {
		const result = await http.post(
			"/api/services/app/LicenceManagerService/Create",
			createLicenseInput,
		);
		return result.data.result;
	}

	public async get(entityDto: EntityDto): Promise<GetLicenseOutput> {
		const result = await http.get(
			"api/services/app/LicenceManagerService/Get",
			{
				params: entityDto,
			},
		);
		return result.data.result;
	}

	public async getAll(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequest,
	): Promise<PagedResultDto<GetLicenseOutput>> {
		const result = await http.get(
			"api/services/app/LicenceManagerService/GetAll",
			{ params: pagedFilterAndSortedRequest },
		);
		return result.data.result;
	}

	public async delete(entityDto: EntityDto) {
		const result = await http.delete(
			"/api/services/app/LicenceManagerService/Delete",
			{ params: entityDto },
		);
		return result.data;
	}

	public async update(licence: GetLicenseOutput) {
		const result = await http.put(
			"api/services/app/LicenceManagerService/Update",
			licence,
		);
		return result.data.result;
	}

	public async getAssignedLicences(
		entityDto: EntityDto,
	): Promise<PagedResultDto<GetLicenseOutput>> {
		const result = await http.get(
			"/api/services/app/LicenceManagerService/GetAssignedLicences",
			{ params: { TenantId: entityDto.id } },
		);
		return result.data.result;
	}

	public async assignLicence(
		manyLicenceTenant: ManyLicenceTenantDto,
	): Promise<void> {
		await http.post(
			"/api/services/app/LicenceManagerService/AssignLicence",
			manyLicenceTenant,
		);
	}

	public async unassignLicence(licenceTenant: LicenceTenantDto): Promise<void> {
		const path = "/api/services/app/LicenceManagerService/RemoveLicence";
		await http.delete(path, { params: licenceTenant });
	}
}

export default new LicenseService();
