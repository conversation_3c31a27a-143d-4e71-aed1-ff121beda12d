import { GetUserOutput } from "../../user/dto/getUserOutput";

export interface GetPassagesOutput {
	id: number;
	cO2: number;
	cargoQuantity: number;
	shipName: string;
	imoNumber: string;
	voyageNumber: string;
	shipRental: {
		counterParty: GetUserOutput;
		ship: {
			imoNumber: string;
			mmsi: string;
			shipName: string;
		};
	};
	description: string;
	shipRentalId: number;
	departedFromPort: {
		name: string;
	};
	arrivedAtPort: {
		name: string;
	};
	destination: string;
	departTime: Date;
	arrivalTime: Date;
	passageTime: number;
	distanceTravelled: number;
	fuelConsumption: {
		consumption24h: string;
		consumptionPerHour: string;
		consumptionTotal: string;
	};
	isActive: boolean;
}
