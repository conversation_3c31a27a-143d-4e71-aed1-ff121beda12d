import { Modal } from "antd";
import { action, observable } from "mobx";
import customerService from "../services/customer/customerService";
import type { CustomerDto } from "../services/customer/dto/customerDto";
import type { CustomerModel } from "../services/customer/dto/customerModel";
import { EntityDto } from "../services/dto/entityDto";

class CustomerStore {
	@observable customer!: CustomerDto;
	@observable model!: CustomerModel;

	@action
	public async create(customerDto: CustomerDto) {
		const response = await customerService.create(customerDto);
		this.customer = response;
		this.responseInformation(response);
	}

	@action
	public async update(customerDto: CustomerDto) {
		const response = await customerService.update(customerDto);
		this.customer = response;
		this.responseInformation(response);
	}

	@action
	public async deleteByTenant(entity: EntityDto) {
		this.responseInformation(await customerService.deleteByTenant(entity));
	}

	@action
	public createCustomer() {
		this.model = {
			customerId: 0,
			customerName: "",
			customerSynch: false,
		};
	}

	@action
	public async getByTenantId(entityDto: EntityDto) {
		const customer = await customerService.getByTenantId(entityDto);
		if (customer !== undefined) {
			this.model = {
				customerId: customer.id,
				customerName: customer.name,
				customerSynch: customer.isSynchToGs,
			};
		}
	}

	private responseInformation(result: any) {
		if (result.message !== "" && result.message !== undefined) {
			Modal.error({ title: "Error", content: result.message });
		}
	}
}

export default CustomerStore;
