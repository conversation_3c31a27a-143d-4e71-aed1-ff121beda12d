import AccountStore from "./accountStore";
import AisStore from "./aisStore";
import AnswersForEventsStore from "./answersForEventsStore";
import AuthenticationStore from "./authenticationStore";
import BunkeringNotesStore from "./bunkeringNotesStore";
import CargoStore from "./cargoStore";
import ConsumptionCalculationStore from "./consumptionCalculationStore";
import ConsumptionConsumerStore from "./consumptionConsumerStore";
import ConsumptionResultStore from "./consumptionResultStore";
import CustomerStore from "./customerStore";
import DataProviderStore from "./dataProviderStore";
import DataSourceStore from "./dataSourceStore";
import DeviceStore from "./deviceStore";
import DraftStore from "./draftStore";
import EventsStore from "./eventsStore";
import FuelTypeStore from "./fuelTypeStore";
import IntegrationStore from "./integrationStore";
import IntegratorStore from "./integratorStore";
import LicenseStore from "./licenseStore";
import MailRecipientStore from "./mailRecipientStore";
import MapStore from "./mapStore";
import MeterConfigurationStore from "./meterConfigurationStore";
import MeterReadingSetStore from "./meterReadingSetStore";
import MeterReadingStore from "./meterReadingStore";
import MeterStore from "./meterStore";
import NotificationsStore from "./notificationsStore";
import OcrStore from "./ocrStore";
import PassagesStore from "./passagesStore";
import PortStore from "./portStore";
import RoleStore from "./roleStore";
import SessionStore from "./sessionStore";
import ShipInfoStore from "./shipInfoStore";
import ShipRentalStore from "./shipRentalStore";
import ShipStore from "./shipStore";
import TenantStore from "./tenantStore";
import UserStore from "./userStore";
import VoyageStore from "./voyageStore";

export default function initializeStores() {
	return {
		authenticationStore: new AuthenticationStore(),
		roleStore: new RoleStore(),
		tenantStore: new TenantStore(),
		userStore: new UserStore(),
		sessionStore: new SessionStore(),
		accountStore: new AccountStore(),
		meterReadingStore: new MeterReadingStore(),
		meterConfigurationStore: new MeterConfigurationStore(),
		meterReadingSetStore: new MeterReadingSetStore(),
		shipStore: new ShipStore(),
		shipInfoStore: new ShipInfoStore(),
		shipRentalStore: new ShipRentalStore(),
		cargoStore: new CargoStore(),
		passagesStore: new PassagesStore(),
		dataSourceStore: new DataSourceStore(),
		dataProviderStore: new DataProviderStore(),
		eventsStore: new EventsStore(),
		notificationsStore: new NotificationsStore(),
		answersForEventsStore: new AnswersForEventsStore(),
		meterStore: new MeterStore(),
		voyageStore: new VoyageStore(),
		deviceStore: new DeviceStore(),
		mailRecipientStore: new MailRecipientStore(),
		licenseStore: new LicenseStore(),
		consumptionConsumerStore: new ConsumptionConsumerStore(),
		consumptionResultStore: new ConsumptionResultStore(),
		draftStore: new DraftStore(),
		customerStore: new CustomerStore(),
		ocrStore: new OcrStore(),
		integrationStore: new IntegrationStore(),
		integratorStore: new IntegratorStore(),
		fuelTypeStore: new FuelTypeStore(),
		consumptionCalculationStore: new ConsumptionCalculationStore(),
		bunkeringNotesStore: new BunkeringNotesStore(),
		aisStore: new AisStore(),
		mapStore: new MapStore(),
		portStore: new PortStore(),
	};
}
