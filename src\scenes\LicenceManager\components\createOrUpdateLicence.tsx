import * as React from "react";

import { Form } from "@ant-design/compatible";
import "@ant-design/compatible/assets/index.css";

import { Input, Modal, Tabs } from "antd";

import { FormComponentProps } from "@ant-design/compatible/lib/form";
import FormItem from "antd/lib/form/FormItem";
import ComponentLayout from "../../../components/Layout/ComponentLayout";
import { L } from "../../../lib/abpUtility";
import { ModalType } from "../../ModalConsts";

const TabPane = Tabs.TabPane;

export interface ICreateOrUpdateLicenceProps extends FormComponentProps {
	visible: boolean;
	onCancel: () => void;
	modalType: string;
	onCreate: () => void;
	okButtonDisabled: boolean;
}

class CreateOrUpdateLicence extends React.Component<ICreateOrUpdateLicenceProps> {
	state = {};

	render() {
		const { getFieldDecorator } = this.props.form;

		return (
			<Modal
				visible={this.props.visible}
				cancelText={L("Cancel")}
				okText={L("OK")}
				onCancel={this.props.onCancel}
				title={L("Licences")}
				onOk={this.props.onCreate}
				okButtonProps={{ disabled: this.props.okButtonDisabled }}
			>
				<Tabs defaultActiveKey={"role"} size={"small"} tabBarGutter={64}>
					<TabPane tab={L("LicenceDetails")} key={"role"}>
						<FormItem
							label={L("LicenceKey")}
							{...ComponentLayout.formItemLayout}
						>
							{getFieldDecorator("key")(
								<Input disabled={this.props.modalType === ModalType.edit} />,
							)}
						</FormItem>
						<FormItem
							label={L("LicenceDescription")}
							{...ComponentLayout.formItemLayout}
						>
							{getFieldDecorator("description")(<Input />)}
						</FormItem>
					</TabPane>
				</Tabs>
			</Modal>
		);
	}
}

export default Form.create<ICreateOrUpdateLicenceProps>()(
	CreateOrUpdateLicence,
);
