.trigger {
    font-size: 18px;
    line-height: 64px;
    padding: 0 24px;
    cursor: pointer;
    transition: color 0.3s;
}

.trigger:hover {
    color: #1890ff;
}

.logo {
    height: 32px;
    background: rgba(255, 255, 255, 0.2);
    margin: 16px;
}

.sidebar {
  max-height: calc(100vh - 40px);
  overflow-y: auto;
  height: fit-content;
}

.sidebar, .ant-layout-sider, .ant-layout-sider-dark {
    margin: 20px;
    box-shadow: 0px 2px 4px 0px #00000040;
    border-radius: 12px;
    background-color: #F9F8F8;

    .ant-layout-sider-children {
        background-color: #F9F8F8;
        border-radius: 12px;
    
        .ant-menu-dark {
            background-color: #F9F8F8;

            .ant-menu-item {
                display: flex;
                align-items: center;

                svg {
                    margin-right: 10px;
                }
            }
        
            .ant-menu-item,
            .ant-menu-item a,
            .ant-menu-item svg,
            .ant-menu-submenu-title {
              color: #3F3F3F;
            }
          
            .ant-menu-item-selected {
              background-color: #F9F8F8;

              svg {
                color: #A9253B;
                font-weight: bold;
              }

              span {
                color: #A9253B;
                font-weight: bold;
              }
            }
          
            .ant-menu-item:hover {
              background-color: #EDEDED;
              color: #3F3F3F;
            }
          }
          
          .sidebar .ant-menu {
            background-color: #F9F8F8;
            .ant-menu-item,
            .ant-menu-item a,
            .ant-menu-submenu-title {
              color: #3F3F3F;
            }
          }
    }
}


/* Let's get this party started */
::-webkit-scrollbar {
    width: 12px;
}

/* Track */
::-webkit-scrollbar-track {
  -webkit-border: 1px solid #E1E1E1;
  border: 1px solid #E1E1E1;
    -webkit-border-radius: 10px;
    border-radius: 10px;
}

/* Handle */
::-webkit-scrollbar-thumb {
    -webkit-border-radius: 10px;
    border-radius: 10px;
    background: #D8E6FF;
    -webkit-border: 1px solid #E1E1E1;
    border: 1px solid #E1E1E1;
}

::-webkit-scrollbar-thumb:window-inactive {
    background: #D8E6FF;
}

.ant-table-thead > tr > th .anticon-filter > svg, .ant-table-thead > tr > th .ant-table-filter-icon > svg {
  color: #BFBFBF;
}