.chat-icon {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    background-color: #6694E5;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 9999;
    display: none;
    
    svg path {
        fill: #fff;
    }
}

.chat-error{
    background-color: rgba(255, 0, 0, 0.308);
    padding: 10px;
}

.chat-container {
    position: fixed;
    bottom: 80px;
    right: 20px;
    width: 350px;
    height: 450px;
    border: 1px solid #ccc;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    background: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.chat-header {
    background: #6694E5;
    color: #fff;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .icons-container {
        display: flex;
        align-items: center;

        .minimize-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            height: 24px;
            width: 24px;
            cursor: pointer;

            .line-icon {
                height: 2px;
                width: 15px;
                background-color: #fff;
            }
        }

        .close-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
    
            svg{
                transform: rotate(45deg);
            }
        }
    }
}

.chat-icon {
    margin-right: 8px;
}

.chat-messages {
    flex: 1;
    padding: 10px;
    overflow-y: auto;

    .chat-message {
        margin: 10px 0;
        width: max-content;
        padding: 5px 10px;
        border-radius: 12px;
        word-wrap: break-word;
        max-width: 250px;

        &.User {
            color: #fff;
            text-align: right;
            margin-left: auto;
            background-color: #6694E5;
        }
        
        &.Assistant {
            background-color: #d8e6ff;
        }
    }
}

.loader-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.center-loader {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.bottom-loader {
    margin-top: 10px;
    justify-content: center;
    align-items: center;
}


.chat-input {
    display: flex;
    padding: 10px;

    input {
        flex: 1;
        padding: 8px;
        border: 1px solid #ccc;
        border-radius: 4px;
        margin-right: 10px;
    }

    button {
        padding: 8px 12px;
        background: #6694E5;
        color: #fff;
        border: none;
        border-radius: 4px;
        cursor: pointer;

        &:hover {
            background: #005a9e;
        }
    }
}