import { Moment } from "moment";
import { AuditedEntityDto } from "../../dto/auditedEntityDto";
import { Errors } from "./errors";

export interface GetMeterReadingOutput extends AuditedEntityDto {
	aiValue: string;
	value: string;
	callSign: string;
	voyageNumber: string;
	meterBarcode: string;
	version: string;
	deviceId: string;
	timeZone: string;
	unixTime: number;
	localTime: Moment;
	validation: number;
	rollover?: number;
	isMadeManually: boolean;
	vesselOperator: string;
	consumerType: number;
	measureType: number;
	fullImageUrl: string;
	readingImageUrl: string;
	meterName: string;
	meterInfromation: string;
	fuelType: string;
	isFuelChanged: boolean;
	measureUnit: number;
	readingGsStatus: number;
	meterUuid: string;
	scanTriggerReason: number;
	fuelDensity?: number;
	fuelCalorific?: number;
	reasonForEvent: string;
	uuid: string;
	meter: Meter;
	fileUrl: string;
	auditMetrics: Errors;
	editValue: string;
	manualValue: string;
	vesselStatus: string;
	scrubber: boolean;
}

interface Meter {
	name: string;
	ship: Ship;
	meterType: string;
	unitOfMeasure: string;
	digitsCount: number;
	decimalPointPosition: number;
	flowDirection: string;
	measurementType: string;
	scheduledReadings: ScheduledReadings;
}

interface Ship {
	shipName: string;
	imoNumber: string;
}

interface ScheduledReadings {
	0: string;
}
