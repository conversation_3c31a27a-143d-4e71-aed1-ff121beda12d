import * as React from "react";

import { Card, Col, Modal, Row, Table } from "antd";
import { inject, observer } from "mobx-react";

import moment from "moment";
import AppComponentBase from "../../components/AppComponentBase";
import { L } from "../../lib/abpUtility";
import draftService from "../../services/draft/draftService";
import {
	DraftDto,
	DraftSendStatus,
	DraftVoyageStatus,
} from "../../services/draft/dto/draftDto";
import { EntityDto } from "../../services/dto/entityDto";
import { ListResultDto } from "../../services/dto/pagedResultDto";
import DraftStore from "../../stores/draftStore";
import MeterReadingStore from "../../stores/meterReadingStore";
import Stores from "../../stores/storeIdentifier";
import {
	getTablePaginationOptions,
	renderDate,
	renderDateFromUtcStringAndTimezone,
} from "../renderUtils";
import { SortOrder, TablePaginationConfig, TableRowSelection } from "antd/lib/table/interface";
import { ColumnProps } from "antd/lib/table";

export interface IDraftProps {
	draftStore: DraftStore;
	meterReadingStore: MeterReadingStore;
}

export interface IDraftState {
	modalVisible: boolean;
	maxResultCount: number;
	skipCount: number;
	consumptionConsumerId: number;
	filter: string;
	sending: boolean;
	selectedRows: ListResultDto<EntityDto>;
}

const confirm = Modal.confirm;
// const Search = Input.Search;

@inject(Stores.DraftStore)
@inject(Stores.MeterReadingStore)
@observer
class Draft extends AppComponentBase<IDraftProps, IDraftState> {
	formRef: any;

	state = {
		modalVisible: false,
		maxResultCount: 10,
		skipCount: 0,
		consumptionConsumerId: 0,
		filter: "",
		sending: false,
		selectedRows: {
			items: [],
		},
	};

	async componentDidMount() {
		await this.getAll();
		await this.props.meterReadingStore.getReadingGsStatuses();
	}

	async getAll() {
		const filterData = {
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: this.state.filter,
		};
		await this.props.draftStore.getAll(filterData);
	}

	handleTableChange = (pagination: TablePaginationConfig) => {
		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
			},
			async () => await this.getAll(),
		);
	};

	Modal = () => {
		this.setState({
			modalVisible: !this.state.modalVisible,
		});
	};

	delete(input: EntityDto) {
		const self = this;
		confirm({
			title: "Do you Want to delete these items?",
			onOk() {
				self.props.draftStore.delete(input);
			},
			onCancel() {
				console.log("Cancel");
			},
		});
	}

	handleSearch = (value: string) => {
		this.setState({ filter: value }, async () => await this.getAll());
	};

	async resendToGs() {
		const self = this;
		const modalPromise = new Promise<boolean>((resolve, rejects) => {
			confirm({
				title:
					this.state.selectedRows.items.length === 1
						? L("DoYouWantToSendThatItem")
						: L("DoYouWantToSendTheseItems"),
				onOk() {
					self.setState({ sending: true });
					self.props.draftStore
						.resendToGs(self.state.selectedRows)
						.then(() => resolve(true));
				},
				onCancel() {
					rejects();
				},
			});
		});
		const result = await modalPromise;
		if (result) {
			this.setState({
				selectedRows: {
					items: [],
				},
				sending: false,
			});
		}
	}


	async downloadSelectedDrafts() {
		draftService.downloadCsv(this.state.selectedRows);
		this.setState({
			selectedRows: {
				items: [],
			},
		});
	}

	async downloadAllDrafts() {
		const filterData = {
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: this.state.filter,
		};
		draftService.downloadAllCsv(filterData);
	}

	public render() {
		const { drafts } = this.props.draftStore;
		const paginationOptions = getTablePaginationOptions(drafts?.totalCount);

		const columns: Array<ColumnProps<DraftDto>> = [
			{
				title: L("ShipName"),
				dataIndex: "shipName",
				key: "shipName",
				width: 200,
				sorter: (a: DraftDto, b: DraftDto) =>
					a.shipName?.length - b.shipName?.length,
			},
			{
				title: L("CallSign"),
				dataIndex: "callSign",
				key: "callSign",
				width: 150,
				sorter: (a: DraftDto, b: DraftDto) =>
					a.callSign?.length - b.callSign?.length,
			},
			{
				title: L("VoyageNumber"),
				dataIndex: "voyageNumber",
				key: "voyageNumber",
				width: 200,
				sorter: (a: DraftDto, b: DraftDto) =>
					a.voyageNumber?.length - b.voyageNumber?.length,
			},
			{
				title: L("AddingDateUtc"),
				dataIndex: "addingDate",
				key: "addingDateUtc",
				width: 150,
				defaultSortOrder: "descend" as SortOrder,
				sorter: (a: DraftDto, b: DraftDto) =>
					moment(a.addingDate).diff(moment(b.addingDate)),
				render: (text: string, record: DraftDto, index: number) =>
					renderDate(text),
			},
			{
				title: L("AddingDate"),
				dataIndex: "addingDate",
				key: "addingDate",
				width: 150,
				sorter: (a: DraftDto, b: DraftDto) =>
					moment(a.addingDate).diff(moment(b.addingDate)),
				render: (text: string, record: DraftDto, index: number) =>
					renderDateFromUtcStringAndTimezone(text, record.timezone),
			},
			{
				title: L("Timezone"),
				dataIndex: "timezone",
				key: "timezone",
				width: 150,
			},
			{
				title: L("Forward Draft"),
				dataIndex: "forwardDraft",
				key: "forwardDraft",
				width: 150,
				defaultSortOrder: "descend" as SortOrder,
				sorter: (a: DraftDto, b: DraftDto) => {
					return a.forwardDraft - b.forwardDraft > 0
						? a.forwardDraft - b.forwardDraft < 0
							? -1
							: 0
						: 1;
				},
			},
			{
				title: L("Aft Draft"),
				dataIndex: "aftDraft",
				key: "aftDraft",
				width: 150,
				defaultSortOrder: "descend" as SortOrder,
				sorter: (a: DraftDto, b: DraftDto) => {
					return a.aftDraft - b.aftDraft > 0
						? a.aftDraft - b.aftDraft < 0
							? -1
							: 0
						: 1;
				},
			},
			{
				title: L("VoyageStatus"),
				dataIndex: "draftVoyageStatus",
				key: "draftVoyageStatus",
				width: 150,
				defaultSortOrder: "descend" as SortOrder,
				sorter: (a: DraftDto, b: DraftDto) =>
					a.draftVoyageStatus - b.draftVoyageStatus,
				render: (status) => {
					return DraftVoyageStatus.EndVoyage === status
						? "End Voyage"
						: DraftVoyageStatus.InVoyage === status
							? "In Voyage"
							: DraftVoyageStatus.StartVoyage === status
								? "Start Voyage"
								: "NaN";
				},
			},
			{
				title: L("ServerStatus"),
				dataIndex: "serverStatus",
				key: "serverStatus",
				width: 150,
				defaultSortOrder: "descend" as SortOrder,
				sorter: (a: DraftDto, b: DraftDto) => a.serverStatus - b.serverStatus,
				render: (status) => {
					return DraftSendStatus.Sent === status
						? "Sent"
						: DraftSendStatus.Unsent === status
							? "Unsent"
							: "NaN";
				},
			},
			// {
			//   title: L('GS Status'),
			//   dataIndex: 'gsStatus',
			//   key: 'gsStatus',
			//   width: 150,
			//   defaultSortOrder: 'descend' as SortOrder,
			//   sorter: (a: DraftDto, b: DraftDto) => a.gsStatus - b.gsStatus,
			//   render: (text: string) => renderDictionaryValue(text, readingGsStatuses),
			//   filters: this.createFilters(readingGsStatuses),
			//   onFilter: (value: string, record: DraftDto) => record.gsStatus.toString() === value,
			// },
		];

		const { selectedRows } = this.state;

		const rowSelection: TableRowSelection<DraftDto> = {
			onChange: (selectedRowKeys, selectedRows: DraftDto[]) => {
				const selectedRowsItems: EntityDto[] = selectedRows.map((x) => {
					return {
						id: x.id,
					};
				});
				console.log(
					`selectedRowKeys: ${selectedRowKeys}`,
					"selectedRows: ",
					selectedRows,
				);
				this.setState({
					selectedRows: {
						items: selectedRowsItems,
					},
				});
			},
			selectedRowKeys: selectedRows?.items?.map((x: DraftDto) =>
				x.id.toString(),
			),
		};

		// const hasSelected = selectedRows.items.length > 0;

		return (
			<Card>
				{/* <Row>
          <Col
            xs={{ span: 4, offset: 0 }}
            sm={{ span: 4, offset: 0 }}
            md={{ span: 4, offset: 0 }}
            lg={{ span: 2, offset: 0 }}
            xl={{ span: 2, offset: 0 }}
            xxl={{ span: 2, offset: 0 }}
          >
          </Col>
          <Col
            xs={{ span: 14, offset: 0 }}
            sm={{ span: 15, offset: 0 }}
            md={{ span: 15, offset: 0 }}
            lg={{ span: 1, offset: 21 }}
            xl={{ span: 1, offset: 21 }}
            xxl={{ span: 1, offset: 21 }}
          ></Col>
        </Row> */}
				{/* <Row>
          <Col sm={{ span: 10, offset: 0 }}>
            <Search placeholder={this.L('Filter')} onSearch={this.handleSearch} />
          </Col>
        </Row> */}
				{/* <Row style={{ marginTop: 20 }}>
          <div className="drafts-header__buttons">
            {/* <Col sm={{ span: 5, offset: 0 }}>
              <div className="drafts-header__buttons__container">
                <Button type="primary" disabled={!hasSelected} onClick={() => this.resendToGs()} >{L('ResendToGs')}</Button>
              </div>
            </Col> */}
				{/* <Col sm={{ span: 5, offset: 0 }}>
              <div className="drafts-header__buttons__container">
                <Row>
                  <Button type="primary" style={{width: 230}} onClick={() => this.downloadAllDrafts()} >{L('DownloadAllDrafts')}</Button>
                </Row>
                <Row style={{ marginTop: 5 }}>
                  <Button type="primary" style={{width: 230}} disabled={!hasSelected} onClick={() => this.downloadSelectedDrafts()} >{L('DownloadSelectedDrafts')}</Button>
                </Row>
              </div>
            </Col>
            <Col sm={{ span: 5, offset: 0 }}>
              <div className="drafts-header__buttons__container drafts-header__buttons__container--reverse">
              </div>
            </Col>
          </div> */}
				{/* // </Row> */}
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey={(record: DraftDto) => record.id.toString()}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={drafts  === undefined}
							dataSource={drafts === undefined ? [] : drafts.items}
							onChange={this.handleTableChange}
							rowSelection={rowSelection}
							scroll={{ x: true }}
						/>
					</Col>
				</Row>
			</Card>
		);
	}
}

export default Draft;
