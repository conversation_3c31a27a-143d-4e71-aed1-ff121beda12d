import { EntityDto } from "../dto/entityDto";
import { PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoints";
import http from "../httpService";
import { FuelTypeDto } from "./dto/fuelTypeDto";
import { PagedMeterResultRequestFuelTypeDto } from "./dto/pagedFilterAndSortedRequestFuelType";

class FuelTypeService {
	public async get(entityDto: EntityDto): Promise<FuelTypeDto> {
		const result = await http.get(Endpoint.FuelType.Get, { params: entityDto });
		return result.data.result;
	}

	public async create(fuelTypeDto: FuelTypeDto) {
		const result = await http.post(Endpoint.FuelType.Create, fuelTypeDto);
		return result.data.result;
	}

	public async getAll(
		pagedFilterAndSortedRequest: PagedMeterResultRequestFuelTypeDto,
	): Promise<PagedResultDto<FuelTypeDto>> {
		const result = await http.get(Endpoint.FuelType.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}
	public async update(updateDto: FuelTypeDto) {
		const result = await http.put(Endpoint.FuelType.Update, updateDto);
		return result.data.result;
	}

	public async getFilters(
		filters: { keyword: string; searchColumn: string },
		property: string,
	): Promise<string[]> {
		const result = await http.get(Endpoint.FuelType.Filters, {
			params: { ...filters, propertyName: property },
		});
		return result.data.result;
	}

	public async delete(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.FuelType.Delete, {
			params: entityDto,
		});
		return result.data;
	}
}

export default new FuelTypeService();
