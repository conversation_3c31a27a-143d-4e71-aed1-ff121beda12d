import { EntityDto } from "../dto/entityDto";
import { PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoints";
import http from "../httpService";
import { CreateOrUpdateEventsInput } from "./dto/CreateOrUpdateEventsInput";
import { PagedFilterAndSortedRequestEvents } from "./dto/PagedFilterAndSortedRequestEvents";
import { RequestMeterReadingsInput } from "./dto/RequestMeterReadingsInput";
import { UpdateEventsInput } from "./dto/UpdateEventsInput";

class EventsService {
	public async create(createEventsInput: CreateOrUpdateEventsInput) {
		const result = await http.post(Endpoint.Events.Create, createEventsInput);
		return result.data.result;
	}

	public async requestMeterReadings(
		requestMeterReadingsInput: RequestMeterReadingsInput,
	) {
		const result = await http.post(
			Endpoint.Events.RequestMeterReadings,
			requestMeterReadingsInput,
		);
		return result.data.result;
	}

	public async update(updateEvents: UpdateEventsInput) {
		const result = await http.put(Endpoint.Events.Update, updateEvents);
		return result.data.result;
	}

	public async delete(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.Events.Delete, {
			params: entityDto,
		});
		return result.data;
	}

	public async get(entityDto: EntityDto): Promise<CreateOrUpdateEventsInput> {
		const result = await http.get(Endpoint.Events.Get, { params: entityDto });
		return result.data.result;
	}

	public async getCargo(shipId: number) {
		const result = await http.get(Endpoint.Events.GetCargo, {
			params: { shipId },
		});
		return result.data.result;
	}

	public async getAll(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequestEvents,
	): Promise<PagedResultDto<CreateOrUpdateEventsInput>> {
		const result = await http.get(Endpoint.Events.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}
}

export default new EventsService();
