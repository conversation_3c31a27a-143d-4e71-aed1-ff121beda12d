import { Input } from "antd";

const ShipInfoDisplay = ({
	title,
	value,
	withoutMaxWidth,
}: { title: string; value: string | number; withoutMaxWidth?: boolean }) => {
	return (
		<div style={{ display: "flex", flex: 1, alignItems: "center" }}>
			<span
				className={withoutMaxWidth ? "data-title-without-max" : "data-title"}
			>
				{title}
			</span>
			<Input
				readOnly
				value={value}
				className={withoutMaxWidth ? "data-value-without-max" : "data-value"}
			/>
		</div>
	);
};

export default ShipInfoDisplay;
