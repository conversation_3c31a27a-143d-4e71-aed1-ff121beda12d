import { Moment } from "moment";

export interface CreateOrUpdateShipRentalInput {
	id: number;
	ship: {
		id: number;
		creationTime: Date;
		creationUserId: number;
		lastModificationTime: Date;
		lastModifierUserId: number;
		uuid: string;
		shipName: string;
		mmsi: string;
		imoNumber: string;
		shipEmail: string;
		captainName: string;
		chiefEngineersName: string;
		engineRoomUnmannedHoursFrom: string;
		engineRoomUnmannedHoursTo: string;
		scrubber: string;
	};
	startDate?: Moment;
	endDate?: Moment;
	counterParty: string;
}
