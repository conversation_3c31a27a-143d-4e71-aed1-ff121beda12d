import "./UserLayout.less";

import * as React from "react";

import { Redirect, Route, Switch } from "react-router-dom";

import { Col } from "antd";
import DocumentTitle from "react-document-title";
import utils from "../../utils/utils";
import Footer from "../Footer";
import LazyImage from "../LazyImage/LazyImage";
import { userRouter } from "../Router/router.config";

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
class UserLayout extends React.Component<any> {
	render() {
		const {
			location: { pathname },
		} = this.props;

		return (
			<DocumentTitle title={utils.getPageTitle(pathname)}>
				<div>
					<LazyImage
						placeholderSrc={"/background-small.png"}
						placeholderStyle={{ filter: "blur(25px)" }}
						src={"/background.png"}
						style={{ position: "absolute", height: "100vh", width: "100vw" }}
					/>
					<Col
						className="container"
						style={{
							background: "transparent",
							justifyContent: "space-between",
						}}
					>
						{/* <div className={"lang"} style={{ position: "absolute" }}>
							<LanguageSelect />
						</div> */}
						<Switch>
							{userRouter
								.filter((item) => !item.isLayout)
								.map((item, index: number) => (
									<Route
										key={item.name}
										path={item.path}
										component={item.component}
									/>
								))}
							<Redirect from="/user" to="/user/login" />
						</Switch>
						<Footer />
					</Col>
				</div>
			</DocumentTitle>
		);
	}
}

export default UserLayout;
