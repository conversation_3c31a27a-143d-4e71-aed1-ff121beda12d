import * as React from "react";
import "./index.less";
import { Tabs } from "antd";
import { inject, observer } from "mobx-react";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import { L } from "../../lib/abpUtility";
import { OcrDto } from "../../services/ocr/dTo/ocrDto";
import OcrStore from "../../stores/ocrStore";
import Stores from "../../stores/storeIdentifier";
import VerifiedTable from "./components/VerifiedTable";
import AfterOcrTable from "./components/afterOcrTable";
import BeforeOcrTable from "./components/beforeOcrTable";
import DeniedTable from "./components/deniedTable";
import MovedTable from "./components/movedTable";

const { TabPane } = Tabs;

export interface IOcrProps {
	ocrStore: OcrStore;
}

export interface IOcrState {
	modalVisible: boolean;
	ocrId: number;
	status: number;
	selectedRow: OcrDto | null;
}

@inject(Stores.OcrStore)
@observer
class Ocr extends AppComponentBase<IOcrProps, IOcrState> {
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	formRef: any;

	state = {
		modalVisible: false,
		ocrId: 0,
		status: 0,
		selectedRow: null as OcrDto | null,
	};

	async fetchAll() {
		await this.props.ocrStore.getAllByStatus({
			status: this.state.status,
		});
	}

	async componentDidUpdate(
		prevProps: Readonly<IOcrProps>,
		prevState: Readonly<IOcrState>,
	): Promise<void> {
		if (prevState.status !== this.state.status) {
			await this.fetchAll();
		}
	}

	async componentDidMount() {
		await this.fetchAll();
	}

	analyzeFile = async (ocrStore: OcrDto) => {
		await this.props.ocrStore?.analyzeFile(ocrStore);
		await this.fetchAll();
	};

	verifyFile = async (files: OcrDto[]) => {
		await this.props.ocrStore?.verifyFile(files);
		await this.fetchAll();
	};

	denyFile = async (ocrStore: OcrDto) => {
		await this.props.ocrStore?.denyFile(ocrStore);
		await this.fetchAll();
	};

	moveFile = async (ocrStore: OcrDto) => {
		await this.props.ocrStore?.moveFile(ocrStore);
		await this.fetchAll();
	};

	Modal = () => {
		this.setState({
			modalVisible: !this.state.modalVisible,
		});
	};

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	saveFormRef = (formRef: any) => {
		this.formRef = formRef;
	};

	public render() {
		return (
			<>
				<Tabs
					defaultActiveKey="1"
					onTabClick={(key: string) => {
						this.setState({
							status: Number.parseInt(key) - 1,
						});
					}}
				>
					<TabPane tab={L("BeforeOCR")} key="1">
						<BeforeOcrTable
							ocrStore={this.props.ocrStore}
							ocr={this.props.ocrStore.ocr ? this.props.ocrStore.ocr : []}
							analyzeFile={this.analyzeFile}
							denyFile={this.denyFile}
						/>
					</TabPane>
					<TabPane tab={L("AfterOCR")} key="2">
						<AfterOcrTable
							selectedRow={this.state.selectedRow}
							ocrStore={this.props.ocrStore}
							verifyFile={this.verifyFile}
						/>
					</TabPane>
					<TabPane tab={L("Verified")} key="3">
						<VerifiedTable
							ocrStore={this.props.ocrStore}
							ocr={this.props.ocrStore.ocr ? this.props.ocrStore.ocr : []}
							moveFile={this.moveFile}
						/>
					</TabPane>
					<TabPane tab={L("Denied")} key="4">
						<DeniedTable
							ocrStore={this.props.ocrStore}
							ocr={this.props.ocrStore.ocr ? this.props.ocrStore.ocr : []}
						/>
					</TabPane>
					<TabPane tab={L("Accepted")} key="5">
						<MovedTable
							ocrStore={this.props.ocrStore}
							ocr={this.props.ocrStore.ocr ? this.props.ocrStore.ocr : []}
						/>
					</TabPane>
				</Tabs>
				<Chat />
			</>
		);
	}
}

export default Ocr;
