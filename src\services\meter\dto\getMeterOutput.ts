import { AuditedEntityDto } from "../../dto/auditedEntityDto";
export interface GetMeterOutput extends AuditedEntityDto {
	barcode: string;
	name: string;
	meterType: number;
	digitsCount: number;
	decimalPoint: number;
	maxDigitsCount: number;
	measureType: number;
	measurementFrequencyPerDay: number;
	shipName: string;
	callSign: string;
	consumerType: number;
	additionalScanHours: string[];
	controlImageUrl: string;
	fuelType: number;
	measureUnit: number;
	uuid: string;
	shipId: number;

	lastValidReadingDate: number;
	lastValidReadingValue: string;
	lastValidReadingUuid: string;

	validationRules: number;
	manualReadingOnly: boolean;
}
