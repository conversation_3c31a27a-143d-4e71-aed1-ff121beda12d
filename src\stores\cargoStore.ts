import { action, observable } from "mobx";
import moment from "moment";
import cargoService from "../services/cargo/cargoService";
import type { CreateOrUpdateCargoInput } from "../services/cargo/dto/CreateOrUpdateCargoInput";
import { GetCargoOutput } from "../services/cargo/dto/GetCargoOutput";
import { GetGroupByCounterparty } from "../services/cargo/dto/GetGroupByCounterparty";
import type { PagedFilterAndSortedRequestCargo } from "../services/cargo/dto/PagedFilterAndSortedRequestCargo";
import type { UpdateCargoInput } from "../services/cargo/dto/UpdateCargoInput";
import { EntityDto } from "../services/dto/entityDto";
import type { PagedResultDto } from "../services/dto/pagedResultDto";
import { GetRoles } from "../services/user/dto/getRolesOuput";

class CargoStore {
	@observable cargos!: PagedResultDto<GetCargoOutput>;
	@observable editCargos!: CreateOrUpdateCargoInput;
	@observable cargoList!: PagedResultDto<GetGroupByCounterparty>;
	@observable roles: GetRoles[] = [];
	@observable filters: string[] = [];

	@action
	async create(createOrUpdateCargoInput: CreateOrUpdateCargoInput) {
		await cargoService.create(createOrUpdateCargoInput);
	}

	@action createCargo() {
		this.editCargos = {
			id: 0,
			creationTime: new Date(),
			dateOfTransport: moment(),
			quantity: 0,
			status: "",
			cargoUnit: 0,
			units: "",
			counterparty: {
				id: 0,
				userName: "",
				name: "",
				surname: "",
				emailAddress: "",
				isActive: false,
				fullName: "",
				rank: "",
				lastLoginTime: new Date(),
				creationTime: new Date(),
				roleNames: [],
			},
			ship: {
				id: 0,
				shipName: "",
				mmsi: "",
				imoNumber: "",
			},
		};
	}

	@action
	async update(updateCargoInput: UpdateCargoInput) {
		await cargoService.update(updateCargoInput);
	}

	@action
	async getFilters(
		filters: { keyword: string; searchColumn: string },
		property: string,
	) {
		const result = await cargoService.getFilters(filters, property);

		this.filters = result;
	}

	@action
	async delete(entityDto: EntityDto) {
		await cargoService.delete(entityDto);
	}

	@action
	async get(entityDto: EntityDto) {
		const result = await cargoService.get(entityDto);
		this.editCargos = result;
	}

	@action
	async getAll(pagedFilterAndSortedRequest: PagedFilterAndSortedRequestCargo) {
		const result = await cargoService.getAll(pagedFilterAndSortedRequest);
		this.cargos = result;
	}

	async getGroupByCounterparty(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequestCargo,
	) {
		const result = await cargoService.getGroupByCounterparty(
			pagedFilterAndSortedRequest,
		);
		this.cargoList = result;
	}
}

export default CargoStore;
