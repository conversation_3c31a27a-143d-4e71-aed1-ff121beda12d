import { action, observable } from "mobx";
import { EntityDto } from "../services/dto/entityDto";
import type { PagedResultDto } from "../services/dto/pagedResultDto";
import type { CreateOrUpdateShipRentalInput } from "../services/shipRental/dto/CreateOrUpdateShipRentalInput";
import { GetRentalGroupByCounterparty } from "../services/shipRental/dto/GetRentalGroupByCounterparty";
import { GetShipRentalOutput } from "../services/shipRental/dto/GetShipRentalOutput";
import type { PagedFilterAndSortedRequestShipRental } from "../services/shipRental/dto/PagedFilterAndSortedRequestShipRental";
import type { UpdateShipRentalInput } from "../services/shipRental/dto/UpadteShipRentalInput";
import shipRentalService from "../services/shipRental/shipRentalService";
import { GetRoles } from "../services/user/dto/getRolesOuput";

class ShipRentalStore {
	@observable shipRentals!: GetShipRentalOutput[];
	@observable shipRentalsList!: PagedResultDto<GetRentalGroupByCounterparty>;
	@observable editShipRentals!: CreateOrUpdateShipRentalInput;
	@observable roles: GetRoles[] = [];
	@observable filters: string[] = [];

	@action
	async create(createOrUpdateShipRentalInput: CreateOrUpdateShipRentalInput) {
		await shipRentalService.create(createOrUpdateShipRentalInput);
	}

	@action
	async getFilters(
		pagedFilterAndSortedRequest: { keyword: string; searchColumn: string },
		property: string,
	) {
		const result = await shipRentalService.getFilters(
			pagedFilterAndSortedRequest,
			property,
		);
		this.filters = result;
	}

	@action
	async createShipRental() {
		this.editShipRentals = {
			id: 0,
			ship: {
				id: 0,
				creationTime: new Date(),
				creationUserId: 0,
				lastModificationTime: new Date(),
				lastModifierUserId: 0,
				uuid: "",
				shipName: "",
				mmsi: "",
				imoNumber: "",
				shipEmail: "",
				captainName: "",
				chiefEngineersName: "",
				engineRoomUnmannedHoursFrom: "",
				engineRoomUnmannedHoursTo: "",
				scrubber: "",
			},
			startDate: undefined,
			endDate: undefined,
			counterParty: "",
		};
	}

	@action
	async update(updateShipRental: UpdateShipRentalInput) {
		await shipRentalService.update(updateShipRental);
	}

	@action
	async delete(entityDto: EntityDto) {
		await shipRentalService.delete(entityDto);
	}

	@action
	async get(entityDto: EntityDto) {
		const result = await shipRentalService.get(entityDto);
		this.editShipRentals = result;
	}

	@action
	async getAll(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequestShipRental,
	) {
		const result = await shipRentalService.getAll(pagedFilterAndSortedRequest);
		this.shipRentals = result.items;
	}

	async getRentalGroupyByCounterparty(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequestShipRental,
	) {
		const result = await shipRentalService.getRentalGroupyByCounterparty(
			pagedFilterAndSortedRequest,
		);
		this.shipRentalsList = result;
	}
}

export default ShipRentalStore;
