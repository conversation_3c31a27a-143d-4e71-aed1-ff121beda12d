import { Form } from "@ant-design/compatible";
import "@ant-design/compatible/assets/index.css";
import { Button, Input, InputNumber, Modal, Select } from "antd";
import * as React from "react";
import { ChangeEvent, RefObject } from "react";
import AppComponentBase from "../../../components/AppComponentBase";
import ComponentLayout from "../../../components/Layout/ComponentLayout";
import { L } from "../../../lib/abpUtility";
import FuelTypeStore from "../../../stores/fuelTypeStore";
import MeterConfigurationStore, {
	DefaultDecimalPoint,
} from "../../../stores/meterConfigurationStore";
import ShipStore from "../../../stores/shipStore";
import { ModalType } from "../../ModalConsts";
import { FormWidths } from "../../ViewSettingsConsts";
import {
	flowDirectionCustomData,
	measureTypeCustomData,
	measuringCommodityCustomData,
	meterTypeCustomData,
	unitOfMeasureCustomData,
} from "../../enumUtils";
import { ModalFormComponentProps } from "../../modalFormComponentProps";
import rules from "./createOrUpdateMeterConfiguration.validation";
import { MeterConfigurationValidationRules } from "./meterConfigurationValidationRulesTable";

export interface ICreateOrUpdateMeterConfigurationProps
	extends ModalFormComponentProps {
	meterConfigurationStore: MeterConfigurationStore;
	shipStore: ShipStore;
	fuelTypesStore: FuelTypeStore;
}

export interface ICreateOrUpdateMeterConfigurationState {
	confirmDirty: boolean;
	selectedFile: File | null;
	fileUploadRef: RefObject<HTMLInputElement>;
	selectedHours: Array<string>;
}

class CreateOrUpdateMeterConfiguration extends AppComponentBase<
	ICreateOrUpdateMeterConfigurationProps,
	ICreateOrUpdateMeterConfigurationState
> {
	meterConfigurationStore: MeterConfigurationStore =
		this.props.meterConfigurationStore;
	shipStore: ShipStore = this.props.shipStore;
	fuelTypesStore: FuelTypeStore = this.props.fuelTypesStore;

	state: ICreateOrUpdateMeterConfigurationState = {
		confirmDirty: false,
		selectedFile: null,
		fileUploadRef: React.createRef(),
		selectedHours: [],
	};

	fileSelectedHandler = (event: ChangeEvent<HTMLInputElement>) => {
		if (!event.target.files) return;
		this.setState({ selectedFile: event.target.files[0] });
	};

	isExactNumberOfDigitsFlag = (): boolean => {
		const rules: number = this.props.form.getFieldValue("validationRules");
		return (
			rules?.hasFlag(MeterConfigurationValidationRules.ExactNumberOfDigits) ??
			false
		);
	};

	isMaximumNumberOfDigitsFlag = (): boolean => {
		const rules: number = this.props.form.getFieldValue("validationRules");
		return (
			rules?.hasFlag(MeterConfigurationValidationRules.MaximumNumberOfDigits) ??
			false
		);
	};

	calculateMaxDecimalPoint = (): number => {
		if (this.isExactNumberOfDigitsFlag()) {
			const digitsCount: number = this.props.form.getFieldValue("digitsCount");
			return digitsCount;
		}
		if (this.isMaximumNumberOfDigitsFlag()) {
			const maxDigitsCount: number =
				this.props.form.getFieldValue("maxDigitsCount");
			return maxDigitsCount;
		}
		return DefaultDecimalPoint;
	};

	render() {
		const { getFieldDecorator } = this.props.form;
		const { visible, onCancel, onCreate, okButtonDisabled } = this.props;

		const ships = this.props.shipStore.allShipNames
			? Object.values(this.props.shipStore.allShipNames)
			: [];

		const fuelTypes = this.props.fuelTypesStore.fuelTypes
			? Object.values(this.props.fuelTypesStore.fuelTypes.items)
			: [];

		return (
			<Modal
				visible={visible}
				cancelText={L("Cancel")}
				okText={L("OK")}
				onCancel={onCancel}
				onOk={onCreate}
				title={L("Meter")}
				okButtonProps={{ loading: okButtonDisabled }}
			>
				{getFieldDecorator("id")(<Input hidden />)}
				<Form layout="vertical">
					<Form.Item
						label={L("Meter Name")}
						{...ComponentLayout.formItemLayout}
						required
					>
						{getFieldDecorator("name", { rules: rules.name })(
							<Input style={{ width: FormWidths.wide }} />,
						)}
					</Form.Item>
					<Form.Item
						label={L("Ship Name")}
						{...ComponentLayout.formItemLayout}
						required
					>
						{getFieldDecorator("shipId", {
							rules: rules.shipId,
						})(
							<Select
								{...ComponentLayout.formItemLayout}
								style={{ width: FormWidths.wide }}
								placeholder={L("Select Ship")}
								onChange={(value: string) => {
									this.props.form.setFieldsValue({
										shipId: value,
									});
								}}
								disabled={this.props.modalType === ModalType.edit}
							>
								{ships.map((x) => {
									return (
										<Select.Option key={x.id} value={x.id}>
											{x.shipName}
										</Select.Option>
									);
								})}
							</Select>,
						)}
					</Form.Item>
					<Form.Item
						label={L("Meter Type")}
						{...ComponentLayout.formItemLayout}
						required
					>
						{getFieldDecorator("meterType", {
							rules: rules.meterType,
						})(
							<Select
								{...ComponentLayout.formItemLayout}
								style={{ width: FormWidths.wide }}
								placeholder={L("Select Meter Type")}
								onChange={(value: string) => {
									this.props.form.setFieldsValue({
										meterType: value,
									});
								}}
							>
								{[...meterTypeCustomData].map((x) => {
									return (
										<Select.Option key={x[0]} value={x[0]}>
											{x[1]}
										</Select.Option>
									);
								})}
							</Select>,
						)}
					</Form.Item>

					<Form.Item
						label={L("Measuring Commodity")}
						{...ComponentLayout.formItemLayout}
						required
					>
						{getFieldDecorator("measuringCommodity", {
							rules: rules.measuringCommodity,
						})(
							<Select
								{...ComponentLayout.formItemLayout}
								style={{ width: FormWidths.wide }}
								placeholder={L("Select Measuring Commodity")}
								onChange={(value: string) => {
									this.props.form.setFieldsValue({
										measuringCommodity: value,
									});
								}}
							>
								{[...measuringCommodityCustomData].map((x) => {
									return (
										<Select.Option key={x[0]} value={x[0]}>
											{x[1]}
										</Select.Option>
									);
								})}
							</Select>,
						)}
					</Form.Item>
					<Form.Item
						label={L("Measure Unit")}
						{...ComponentLayout.formItemLayout}
						required
					>
						{getFieldDecorator("unitOfMeasure", {
							rules: rules.unitOfMeasure,
						})(
							<Select
								{...ComponentLayout.formItemLayout}
								style={{ width: FormWidths.wide }}
								placeholder={L("Select Measure Unit")}
								onChange={(value: string) => {
									this.props.form.setFieldsValue({
										unitOfMeasure: value,
									});
								}}
							>
								{[...unitOfMeasureCustomData].map((x) => {
									return (
										<Select.Option key={x[0]} value={x[0]}>
											{x[1]}
										</Select.Option>
									);
								})}
							</Select>,
						)}
					</Form.Item>
					<Form.Item
						label={L("Fuel Type")}
						{...ComponentLayout.formItemLayout}
						required
					>
						{getFieldDecorator("fuelType", {
							rules: rules.fuelType,
						})(
							<Select
								{...ComponentLayout.formItemLayout}
								style={{ width: FormWidths.wide }}
								placeholder={L("Select Fuel Type")}
								onChange={(value: string) => {
									this.props.form.setFieldsValue({
										fuelType: value,
									});
								}}
							>
								{fuelTypes.map((x) => {
									return (
										<Select.Option key={x.id} value={x.type}>
											{x.type}
										</Select.Option>
									);
								})}
							</Select>,
						)}
					</Form.Item>

					<Form.Item
						label={L("Number Of Digits")}
						{...ComponentLayout.formItemLayout}
						required
					>
						{getFieldDecorator("digitsCount", { rules: rules.numberOfDigits })(
							<InputNumber
								style={{ width: FormWidths.wide }}
								min={1}
								max={10}
							/>,
						)}
					</Form.Item>

					<Form.Item
						label={L("Decimal Point")}
						{...ComponentLayout.formItemLayout}
						required
					>
						{getFieldDecorator("decimalPointPosition", {
							rules: rules.decimalPointPosition,
						})(
							<InputNumber
								style={{ width: FormWidths.wide }}
								min={0}
								max={this.calculateMaxDecimalPoint()}
							/>,
						)}
					</Form.Item>
					<Form.Item
						label={L("Measure Type")}
						{...ComponentLayout.formItemLayout}
						labelAlign={"left"}
						required
					>
						{getFieldDecorator("measurementType", {
							rules: rules.measureType,
						})(
							<Select
								{...ComponentLayout.formItemLayout}
								style={{ width: FormWidths.wide }}
								placeholder={L("Select Measure Type")}
								onChange={(value: string) => {
									this.props.form.setFieldsValue({
										measurementType: value,
									});
								}}
							>
								{[...measureTypeCustomData].map((x) => {
									return (
										<Select.Option key={x[0]} value={x[0]}>
											{x[1]}
										</Select.Option>
									);
								})}
							</Select>,
						)}
					</Form.Item>
					<Form.Item
						label={L("Flow Direction")}
						{...ComponentLayout.formItemLayout}
						labelAlign={"left"}
						required
					>
						{getFieldDecorator("flowDirection", {
							rules: rules.flowDirection,
						})(
							<Select
								{...ComponentLayout.formItemLayout}
								style={{ width: FormWidths.wide }}
								placeholder={L("Select Flow Direction")}
								onChange={(value: string) => {
									this.props.form.setFieldsValue({
										flowDirection: value,
									});
								}}
							>
								{[...flowDirectionCustomData].map((x) => {
									return (
										<Select.Option key={x[0]} value={x[0]}>
											{x[1]}
										</Select.Option>
									);
								})}
							</Select>,
						)}
					</Form.Item>

					<Form.Item
						label={L("Control Image")}
						{...ComponentLayout.formItemLayout}
					>
						{getFieldDecorator("fileUrl")(
							<div>
								<Button
									onClick={() => this.state.fileUploadRef.current?.click()}
								>
									Choose File
								</Button>
								<input
									type="file"
									ref={this.state.fileUploadRef}
									onChange={this.fileSelectedHandler}
									hidden
								/>
								<div>
									{this.props.form.getFieldValue("fileUrl") && (
										<img
											src={this.props.form.getFieldValue("fileUrl")}
											style={{ width: "100%", height: 300 }}
											alt="Control Image"
										/>
									)}
								</div>
							</div>,
						)}
					</Form.Item>
				</Form>
			</Modal>
		);
	}
}

export default Form.create<ICreateOrUpdateMeterConfigurationProps>()(
	CreateOrUpdateMeterConfiguration,
);
