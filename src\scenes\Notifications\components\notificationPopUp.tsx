import { Descriptions } from "antd";
import moment from "moment";
import React from "react";
import { SingleNotification } from "../../../services/notifications/dto/SingleNotification";
import { DescriptionsItemProps } from "antd/lib/descriptions/Item";

export interface NotificationPopUpProps {
	notification: SingleNotification;
}

const NotificationPopUp: React.FC<NotificationPopUpProps> = ({
	notification,
}) => {
	const items: DescriptionsItemProps[] = [
		{
			label: "Date",
			children: moment.utc(moment(notification.creationTime).utc()).format(),
		},
		{
			label: "Status",
			children: notification.title,
		},
		{
			label: "Latitude",
			children: notification.latitude,
		},
		{
			label: "Longitude",
			children: notification.longtitude,
		},
	];

	return (
		// <Card bordered={false}>
		//     {notification.description} <br />
		// </Card>
		<Descriptions
			size={"small"}
			column={1}
			style={{ width: "250px", padding: "5px" }}
			// extra={<Button type="primary">Edit</Button>}
		>
			{items.map((item) => (
				<Descriptions.Item label={item.label} key={Math.random().toString()}>
					{item.children}
				</Descriptions.Item>
			))}
		</Descriptions>
	);
};

export default NotificationPopUp;
