import { EntityDto } from "../../dto/entityDto";

export interface CreateOrUpdateMeterInput extends EntityDto {
	roleNames: string[];
	barcode: string;
	name: string;
	meterType: number;
	digitsCount: number;
	decimalPoint: number;
	maxDigitsCount: number;
	measureType: number;
	shipId: number;
	measurementFrequencyPerDay: number;
	uuid: string;
	consumerType: number;
	additionalScanHours: string[];
	controlImage: string;
	fuelType: number;
	measureUnit: number;

	validationRules: number;
	manualReadingOnly: boolean;
}
