import { WrappedFormUtils } from "@ant-design/compatible/lib/form/Form";
import { SettingOutlined } from "@ant-design/icons";
import {
	Button,
	Card,
	Col,
	Dropdown,
	Menu,
	Modal,
	Row,
	Table,
	TablePaginationConfig,
} from "antd";
import MenuItem from "antd/lib/menu/MenuItem";
import { ColumnProps } from "antd/lib/table";
import { SorterResult } from "antd/lib/table/interface";
import { inject, observer } from "mobx-react";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import { L, isGranted } from "../../lib/abpUtility";
import { GetDeviceOutput } from "../../services/device/dto/getDeviceOutput";
import { EntityDto } from "../../services/dto/entityDto";
import DeviceStore from "../../stores/deviceStore";
import Stores from "../../stores/storeIdentifier";
import utils from "../../utils/utils";
import { ModalType } from "../ModalConsts";
import {
	getTablePaginationOptions,
	renderCheckboxCustomValue,
	renderDate,
} from "../renderUtils";
import CreateOrUpdateDeviceRegistration from "./components/createOrUpdateDeviceRegistration";

export interface IDeviceRegistrationProps {
	deviceStore: DeviceStore;
}

export interface IDeviceRegistrationState {
	modalVisible: boolean;
	loading: boolean;
	maxResultCount: number;
	skipCount: number;
	deviceId: number;
	filter: string;
	sorters: SorterResult<GetDeviceOutput>[];
}

const confirm = Modal.confirm;

type PreviousState = {
	sorters: IDeviceRegistrationState["sorters"];
};

@inject(Stores.DeviceStore)
@observer
class DeviceRegistration extends AppComponentBase<
	IDeviceRegistrationProps,
	IDeviceRegistrationState
> {
	formRef?: WrappedFormUtils;

	state: IDeviceRegistrationState = {
		modalVisible: false,
		loading: false,
		maxResultCount: 10,
		skipCount: 0,
		deviceId: 0,
		filter: "",
		sorters: [],
	};

	async componentDidMount() {
		const prevState = this.extractPreviousState();

		this.setState({ ...prevState }, async () => {
			await Promise.all([
				this.getAll(),
				this.props.deviceStore.getDeviceStatuses(),
			]);
		});
	}

	extractPreviousState(): PreviousState {
		let state: PreviousState = {
			sorters: [],
		};

		const prevState =
			utils.getSortAndFilterFromStorage<PreviousState>("device-filters");

		if (prevState)
			state = {
				...prevState,
			};

		utils.removeStateFromStorage("device-filters");

		return state;
	}

	componentWillUnmount(): void {
		const settings: PreviousState = {
			sorters: this.state.sorters,
		};

		utils.saveSortAndFilterToStorage("device-filters", settings);
	}

	async getAll() {
		this.setState({ loading: true });
		const sortString = utils.getSorterString(this.state.sorters);

		await this.props.deviceStore.getAll({
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: this.state.filter,
			sorting: sortString,
		});
		this.setState({ loading: false });
	}

	handleTableChange = (
		pagination: TablePaginationConfig,
		_: Partial<Record<keyof GetDeviceOutput, string[]>>,
		sorter: SorterResult<GetDeviceOutput> | SorterResult<GetDeviceOutput>[],
	) => {
		const sorters = utils.getSorters(sorter);

		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
				sorters: sorters,
			},
			async () => await this.getAll(),
		);
	};

	Modal = () => {
		this.setState({
			modalVisible: !this.state.modalVisible,
		});
	};

	async createOrUpdateModalOpen(entityDto: EntityDto) {
		this.setState({ loading: true });

		if (entityDto.id !== 0) {
			await this.props.deviceStore.get(entityDto);
		}

		this.setState({ deviceId: entityDto.id, loading: false });
		this.Modal();

		if (this.formRef)
			this.formRef.setFieldsValue({ ...this.props.deviceStore.editDevice });
	}

	delete(input: EntityDto) {
		const self = this;
		confirm({
			title: "Do you want to delete these items?",
			onOk() {
				self.props.deviceStore.delete(input);
			},
			onCancel() {
				console.log("Cancel");
			},
		});
	}

	handleCreate = () => {
		const form = this.formRef;
		if (!form) return;

		// biome-ignore lint/suspicious/noExplicitAny: Poor implementation
		form.validateFields(async (err: any, values: any) => {
			if (err) {
				return;
			}
			this.setState({ loading: true });
			try {
				if (this.state.deviceId === 0) {
					await this.props.deviceStore.create(values);
				} else {
					await this.props.deviceStore.update({
						id: this.state.deviceId,
						...values,
					});
				}
				this.setState({ modalVisible: false, loading: false });
				await this.getAll();
				form.resetFields();
			} catch (ex) {}
		});
	};

	// biome-ignore lint/suspicious/noExplicitAny: Poor type implementation from library
	saveFormRefEdit = (formRefEdit: any) => {
		if (!formRefEdit) return;
		this.formRef = formRefEdit.props.form;
	};
	handleSearch = (value: string) => {
		this.setState({ filter: value }, async () => await this.getAll());
	};

	public render() {
		const { devices } = this.props.deviceStore;
		// const { deviceStatuses } = this.props.deviceStore;
		const paginationOptions = getTablePaginationOptions(devices?.totalCount);
		const columns: Array<ColumnProps<GetDeviceOutput>> = [
			{
				title: L("Name"),
				dataIndex: ["user", "name"],
				key: "user.name",
				width: 150,
				sorter: { multiple: 1 },
				sortOrder: this.state.sorters.find((x) => x.columnKey === "user.name")
					?.order,
			},
			{
				title: L("Surname"),
				dataIndex: ["user", "surname"],
				key: "user.surname",
				width: 150,
				sorter: { multiple: 2 },
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "user.surname",
				)?.order,
			},
			{
				title: L("Email"),
				dataIndex: ["user", "emailAddress"],
				key: "user.emailAddress",
				width: 150,
				sorter: { multiple: 3 },
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "user.emailAddress",
				)?.order,
			},
			{
				title: L("Rank"),
				dataIndex: ["user", "rank"],
				key: "user.rank",
				width: 150,
				sorter: { multiple: 4 },
				sortOrder: this.state.sorters.find((x) => x.columnKey === "user.rank")
					?.order,
			},
			{
				title: L("Ship Name"),
				dataIndex: ["ship", "shipName"],
				key: "ship.shipName",
				width: 150,
				sorter: { multiple: 5 },
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "user.shipName",
				)?.order,
			},
			{
				title: L("IMO"),
				dataIndex: "shipImo",
				key: "shipImo",
				width: 150,
				sorter: { multiple: 6 },
				sortOrder: this.state.sorters.find((x) => x.columnKey === "shipImo")
					?.order,
			},
			{
				title: L("Status"),
				dataIndex: "status",
				key: "status",
				width: 150,
				sorter: { multiple: 6 },
				sortOrder: this.state.sorters.find((x) => x.columnKey === "status")
					?.order,
			},
			{
				title: L("Build number"),
				dataIndex: "buildNumber",
				key: "buildNumber",
				width: 150,
				sorter: { multiple: 7 },
				sortOrder: this.state.sorters.find((x) => x.columnKey === "buildNumber")
					?.order,
			},
			{
				title: L("Created At Activation Code"),
				dataIndex: "codeCreatedAt",
				key: "codeCreatedAt",
				width: 250,
				render: (text: string) => renderDate(text),
				sorter: { multiple: 8 },
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "codeCreatedAt",
				)?.order,
			},
			{
				title: L("Is It Used"),
				dataIndex: "codeUsed",
				key: "codeUsed",
				width: 150,
				render: (value: boolean) =>
					renderCheckboxCustomValue(value, "Yes", "No"),
			},
			{
				title: L("Activation Code"),
				dataIndex: "activationCode",
				key: "activationCode",
				width: 350,
			},
			{
				title: L("Actions"),
				width: 150,
				fixed: "right" as const,
				render: (text: string, item: GetDeviceOutput) => (
					<div>
						<Dropdown
							trigger={["click"]}
							overlay={
								<Menu>
									{isGranted("Pages.Devices-Edit") && (
										<MenuItem
											onClick={() =>
												this.createOrUpdateModalOpen({ id: item.id })
											}
										>
											{L("Edit")}
										</MenuItem>
									)}
									{isGranted("Pages.Devices-Delete") && (
										<MenuItem onClick={() => this.delete({ id: item.id })}>
											{L("Delete")}
										</MenuItem>
									)}
								</Menu>
							}
							placement="bottomLeft"
						>
							<Button type="primary" icon={<SettingOutlined />}>
								{L("Actions")}
							</Button>
						</Dropdown>
					</div>
				),
			},
		];

		const hasNoActions =
			!isGranted("Pages.Devices-Edit") && !isGranted("Pages.Devices-Delete");

		if (hasNoActions) {
			columns.pop();
		}

		return (
			<Card>
				<Row>
					{/* <Col
                        xs={{ span: 10, offset: 0 }}
                        sm={{ span: 10, offset: 0 }}
                        md={{ span: 10, offset: 0 }}
                        lg={{ span: 10, offset: 0 }}
                        xl={{ span: 10, offset: 0 }}
                        xxl={{ span: 10, offset: 0 }}
                    >
                        <Search placeholder={this.L('Filter')} onSearch={this.handleSearch}/>
                    </Col> */}
					<Col
						xs={{ span: 23, offset: 0 }}
						sm={{ span: 23, offset: 0 }}
						md={{ span: 23, offset: 0 }}
						lg={{ span: 23, offset: 0 }}
						xl={{ span: 23, offset: 0 }}
						xxl={{ span: 23, offset: 0 }}
						style={{ display: "flex", justifyContent: "end" }}
					>
						{/* <Button type="primary" shape="circle" icon="plus" onClick={() => this.createOrUpdateModalOpen({ id: 0 })}/> */}
					</Col>
				</Row>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey={(record) => record.id.toString()}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={devices === undefined || this.state.loading}
							dataSource={devices === undefined ? [] : devices.items}
							onChange={this.handleTableChange}
							scroll={{ y: 850 }}
						/>
					</Col>
				</Row>
				<CreateOrUpdateDeviceRegistration
					deviceStore={this.props.deviceStore}
					wrappedComponentRef={this.saveFormRefEdit}
					visible={this.state.modalVisible}
					loading={this.state.loading}
					onCancel={() =>
						this.setState({
							modalVisible: false,
						})
					}
					modalType={
						this.state.deviceId === 0 ? ModalType.create : ModalType.edit
					}
					onCreate={this.handleCreate}
				/>
				<Chat />
			</Card>
		);
	}
}

export default DeviceRegistration;
