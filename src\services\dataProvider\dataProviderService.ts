import { EntityDto } from "../dto/entityDto";
import { PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoints";
import http from "../httpService";
import { CreateOrUpdateDataProviderInput } from "./dto/CreateOrUpdateDataProviderInput";
import { PagedFilterAndSortedRequestDataProvider } from "./dto/PagedFilterAndSortedRequestDataProvider";
import { UpdateDataProviderInput } from "./dto/UpdateDataProviderInput";

class DataProviderService {
	public async create(
		createDataProviderInput: CreateOrUpdateDataProviderInput,
	) {
		const result = await http.post(
			Endpoint.DataProvider.Create,
			createDataProviderInput,
		);
		return result.data.result;
	}

	public async update(updateDataProvider: UpdateDataProviderInput) {
		const result = await http.put(
			Endpoint.DataProvider.Update,
			updateDataProvider,
		);
		return result.data.result;
	}

	public async delete(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.DataProvider.Delete, {
			params: entityDto,
		});
		return result.data;
	}

	public async get(
		entityDto: EntityDto,
	): Promise<CreateOrUpdateDataProviderInput> {
		const result = await http.get(Endpoint.DataProvider.Get, {
			params: entityDto,
		});
		return result.data.result;
	}

	public async getAll(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequestDataProvider,
	): Promise<PagedResultDto<CreateOrUpdateDataProviderInput>> {
		const result = await http.get(Endpoint.DataProvider.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}
}

export default new DataProviderService();
