// biome-ignore lint/suspicious/noExplicitAny: <explanation>
const config = (window as any)._env_;
const AppConsts = {
	userManagement: {
		defaultAdminUserName: "admin",
	},
	localization: {
		defaultLocalizationSourceName: "Trust",
	},
	authorization: {
		encrptedAuthTokenName: "enc_auth_token",
	},
	appBaseUrl:
		process.env.REACT_APP_APP_BASE_URL || config.REACT_APP_APP_BASE_URL,
	remoteServiceBaseUrl:
		process.env.REACT_APP_REMOTE_SERVICE_BASE_URL ||
		config.REACT_APP_REMOTE_SERVICE_BASE_URL,
	azureMapSubscriptionKey: process.env.REACT_APP_AZURE_MAP_SUBSCRIPTION_KEY,
	sortCacheKeys: {
		Performance: "performance-filters",
		Notifications: "notification-filters",
		Emission: "emission-filters",
		Formulae: "formulae-filters",
		Results: "results-filters",
		Readings: "readings-filters",
		Meters: "meters-filters",
		Cargo: "cargo-filters",
		Ships: "ship-filters",
		Roles: "role-filters",
		Devices: "device-filters",
		Users: "user-filters",
		Fuels: "fuel-filters",
		Documents: "docs-filters",
	} as const,
} as const;
export default AppConsts;
