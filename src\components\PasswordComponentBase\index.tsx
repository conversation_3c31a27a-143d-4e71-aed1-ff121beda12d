import { FormComponentProps } from "@ant-design/compatible/lib/form";
import AppComponentBase from "../AppComponentBase";

class PasswordComponentBase<
	P extends FormComponentProps,
	S = {},
	SS = any,
> extends AppComponentBase<P, S, SS> {
	compareToFirstPassword = (rule: any, value: any, callback: any) => {
		const { form } = this.props;
		if (value && value !== form.getFieldValue("password")) {
			callback(this.L("PasswordsDoNotMatch"));
		} else {
			callback();
		}
	};
}

export default PasswordComponentBase;
