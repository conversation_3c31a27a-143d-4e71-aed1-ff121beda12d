export interface GetMeterConfigurationOutput {
	uuid: string;
	name: string;
	meterType: string;
	unitOfMeasure: string;
	digitsCount: number;
	fileUrl: string;
	lastValidMeterValue: number;
	lastValidReadingMeterDate: Date;
	decimalPointPosition: number;
	flowDirection: string;
	measurementType: string;
	fuelType: string;
	shipId: number;
	shipName: string;
	ship: {
		id: number;
		shipName: string;
		imoNumber: string;
	};
	scheduledReadings: string[];
	id: number;
	consumerType: string;
	additionalScanHours: string[];
	meterId: number;
	measuringCommodity: string;
}
