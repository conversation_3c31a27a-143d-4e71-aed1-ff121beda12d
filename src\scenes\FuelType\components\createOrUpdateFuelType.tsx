import * as React from "react";
import "../../scenes.less";

import { Form } from "@ant-design/compatible";
import "@ant-design/compatible/assets/index.css";

import { Input, InputNumber, Modal } from "antd";

import { FormComponentProps } from "@ant-design/compatible/lib/form";
import ComponentLayout from "../../../components/Layout/ComponentLayout";
import { L } from "../../../lib/abpUtility";
import { FormWidths } from "../../ViewSettingsConsts";
import rules from "./createOrUpdateFuelType.validation";

export interface ICreateOrUpdateFuelTypeProps extends FormComponentProps {
	visible: boolean;
	onCancel: () => void;
	modalType: string;
	onCreate: () => void;
	fuelTypeEnums: { [key: number]: string };
}

class CreateOrUpdateFuelType extends React.Component<ICreateOrUpdateFuelTypeProps> {
	render() {
		const { getFieldDecorator } = this.props.form;
		const { visible, onCancel, onCreate } = this.props;

		// let fuelTypeString: string = '';
		// if (fuelTypeNumber && this.props.fuelTypeEnums) {
		//   fuelTypeString = this.props?.fuelTypeEnums[fuelTypeNumber];
		// }
		return (
			<Modal
				visible={visible}
				cancelText={L("Cancel")}
				okText={L("OK")}
				onCancel={onCancel}
				onOk={onCreate}
				title={"FuelType"}
			>
				<Form layout="vertical">
					<Form.Item label={L("Fuel Type")} {...ComponentLayout.formItemLayout}>
						{getFieldDecorator("type", { rules: rules.fuelType })(
							<Input style={{ width: FormWidths.wide }} />,
						)}
					</Form.Item>
					<Form.Item
						label={L("Fuel Density (Kg/cbm)")}
						{...ComponentLayout.formItemLayout}
					>
						{getFieldDecorator("densityValue", { rules: rules.densityValue })(
							<InputNumber style={{ width: FormWidths.wide }} />,
						)}
					</Form.Item>
					<Form.Item
						label={L("Fuel LCV (KJ/Kg)")}
						{...ComponentLayout.formItemLayout}
					>
						{getFieldDecorator("lcv", { rules: rules.lcv })(
							<InputNumber style={{ width: FormWidths.wide }} />,
						)}
					</Form.Item>
					<Form.Item
						label={L("CO2eq Emissions (gCO2eq/MJ)")}
						{...ComponentLayout.formItemLayout}
					>
						{getFieldDecorator("co2Emissions", { rules: rules.co2Emissions })(
							<InputNumber style={{ width: FormWidths.wide }} />,
						)}
					</Form.Item>
					<Form.Item
						label={L("Cf (gCO2/g fuel)")}
						{...ComponentLayout.formItemLayout}
					>
						{getFieldDecorator("co2Factor", { rules: rules.co2Factor })(
							<InputNumber style={{ width: FormWidths.wide }} />,
						)}
					</Form.Item>
					<Form.Item
						label={L("Cf (gCH4/g fuel)")}
						{...ComponentLayout.formItemLayout}
					>
						{getFieldDecorator("ch4Factor", { rules: rules.ch4Factor })(
							<InputNumber style={{ width: FormWidths.wide }} />,
						)}
					</Form.Item>
					<Form.Item
						label={L("Cf (gN2O/g fuel)")}
						{...ComponentLayout.formItemLayout}
					>
						{getFieldDecorator("n2oFactor", { rules: rules.n2oFactor })(
							<InputNumber style={{ width: FormWidths.wide }} />,
						)}
					</Form.Item>
					<Form.Item
						label={L("Cslip (% of Fuel mass)")}
						{...ComponentLayout.formItemLayout}
					>
						{getFieldDecorator("cslip", { rules: rules.cslip })(
							<InputNumber style={{ width: FormWidths.wide }} />,
						)}
					</Form.Item>
				</Form>
			</Modal>
		);
	}
}

export default Form.create<ICreateOrUpdateFuelTypeProps>()(
	CreateOrUpdateFuelType,
);
