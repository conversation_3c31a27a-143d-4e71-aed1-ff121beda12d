import { action, observable } from "mobx";

import { MeterConfigurationValidationRules } from "../scenes/MeterConfiguration/components/meterConfigurationValidationRulesTable";
import fileService from "../services/azure/fileService";
import { EntityDto } from "../services/dto/entityDto";
import type { PagedResultDto } from "../services/dto/pagedResultDto";
import type { PagedMeterResultRequestDto } from "../services/meter/dto/PagedMeterResultRequestDto";
import type { CreateOrUpdateMeterInput } from "../services/meter/dto/createOrUpdateMeterInput";
import { GetMeterOutput } from "../services/meter/dto/getMeterOutput";
import type { MeterByShipResultRequestDto } from "../services/meter/dto/meterByShipResultRequestDto";
import { MeterNameAndUuidDto } from "../services/meter/dto/meterNameAndUuidDto";
import meterService from "../services/meter/meterService";
import { GetShipOutput } from "../services/ship/dto/getShipOutput";
import { GetRoles } from "../services/user/dto/getRolesOuput";
import ShipStore from "./shipStore";

export const DefaultDecimalPoint = 2;

class MeterStore {
	@observable meters!: PagedResultDto<GetMeterOutput>;
	@observable editMeter!: CreateOrUpdateMeterInput;
	@observable roles: GetRoles[] = [];
	@observable filters: string[] = [];
	@observable meterTypes!: { [key: number]: string };
	@observable measureTypes!: { [key: number]: string };
	@observable consumerTypes!: { [key: number]: string };
	@observable ships!: GetShipOutput[];
	@observable fuelTypes!: { [key: number]: string };
	@observable measureUnits!: { [key: number]: string };
	@observable meterNameAndUuids!: MeterNameAndUuidDto[];
	@observable metersForCurrentShip!: PagedResultDto<GetMeterOutput>;
	@observable MeterConfigurationValidationRules!: { [key: number]: string };
	qrCodeLink!: string;

	shipStore: ShipStore = new ShipStore();

	@action
	async create(createMeterInput: CreateOrUpdateMeterInput, file: File | null) {
		if (file !== null) {
			const fileName = await fileService.uploadFile(file);
			createMeterInput.controlImage = fileName;
		}
		const result = await meterService.create(createMeterInput);
		this.meters.items.push(result);
	}

	@action
	async update(updateMeterInput: CreateOrUpdateMeterInput, file: File | null) {
		if (file != null) {
			const fileName = await fileService.uploadFile(file);
			updateMeterInput.controlImage = fileName;
		} else {
			updateMeterInput.controlImage = this.editMeter.controlImage;
		}
		const result = await meterService.update(updateMeterInput);
		this.meters.items = this.meters.items.map((x: GetMeterOutput) => {
			if (x.id === updateMeterInput.id) x = result;
			return x;
		});
	}

	@action
	async delete(entityDto: EntityDto) {
		await meterService.delete(entityDto);
		this.meters.items = this.meters.items.filter(
			(x: GetMeterOutput) => x.id !== entityDto.id,
		);
	}

	@action
	async get(entityDto: EntityDto) {
		const result = await meterService.get(entityDto);
		this.editMeter = result;
	}

	@action
	async createMeter() {
		await this.shipStore.getAllWithIdAndName();
		const shipId = this.shipStore.allShips[0]?.id;

		this.editMeter = {
			id: 0,
			barcode: "",
			name: "",
			meterType: 0,
			digitsCount: 4,
			maxDigitsCount: 4,
			decimalPoint: DefaultDecimalPoint,
			measureType: 0,
			shipId: shipId,
			measurementFrequencyPerDay: 0,
			roleNames: [],
			uuid: "",
			consumerType: 0,
			additionalScanHours: [],
			controlImage: "",
			fuelType: 0,
			measureUnit: 0,
			validationRules:
				MeterConfigurationValidationRules.ExactNumberOfDigits |
				MeterConfigurationValidationRules.LowerReadingValidation,
			manualReadingOnly: false,
		};
		this.roles = [];
	}

	@action
	async getAll(pagedFilterAndSortedRequest: PagedMeterResultRequestDto) {
		const result = await meterService.getAll(pagedFilterAndSortedRequest);
		this.meters = result;
	}

	@action
	async getMeterTypes() {
		const result = await meterService.getMeterTypes();
		this.meterTypes = result;
	}

	@action
	async getMeasureTypes() {
		const result = await meterService.getMeasureTypes();
		this.measureTypes = result;
	}

	@action
	async getConsumerTypes() {
		const result = await meterService.getConsumerTypes();
		this.consumerTypes = result;
	}

	@action
	async getFuelTypes() {
		const result = await meterService.getFuelTypes();
		this.fuelTypes = result;
	}

	@action
	async getMeasureUnits() {
		const result = await meterService.getMeasureUnits();
		this.measureUnits = result;
	}

	@action
	async getShips() {
		const result = await meterService.getShips();
		this.ships = result.items;
	}

	@action
	async getAllForShip(
		meterByShipResultRequestDto: MeterByShipResultRequestDto,
	) {
		const result = await meterService.getAllForShip(
			meterByShipResultRequestDto,
		);
		this.metersForCurrentShip = result;
	}

	@action
	async downloadQrCodeLink(entityDto: EntityDto) {
		const result = await meterService.downloadQrCodeLink(entityDto);
		this.qrCodeLink = result;
	}

	@action
	async getMeterNameAndUuids(uuids: string[]) {
		const result = await meterService.getMeterNameAndUuids(uuids);
		this.meterNameAndUuids = result;
	}

	@action
	async getMeterValidationRules() {
		const result = await meterService.getMeterValidationRules();
		this.MeterConfigurationValidationRules = result;
	}
}

export default MeterStore;
