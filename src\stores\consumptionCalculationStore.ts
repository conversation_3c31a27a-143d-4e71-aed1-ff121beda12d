import { action, observable } from "mobx";
import consumptionCalculationService from "../services/consumptionCalculation/consumptionCalculationService";
import type {
	CalculationResultAutoInput,
	ConsumptionCalculationDtos,
	ConsumptionConfigurationDto,
} from "../services/consumptionCalculation/dto/calculationResultAutoInput";

class ConsumptionCalculationStore {
	@observable consumptionCalculations!: ConsumptionCalculationDtos;
	@observable config!: ConsumptionConfigurationDto;

	@action
	async calculateResults(calculateResultsInput: CalculationResultAutoInput) {
		const calculations = await consumptionCalculationService.calculateResults(
			calculateResultsInput,
		);
		this.consumptionCalculations = calculations;
	}

	@action
	async getConfig() {
		const config = await consumptionCalculationService.getConfig();
		this.config = config;
	}

	@action
	async setGroupingPeriod(groupingPeriodInMinutes: number) {
		const result = await consumptionCalculationService.setGroupingPeriod(
			groupingPeriodInMinutes,
		);
		this.config = result;
	}
}

export default ConsumptionCalculationStore;
