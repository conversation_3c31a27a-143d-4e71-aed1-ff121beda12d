import { Form } from "@ant-design/compatible";
import "@ant-design/compatible/assets/index.css";
import { DatePicker, Input, Modal, Select } from "antd";
import moment from "moment";
import AppComponentBase from "../../../components/AppComponentBase";
import ComponentLayout from "../../../components/Layout/ComponentLayout";
import { L } from "../../../lib/abpUtility";
import { GetUserOutput } from "../../../services/user/dto/getUserOutput";
import ShipRentalStore from "../../../stores/shipRentalStore";
import ShipStore from "../../../stores/shipStore";
import { ModalType } from "../../ModalConsts";
import { FormWidths } from "../../ViewSettingsConsts";
import { ModalFormComponentProps } from "../../modalFormComponentProps";
import rules from "./createOrUpdateShipRental.validation";

export interface ICreateOrUpdateShipRentalProps
	extends ModalFormComponentProps {
	shipRentalStore: ShipRentalStore;
	shipStore: ShipStore;
	listCounterparty: GetUserOutput[];
}

class CreateOrUpdateShipRental extends AppComponentBase<ICreateOrUpdateShipRentalProps> {
	shipRentalStore: ShipRentalStore = this.props.shipRentalStore;
	shipStore: ShipStore = this.props.shipStore;

	componentDidUpdate(): void {
		if (
			this.shipStore.allShipNames?.[0] &&
			this.props.form.getFieldValue("ship.id") === 0
		) {
			this.props.form.setFieldsValue({
				ship: { id: this.shipStore.allShipNames[0].id },
			});
		}
	}

	render() {
		const { getFieldDecorator } = this.props.form;
		const { visible, onCancel, onCreate, okButtonDisabled, listCounterparty } =
			this.props;

		const ships = this.props.shipStore.allShipNames
			? Object.values(this.props.shipStore.allShipNames)
			: [];

		return (
			<Modal
				visible={visible}
				cancelText={L("Cancel")}
				okText={L("OK")}
				onCancel={onCancel}
				onOk={onCreate}
				title={L("Set up Charter period")}
				okButtonProps={{ loading: okButtonDisabled }}
			>
				{getFieldDecorator("id")(<Input hidden />)}
				<Form layout="vertical">
					<Form.Item
						label={L("Ship Name")}
						{...ComponentLayout.formItemLayout}
						required
					>
						{getFieldDecorator("ship.id", {
							rules: rules.shipName,
						})(
							<Select
								{...ComponentLayout.formItemLayout}
								style={{ width: FormWidths.wide }}
								placeholder={L("Select Ship")}
								onChange={(value: string) => {
									this.props.form.setFieldsValue({
										ship: {
											id: value,
										},
									});
								}}
								disabled={this.props.modalType === ModalType.edit}
							>
								{ships.map((x) => {
									return (
										<Select.Option key={x.id} value={x.id}>
											{x.shipName}
										</Select.Option>
									);
								})}
							</Select>,
						)}
					</Form.Item>
					<Form.Item
						label={L("Counterparty")}
						{...ComponentLayout.formItemLayout}
						required
					>
						{getFieldDecorator("counterParty.id", {
							rules: rules.counterParty,
						})(
							<Select
								{...ComponentLayout.formItemLayout}
								style={{ width: FormWidths.wide }}
								placeholder={L("Select Counterparty")}
								onChange={(value: string) => {
									this.props.form.setFieldsValue({
										counterParty: {
											id: value,
										},
									});
								}}
								disabled={this.props.modalType === ModalType.edit}
							>
								{listCounterparty.map((x) => {
									return (
										<Select.Option
											key={x.id}
											value={x.id}
										>{`${x.fullName} (${x.userName})`}</Select.Option>
									);
								})}
							</Select>,
						)}
					</Form.Item>
					<Form.Item
						label={L("Start of hire date and time")}
						{...ComponentLayout.formItemLayout}
						required
					>
						{getFieldDecorator("startDate", {
							rules: rules.startDate,
						})(
							<DatePicker
								disabled={this.props.modalType === ModalType.edit}
								showTime={{ format: "HH:mm" }}
								style={{ width: FormWidths.wide }}
								onChange={(date) =>
									this.props.form.setFieldsValue({ startDate: date })
								}
							/>,
						)}
					</Form.Item>
					<Form.Item
						label={L("Stop hire date and time")}
						{...ComponentLayout.formItemLayout}
						required
					>
						{getFieldDecorator("endDate", {
							rules: rules.endDate,
						})(
							<DatePicker
								showTime={{ format: "HH:mm" }}
								style={{ width: FormWidths.wide }}
								disabled={!this.props.form.getFieldValue("startDate")}
								onChange={(date) =>
									this.props.form.setFieldsValue({ endDate: date })
								}
								disabledDate={(date) =>
									moment(this.props.form.getFieldValue("startDate")) > date
								}
							/>,
						)}
					</Form.Item>
				</Form>
			</Modal>
		);
	}
}

export default Form.create<ICreateOrUpdateShipRentalProps>()(
	CreateOrUpdateShipRental,
);
