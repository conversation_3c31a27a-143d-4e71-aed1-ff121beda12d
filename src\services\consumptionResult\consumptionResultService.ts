import { EntityDto } from "../dto/entityDto";
import { PagedFilterAndSortedRequest } from "../dto/pagedFilterAndSortedRequest";
import { ListResultDto, PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoints";
import httpCsv from "../httpCsvService";
import http from "../httpService";
import { ConsumptionResultDto } from "./dto/consumptionResultDto";

class ConsumptionResultService {
	public async create(createConsumptionResultInput: ConsumptionResultDto) {
		const result = await http.post(
			Endpoint.ConsumptionResult.Create,
			createConsumptionResultInput,
		);
		return result.data.result;
	}

	public async update(updateConsumptionResultInput: ConsumptionResultDto) {
		const result = await http.put(
			Endpoint.ConsumptionResult.Update,
			updateConsumptionResultInput,
		);
		return result.data.result;
	}

	public async delete(entityDto: EntityDto) {
		const result = await http.delete(Endpoint.ConsumptionResult.Delete, {
			params: entityDto,
		});
		return result.data;
	}

	public async get(entityDto: EntityDto): Promise<ConsumptionResultDto> {
		const result = await http.get(Endpoint.ConsumptionResult.Get, {
			params: entityDto,
		});
		return result.data.result;
	}

	public async getAll(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequest,
	): Promise<PagedResultDto<ConsumptionResultDto>> {
		const result = await http.get(Endpoint.ConsumptionResult.GetAll, {
			params: pagedFilterAndSortedRequest,
		});
		return result.data.result;
	}

	public async resendToGs(entities: ListResultDto<EntityDto>) {
		const result = await http.post(
			Endpoint.ConsumptionResult.ReSendToGs,
			entities,
		);
		return result.data.result;
	}

	public async downloadCsv(entities: ListResultDto<EntityDto>) {
		const result = await httpCsv.post(
			Endpoint.ConsumptionResult.SendCsv,
			entities,
		);
		return result.data.result;
	}

	public async downloadAllCsv(
		pagedFilterAndSortedRequest: PagedFilterAndSortedRequest,
	) {
		const result = await httpCsv.post(
			Endpoint.ConsumptionResult.SendAllCsv,
			pagedFilterAndSortedRequest,
		);
		return result.data.result;
	}

	public async getFilters(
		filters: { keyword: string; searchColumn: string },
		property: string,
	): Promise<string[]> {
		const result = await http.get(Endpoint.ConsumptionResult.Filters, {
			params: { ...filters, propertyName: property },
		});
		return result.data.result;
	}
}

export default new ConsumptionResultService();
