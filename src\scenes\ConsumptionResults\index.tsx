import { WrappedFormUtils } from "@ant-design/compatible/lib/form/Form";
import { Card, Col, DatePicker, Modal, Row, Table } from "antd";
import { ColumnProps, TablePaginationConfig } from "antd/lib/table";
import { FilterDropdownProps, SorterResult } from "antd/lib/table/interface";
import { inject, observer } from "mobx-react";
import moment from "moment";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import FilterSelect from "../../components/FilterSelect/FilterSelect";
import { L } from "../../lib/abpUtility";
import { FilterByColumn } from "../../models/Sort/SortState";
import { ConsumptionResultDto } from "../../services/consumptionResult/dto/consumptionResultDto";
import { EntityDto } from "../../services/dto/entityDto";
import ConsumptionResultStore from "../../stores/consumptionResultStore";
import MeterReadingStore from "../../stores/meterReadingStore";
import Stores from "../../stores/storeIdentifier";
import utils from "../../utils/utils";
import {
	getTablePaginationOptions,
	renderDateFromUtcStringAndTimezone,
	renderFilterIcon,
} from "../renderUtils";

export interface IConsumptionResultProps {
	consumptionResultStore: ConsumptionResultStore;
	meterReadingStore: MeterReadingStore;
}

export interface IConsumptionResultState {
	modalVisible: boolean;
	maxResultCount: number;
	skipCount: number;
	consumptionConsumerId: number;
	filters: Array<FilterByColumn>;
	sorters: SorterResult<ConsumptionResultDto>[];
	keyword: string;
	searchColumn: string;
	loading: boolean;
	fetchingFilters: boolean;
	activeFilters: {
		firstReadingStart?: string;
		firstReadingEnd?: string;
		completedStart?: string;
		completedEnd?: string;
	};
}

const confirm = Modal.confirm;

type PreviousState = {
	filters: IConsumptionResultState["filters"];
	sorters: IConsumptionResultState["sorters"];
	activeFilters: IConsumptionResultState["activeFilters"];
};

@inject(Stores.ConsumptionResultStore)
@inject(Stores.MeterReadingStore)
@observer
class ConsumptionResult extends AppComponentBase<
	IConsumptionResultProps,
	IConsumptionResultState
> {
	formRef?: WrappedFormUtils;

	state: IConsumptionResultState = {
		modalVisible: false,
		maxResultCount: 10,
		skipCount: 0,
		consumptionConsumerId: 0,
		filters: [],
		sorters: [],
		keyword: "",
		searchColumn: "",
		loading: false,
		fetchingFilters: false,
		activeFilters: {
			firstReadingStart: undefined,
			firstReadingEnd: undefined,
			completedStart: undefined,
			completedEnd: undefined,
		},
	};

	async componentDidMount() {
		const prevState = this.extractPreviousState();

		this.setState({ ...prevState }, async () => {
			await Promise.all([this.getAll()]);
		});
	}

	extractPreviousState(): PreviousState {
		let state: PreviousState = {
			filters: [],
			sorters: [],
			activeFilters: {},
		};

		const prevState =
			utils.getSortAndFilterFromStorage<PreviousState>("results-filters");

		if (prevState)
			state = {
				...prevState,
			};

		utils.removeStateFromStorage("results-filters");

		return state;
	}

	componentWillUnmount(): void {
		const settings: PreviousState = {
			sorters: this.state.sorters,
			activeFilters: this.state.activeFilters,
			filters: this.state.filters,
		};

		utils.saveSortAndFilterToStorage("results-filters", settings);
	}

	async getAll() {
		const sortString = utils.getSorterString(this.state.sorters);

		const [searchColumnString, keywordString] = utils.getFilterStrings([
			...this.state.filters,
		]);

		const filterData = {
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: keywordString,
			sorting: sortString,
			searchColumn: searchColumnString,
			startFirstReadingDate: this.state.activeFilters.firstReadingStart,
			endFirstReadingDate: this.state.activeFilters.firstReadingEnd,
			startCompletedDate: this.state.activeFilters.completedStart,
			endCompletedDate: this.state.activeFilters.completedEnd,
		};

		this.setState({ loading: true });
		await this.props.consumptionResultStore.getAll(filterData);
		this.setState({ loading: false });
	}

	handleTableChange = (
		pagination: TablePaginationConfig,
		_: Partial<Record<keyof ConsumptionResultDto, string[]>>,
		sorter:
			| SorterResult<ConsumptionResultDto>
			| SorterResult<ConsumptionResultDto>[],
	) => {
		const sorters = utils.getSorters(sorter);

		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
				sorters: sorters,
			},
			() => {
				this.getAll();
			},
		);
	};

	Modal = () => {
		this.setState({
			modalVisible: !this.state.modalVisible,
		});
	};

	delete(input: EntityDto) {
		const self = this;
		confirm({
			title: "Do you Want to delete these items?",
			onOk() {
				self.props.consumptionResultStore.delete(input);
			},
			onCancel() {
				//console.log('Cancel');
			},
		});
	}

	getColumnSearchProps = (
		dataIndex: string,
		displayName: string,
	): ColumnProps<ConsumptionResultDto> => ({
		filterDropdown: (props: FilterDropdownProps) => (
			<div>
				{props.visible && (
					<FilterSelect
						{...props}
						loading={this.state.fetchingFilters}
						handleFilter={(value) => this.handleFilter(value, dataIndex)}
						title={displayName}
						value={
							this.state.filters.find((x) => x.column === dataIndex)?.value
						}
						options={this.props.consumptionResultStore.filters?.map((x) => {
							return { key: x, value: x };
						})}
					/>
				)}
			</div>
		),
		onFilterDropdownVisibleChange: (v) => {
			if (v) this.fetchFilters(dataIndex);
		},
		filterIcon: () =>
			renderFilterIcon(
				this.state.filters.findIndex((x) => x.column === dataIndex) >= 0,
			),
		sortOrder: this.state.sorters.find((x) => x.columnKey === dataIndex)?.order,
	});

	private handleFilter(value: string, column: string) {
		let currentFilters = [...this.state.filters];
		currentFilters = currentFilters.filter((f) => f.column !== column);

		if (value) {
			currentFilters.push({ value, column: column });
		}

		this.setState({ filters: currentFilters }, async () => {
			await this.getAll();
		});
	}

	async fetchFilters(property: string) {
		this.setState({ fetchingFilters: true });
		const [searchColumnString, keywordString] = utils.getFilterStrings([
			...this.state.filters,
		]);

		try {
			await this.props.consumptionResultStore.getFilters(
				{
					keyword: keywordString,
					searchColumn: searchColumnString,
				},
				property,
			);
		} catch {
			Modal.error({
				title: "Error during fetching",
				content: (
					<div>
						<p>Failed to fetch filters for column</p>
					</div>
				),
			});
		}

		this.setState({ fetchingFilters: false });
	}

	public render() {
		const { consumptionResults } = this.props.consumptionResultStore;
		const paginationOptions = getTablePaginationOptions(
			consumptionResults?.totalCount,
		);

		const columns: Array<ColumnProps<ConsumptionResultDto>> = [
			{
				title: L("ConsumerName"),
				dataIndex: ["consumptionConsumer", "name"],
				key: "consumptionConsumer.name",
				sorter: { multiple: 8 },
				...this.getColumnSearchProps(
					"consumptionConsumer.name",
					L("ConsumerName"),
				),
				width: 250,
			},
			{
				title: L("Ship Name"),
				dataIndex: ["ship", "shipName"],
				key: "ship.shipName",
				sorter: { multiple: 7 },
				...this.getColumnSearchProps("ship.shipName", L("Ship Name")),
				width: 170,
			},
			{
				title: L("IMO"),
				dataIndex: ["ship", "imoNumber"],
				key: "ship.imoNumber",
				...this.getColumnSearchProps("ship.imoNumber", L("IMO")),
				width: 140,
				sorter: { multiple: 6 },
			},
			{
				title: L("First set of Meter Readings (Completed by OBU)"),
				dataIndex: "firstReadingDate",
				key: "firstReadingDate",
				width: 280,
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "firstReadingDate",
				)?.order,
				render: (text: string) =>
					renderDateFromUtcStringAndTimezone(text, "UTC"),
				sorter: { multiple: 5 },
				filterDropdown: ({
					confirm,
					setSelectedKeys,
					selectedKeys,
				}: FilterDropdownProps) => (
					<div style={{ padding: 8 }}>
						<DatePicker.RangePicker
							key={"firstReadingDate"}
							style={{ marginBottom: 8, width: 200 }}
							value={
								this.state.activeFilters.firstReadingStart
									? [
											moment(this.state.activeFilters.firstReadingStart) ||
												null,
											moment(this.state.activeFilters.firstReadingEnd) || null,
										]
									: null
							}
							onChange={(dates) => {
								const [startDate, endDate] = dates || [];
								const formattedDates = [
									startDate?.toISOString(),
									endDate?.toISOString(),
								];
								if (formattedDates[0] && formattedDates[1]) {
									setSelectedKeys([...selectedKeys, "firstReadingDate"]);
								} else {
									setSelectedKeys(
										selectedKeys.filter((x) => x !== "firstReadingDate"),
									);
								}
								this.setState(
									{
										activeFilters: {
											...this.state.activeFilters,
											firstReadingStart: formattedDates[0],
											firstReadingEnd: formattedDates[1],
										},
									},
									async () => await this.getAll(),
								);
								confirm?.();
							}}
						/>
					</div>
				),
				filterIcon: () =>
					renderFilterIcon(
						this.state.activeFilters.firstReadingStart !== undefined &&
							this.state.activeFilters.firstReadingEnd !== undefined,
					),
			},
			{
				title: L("Second set of Meter Readings (Completed by OBU)"),
				dataIndex: "lastReadingDate",
				key: "lastReadingDate",
				width: 300,
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "lastReadingDate",
				)?.order,
				render: (text: string) =>
					renderDateFromUtcStringAndTimezone(text, "UTC"),
				sorter: { multiple: 4 },
				filterDropdown: ({
					confirm,
					setSelectedKeys,
					selectedKeys,
				}: FilterDropdownProps) => (
					<div style={{ padding: 8 }}>
						<DatePicker.RangePicker
							key={"lastReadingDate"}
							style={{ marginBottom: 8, width: 200 }}
							value={
								this.state.activeFilters.completedStart
									? [
											moment(this.state.activeFilters.completedStart) || null,
											moment(this.state.activeFilters.completedEnd) || null,
										]
									: null
							}
							onChange={(dates) => {
								const [startDate, endDate] = dates || [];
								const formattedDates = [
									startDate?.toISOString(),
									endDate?.toISOString(),
								];
								if (formattedDates[0] && formattedDates[1]) {
									setSelectedKeys([...selectedKeys, "lastReadingDate"]);
								} else {
									setSelectedKeys(
										selectedKeys.filter((x) => x !== "lastReadingDate"),
									);
								}
								this.setState(
									{
										activeFilters: {
											...this.state.activeFilters,
											completedStart: formattedDates[0],
											completedEnd: formattedDates[1],
										},
									},
									async () => await this.getAll(),
								);
								confirm?.();
							}}
						/>
					</div>
				),
				filterIcon: () =>
					renderFilterIcon(
						this.state.activeFilters.completedStart !== undefined &&
							this.state.activeFilters.completedEnd !== undefined,
					),
			},
			{
				title: L("Vessel status at Second set of Meter Readings"),
				dataIndex: "reason",
				key: "reason",
				width: 200,
				sorter: { multiple: 3 },
				sortOrder: this.state.sorters.find((x) => x.columnKey === "reason")
					?.order,
			},
			{
				title: L("Duration (h)"),
				dataIndex: "duration",
				key: "duration",
				width: 200,
				sorter: { multiple: 2 },
				sortOrder: this.state.sorters.find((x) => x.columnKey === "duration")
					?.order,
			},

			{
				title: L("Fuel Type"),
				dataIndex: "fuelType",
				key: "fuelType",
				width: 200,
				sorter: { multiple: 2 },
				...this.getColumnSearchProps("fuelType", L("Fuel Type")),
				sortOrder: this.state.sorters.find((x) => x.columnKey === "fuelType")
					?.order,
			},
			{
				title: L("Consumption Result (Kg)"),
				dataIndex: "",
				key: "",
				width: 300,
				render: (text: string, item: ConsumptionResultDto) => (
					<>
						<div
							style={{
								display: "flex",
								justifyContent: "space-between",
								alignItems: "center",
							}}
						>
							<span style={{ textTransform: "uppercase" }}>
								From Last Reading
							</span>
							<div>
								{item.consumptionTotal
									? Number(item.consumptionTotal).toLocaleString("en-US", {
											maximumFractionDigits: 0,
											useGrouping: true,
										})
									: "-"}
							</div>
						</div>
						<div
							style={{
								display: "flex",
								justifyContent: "space-between",
								alignItems: "center",
							}}
						>
							<span style={{ textTransform: "uppercase" }}>Per 24H</span>
							<div>
								{item.consumption24h
									? Number(item.consumption24h).toLocaleString("en-US", {
											maximumFractionDigits: 0,
											useGrouping: true,
										})
									: "-"}
							</div>
						</div>
					</>
				),
			},
			{
				title: L("TimeOfCalculationUtc"),
				dataIndex: "creationTime",
				key: "creationTime",
				width: 220,
				sorter: { multiple: 1 },
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "creationTime",
				)?.order,
				render: (text: string, record: ConsumptionResultDto, index: number) =>
					record.isCalculatedCentrally
						? renderDateFromUtcStringAndTimezone(text, record.timezone)
						: "",
			},
		];

		return (
			<Card className="consumption-results">
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							className="customTableConsumptionResults"
							rowKey={(record) => record.id.toString()}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={consumptionResults === undefined || this.state.loading}
							dataSource={
								consumptionResults === undefined ? [] : consumptionResults.items
							}
							onChange={this.handleTableChange}
							scroll={{ y: 850 }}
						/>
					</Col>
				</Row>
				<Chat />
			</Card>
		);
	}
}

export default ConsumptionResult;
